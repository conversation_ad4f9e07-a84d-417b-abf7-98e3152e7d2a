<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();

            // معرف المستخدم
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // محتوى الإشعار
            $table->string('title');
            $table->text('message');
            $table->string('type', 50)->default('info');
            $table->json('data')->nullable(); // بيانات إضافية

            // حالة الإشعار
            $table->timestamp('read_at')->nullable();
            $table->string('action_url')->nullable();
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->timestamp('expires_at')->nullable();

            $table->timestamps();

            // فهارس لتحسين الأداء
            $table->index(['user_id'], 'notifications_user_index');
            $table->index(['user_id', 'read_at'], 'notifications_user_read_index');
            $table->index(['type'], 'notifications_type_index');
            $table->index(['priority'], 'notifications_priority_index');
            $table->index(['created_at'], 'notifications_created_at_index');
            $table->index(['expires_at'], 'notifications_expires_at_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
