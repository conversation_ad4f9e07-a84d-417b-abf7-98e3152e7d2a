<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

/**
 * أمر لتصفير ملفات الـ logs
 */
class ClearLogs extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'logs:clear 
                            {--all : Clear all log files}
                            {--backup : Create backup before clearing}';

    /**
     * وصف الأمر
     */
    protected $description = 'Clear Laravel log files';

    /**
     * تنفيذ الأمر
     */
    public function handle()
    {
        $logPath = storage_path('logs');
        
        // التحقق من وجود مجلد الـ logs
        if (!File::exists($logPath)) {
            $this->error('مجلد الـ logs غير موجود!');
            return 1;
        }

        // إنشاء نسخة احتياطية إذا طُلب ذلك
        if ($this->option('backup')) {
            $this->createBackup();
        }

        if ($this->option('all')) {
            // تصفير جميع ملفات الـ logs
            $this->clearAllLogs();
        } else {
            // تصفير ملف laravel.log فقط
            $this->clearLaravelLog();
        }

        return 0;
    }

    /**
     * تصفير ملف laravel.log
     */
    private function clearLaravelLog()
    {
        $logFile = storage_path('logs/laravel.log');
        
        if (File::exists($logFile)) {
            $sizeBefore = File::size($logFile);
            $sizeBeforeFormatted = $this->formatBytes($sizeBefore);
            
            // تصفير الملف
            File::put($logFile, '');
            
            $this->info("✅ تم تصفير ملف laravel.log بنجاح!");
            $this->line("📊 الحجم السابق: {$sizeBeforeFormatted}");
            $this->line("📊 الحجم الحالي: 0 bytes");
        } else {
            $this->warn('⚠️ ملف laravel.log غير موجود!');
        }
    }

    /**
     * تصفير جميع ملفات الـ logs
     */
    private function clearAllLogs()
    {
        $logPath = storage_path('logs');
        $logFiles = File::glob($logPath . '/*.log');
        
        if (empty($logFiles)) {
            $this->warn('⚠️ لا توجد ملفات logs للتصفير!');
            return;
        }

        $totalSizeBefore = 0;
        $clearedCount = 0;

        foreach ($logFiles as $logFile) {
            $sizeBefore = File::size($logFile);
            $totalSizeBefore += $sizeBefore;
            
            // تصفير الملف
            File::put($logFile, '');
            $clearedCount++;
            
            $fileName = basename($logFile);
            $this->line("✅ تم تصفير: {$fileName}");
        }

        $totalSizeFormatted = $this->formatBytes($totalSizeBefore);
        $this->info("🎉 تم تصفير {$clearedCount} ملف logs بنجاح!");
        $this->line("📊 إجمالي المساحة المحررة: {$totalSizeFormatted}");
    }

    /**
     * إنشاء نسخة احتياطية
     */
    private function createBackup()
    {
        $logFile = storage_path('logs/laravel.log');
        
        if (File::exists($logFile) && File::size($logFile) > 0) {
            $backupName = 'laravel_backup_' . date('Y-m-d_H-i-s') . '.log';
            $backupPath = storage_path('logs/' . $backupName);
            
            File::copy($logFile, $backupPath);
            $this->info("💾 تم إنشاء نسخة احتياطية: {$backupName}");
        }
    }

    /**
     * تنسيق حجم الملف
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
