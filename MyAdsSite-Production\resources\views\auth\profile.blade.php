@extends('layouts.app')

@section('title', __('Profile') . ' - ' . __('site_name'))
@section('description', __('Manage your account settings and personal information'))

@section('content')
<!-- القسم الرئيسي للملف الشخصي -->
<section class="profile-section py-5" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); min-height: 40vh;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white">
                <div class="profile-header mb-4">
                    <div class="profile-avatar mb-3">
                        <i class="fas fa-user-circle fa-5x"></i>
                    </div>
                    <h1 class="display-5 fw-bold arabic-text mb-2">
                        {{ __('My Profile') }}
                    </h1>
                    <p class="lead arabic-text mb-3">
                        {{ __('Manage your account settings and personal information') }}
                    </p>
                    <div class="profile-meta">
                        <span class="badge bg-light text-dark me-2">
                            <i class="fas fa-calendar me-1"></i>
                            {{ __('Member since') }}: {{ Auth::user()->created_at->format('Y/m/d') }}
                        </span>
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-envelope me-1"></i>
                            {{ Auth::user()->email }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الملف الشخصي -->
<section class="profile-content py-5">
    <div class="container">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-lg-3 mb-4">
                <div class="profile-sidebar">
                    <div class="sidebar-card">
                        <h5 class="arabic-text fw-bold mb-3">
                            <i class="fas fa-cog me-2 text-primary"></i>
                            {{ __('Account Settings') }}
                        </h5>
                        <ul class="profile-menu">
                            <li class="active">
                                <a href="#personal-info" class="arabic-text" onclick="showTab('personal-info')">
                                    <i class="fas fa-user me-2"></i>
                                    {{ __('Personal Information') }}
                                </a>
                            </li>
                            <li>
                                <a href="#payment-info" class="arabic-text" onclick="showTab('payment-info')">
                                    <i class="fas fa-credit-card me-2"></i>
                                    {{ __('Payment Information') }}
                                </a>
                            </li>
                            <li>
                                <a href="#security" class="arabic-text" onclick="showTab('security')">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    {{ __('Security') }}
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-lg-9">
                <!-- رسائل النجاح والأخطاء -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <span class="arabic-text">{{ session('success') }}</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li class="arabic-text">{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- نموذج تحديث الملف الشخصي -->
                <form method="POST" action="{{ route('auth.profile.update') }}" class="profile-form">
                    @csrf
                    @method('PUT')

                    <!-- المعلومات الشخصية -->
                    <div class="tab-content" id="personal-info">
                        <div class="profile-card">
                            <div class="card-header">
                                <h4 class="arabic-text fw-bold mb-0">
                                    <i class="fas fa-user me-2 text-primary"></i>
                                    {{ __('Personal Information') }}
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- الاسم الكامل -->
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-user me-2 text-primary"></i>
                                            {{ __('Full Name') }}
                                        </label>
                                        <input type="text" 
                                               class="form-control @error('name') is-invalid @enderror" 
                                               id="name" 
                                               name="name" 
                                               value="{{ old('name', Auth::user()->name) }}" 
                                               required>
                                        @error('name')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- البريد الإلكتروني -->
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-envelope me-2 text-primary"></i>
                                            {{ __('Email') }}
                                        </label>
                                        <input type="email" 
                                               class="form-control @error('email') is-invalid @enderror" 
                                               id="email" 
                                               name="email" 
                                               value="{{ old('email', Auth::user()->email) }}" 
                                               required>
                                        @error('email')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- العملة المفضلة -->
                                    <div class="col-12 mb-3">
                                        <label for="currency" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-coins me-2 text-primary"></i>
                                            {{ __('Preferred Currency') }}
                                        </label>
                                        <div class="currency-options">
                                            <div class="row g-2">
                                                <div class="col-md-4">
                                                    <div class="form-check currency-check">
                                                        <input class="form-check-input" type="radio" name="currency" id="currency_yer" value="YER" {{ Auth::user()->currency == 'YER' ? 'checked' : '' }}>
                                                        <label class="form-check-label arabic-text" for="currency_yer">
                                                            <i class="fas fa-coins text-warning me-2"></i>
                                                            {{ __('Yemeni Rial') }}
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-check currency-check">
                                                        <input class="form-check-input" type="radio" name="currency" id="currency_usd" value="USD" {{ Auth::user()->currency == 'USD' ? 'checked' : '' }}>
                                                        <label class="form-check-label arabic-text" for="currency_usd">
                                                            <i class="fas fa-dollar-sign text-success me-2"></i>
                                                            {{ __('US Dollar') }}
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-check currency-check">
                                                        <input class="form-check-input" type="radio" name="currency" id="currency_sar" value="SAR" {{ Auth::user()->currency == 'SAR' ? 'checked' : '' }}>
                                                        <label class="form-check-label arabic-text" for="currency_sar">
                                                            <i class="fas fa-coins text-info me-2"></i>
                                                            {{ __('Saudi Riyal') }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الدفع -->
                    <div class="tab-content" id="payment-info" style="display: none;">
                        <div class="profile-card">
                            <div class="card-header">
                                <h4 class="arabic-text fw-bold mb-0">
                                    <i class="fas fa-credit-card me-2 text-success"></i>
                                    {{ __('Payment Information') }}
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- رقم البطاقة -->
                                    <div class="col-md-6 mb-3">
                                        <label for="card_number" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-credit-card me-2 text-success"></i>
                                            {{ __('Card Number') }}
                                            <small class="text-muted">({{ __('Optional') }})</small>
                                        </label>
                                        <input type="text" 
                                               class="form-control @error('card_number') is-invalid @enderror" 
                                               id="card_number" 
                                               name="card_number" 
                                               value="{{ old('card_number', Auth::user()->card_number) }}" 
                                               placeholder="{{ __('Enter card number') }}">
                                        @error('card_number')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- نوع البطاقة -->
                                    <div class="col-md-6 mb-3">
                                        <label for="card_type" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-university me-2 text-success"></i>
                                            {{ __('Card Type') }}
                                            <small class="text-muted">({{ __('Optional') }})</small>
                                        </label>
                                        <select class="form-select @error('card_type') is-invalid @enderror" 
                                                id="card_type" 
                                                name="card_type">
                                            <option value="">{{ __('Select card type') }}</option>
                                            <option value="كاك بنك" {{ Auth::user()->card_type == 'كاك بنك' ? 'selected' : '' }}>{{ __('CAC Bank') }}</option>
                                            <option value="الكريمي" {{ Auth::user()->card_type == 'الكريمي' ? 'selected' : '' }}>{{ __('Al-Kuraimi Bank') }}</option>
                                            <option value="بنك اليمن الدولي" {{ Auth::user()->card_type == 'بنك اليمن الدولي' ? 'selected' : '' }}>{{ __('Yemen International Bank') }}</option>
                                            <option value="البنك الأهلي" {{ Auth::user()->card_type == 'البنك الأهلي' ? 'selected' : '' }}>{{ __('Al-Ahli Bank') }}</option>
                                            <option value="بنك التضامن" {{ Auth::user()->card_type == 'بنك التضامن' ? 'selected' : '' }}>{{ __('Tadhamon Bank') }}</option>
                                            <option value="أخرى" {{ Auth::user()->card_type == 'أخرى' ? 'selected' : '' }}>{{ __('Other') }}</option>
                                        </select>
                                        @error('card_type')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأمان -->
                    <div class="tab-content" id="security" style="display: none;">
                        <div class="profile-card">
                            <div class="card-header">
                                <h4 class="arabic-text fw-bold mb-0">
                                    <i class="fas fa-shield-alt me-2 text-warning"></i>
                                    {{ __('Security Settings') }}
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- كلمة المرور الحالية -->
                                    <div class="col-12 mb-3">
                                        <label for="current_password" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-lock me-2 text-warning"></i>
                                            {{ __('Current Password') }}
                                            <small class="text-muted">({{ __('Required to change password') }})</small>
                                        </label>
                                        <div class="password-input-group">
                                            <input type="password" 
                                                   class="form-control @error('current_password') is-invalid @enderror" 
                                                   id="current_password" 
                                                   name="current_password" 
                                                   placeholder="{{ __('Enter current password') }}">
                                            <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                                <i class="fas fa-eye" id="current_password-icon"></i>
                                            </button>
                                        </div>
                                        @error('current_password')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- كلمة المرور الجديدة -->
                                    <div class="col-md-6 mb-3">
                                        <label for="new_password" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-key me-2 text-warning"></i>
                                            {{ __('New Password') }}
                                        </label>
                                        <div class="password-input-group">
                                            <input type="password" 
                                                   class="form-control @error('new_password') is-invalid @enderror" 
                                                   id="new_password" 
                                                   name="new_password" 
                                                   placeholder="{{ __('Enter new password') }}">
                                            <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                                <i class="fas fa-eye" id="new_password-icon"></i>
                                            </button>
                                        </div>
                                        @error('new_password')
                                            <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- تأكيد كلمة المرور الجديدة -->
                                    <div class="col-md-6 mb-3">
                                        <label for="new_password_confirmation" class="form-label arabic-text fw-bold">
                                            <i class="fas fa-key me-2 text-warning"></i>
                                            {{ __('Confirm New Password') }}
                                        </label>
                                        <div class="password-input-group">
                                            <input type="password" 
                                                   class="form-control" 
                                                   id="new_password_confirmation" 
                                                   name="new_password_confirmation" 
                                                   placeholder="{{ __('Confirm new password') }}">
                                            <button type="button" class="password-toggle" onclick="togglePassword('new_password_confirmation')">
                                                <i class="fas fa-eye" id="new_password_confirmation-icon"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="profile-actions mt-4">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary btn-lg w-100 arabic-text fw-bold">
                                    <i class="fas fa-save me-2"></i>
                                    {{ __('Save Changes') }}
                                </button>
                            </div>
                            <div class="col-md-6">
                                <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-lg w-100 arabic-text">
                                    <i class="fas fa-home me-2"></i>
                                    {{ __('Back to Home') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* تنسيق القسم الرئيسي */
.profile-section {
    position: relative;
    overflow: hidden;
}

.profile-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.profile-avatar {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* الشريط الجانبي */
.profile-sidebar .sidebar-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 2rem;
}

.profile-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.profile-menu li {
    margin-bottom: 0.5rem;
}

.profile-menu a {
    display: block;
    padding: 0.75rem 1rem;
    color: #6c757d;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.profile-menu a:hover,
.profile-menu li.active a {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
    color: white;
    transform: translateX(5px);
}

/* بطاقات الملف الشخصي */
.profile-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    overflow: hidden;
}

.profile-card .card-header {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem;
}

.profile-card .card-body {
    padding: 2rem;
}

/* نموذج الملف الشخصي */
.profile-form .form-control,
.profile-form .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.profile-form .form-control:focus,
.profile-form .form-select:focus {
    border-color: #6f42c1;
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    transform: translateY(-2px);
}

/* خيارات العملة */
.currency-check {
    background: rgba(111, 66, 193, 0.1);
    border-radius: 10px;
    padding: 0.75rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.currency-check:hover {
    background: rgba(111, 66, 193, 0.2);
    transform: translateY(-2px);
}

.currency-check input:checked + label {
    color: #6f42c1;
    font-weight: bold;
}

.currency-check input:checked {
    background-color: #6f42c1;
    border-color: #6f42c1;
}

/* مجموعة كلمة المرور */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #6f42c1;
}

/* أزرار الإجراءات */
.profile-actions .btn {
    border-radius: 15px;
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.profile-actions .btn-primary {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
    border: none;
}

.profile-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(111, 66, 193, 0.3);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .profile-section {
        min-height: 30vh;
        padding: 3rem 0;
    }

    .profile-sidebar {
        margin-bottom: 2rem;
    }

    .profile-sidebar .sidebar-card {
        position: static;
    }

    .profile-menu {
        display: flex;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }

    .profile-menu li {
        flex-shrink: 0;
        margin-right: 0.5rem;
        margin-bottom: 0;
    }

    .profile-card .card-body {
        padding: 1.5rem;
    }

    .profile-actions .btn {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .profile-card .card-header,
    .profile-card .card-body {
        padding: 1rem;
    }

    .currency-check {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// دالة تبديل التبويبات
function showTab(tabId) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.style.display = 'none';
    });

    // إزالة الفئة النشطة من جميع عناصر القائمة
    document.querySelectorAll('.profile-menu li').forEach(item => {
        item.classList.remove('active');
    });

    // إظهار التبويب المحدد
    document.getElementById(tabId).style.display = 'block';

    // إضافة الفئة النشطة للعنصر المحدد
    event.target.closest('li').classList.add('active');
}

// دالة إظهار/إخفاء كلمة المرور
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '-icon');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق التأثير على العملة المحددة
    const checkedCurrency = document.querySelector('input[name="currency"]:checked');
    if (checkedCurrency) {
        const parentCheck = checkedCurrency.closest('.currency-check');
        parentCheck.style.borderColor = '#6f42c1';
        parentCheck.style.background = 'rgba(111, 66, 193, 0.2)';
    }

    // إضافة مستمعات للعملة
    const currencyInputs = document.querySelectorAll('input[name="currency"]');
    currencyInputs.forEach(input => {
        input.addEventListener('change', function() {
            // إزالة التأثير من جميع الخيارات
            document.querySelectorAll('.currency-check').forEach(check => {
                check.style.borderColor = 'transparent';
                check.style.background = 'rgba(111, 66, 193, 0.1)';
            });

            // إضافة التأثير للخيار المحدد
            if (this.checked) {
                const parentCheck = this.closest('.currency-check');
                parentCheck.style.borderColor = '#6f42c1';
                parentCheck.style.background = 'rgba(111, 66, 193, 0.2)';
            }
        });
    });

    // تحسين تجربة النماذج
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});
</script>
@endpush
