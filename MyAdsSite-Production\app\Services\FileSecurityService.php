<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * خدمة أمان الملفات
 * تدير فحص وحماية الملفات المرفوعة
 */
class FileSecurityService
{
    /**
     * أنواع الملفات المسموحة
     */
    const ALLOWED_IMAGE_TYPES = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp'
    ];

    /**
     * امتدادات الملفات المسموحة
     */
    const ALLOWED_EXTENSIONS = [
        'jpg', 'jpeg', 'png', 'gif', 'webp'
    ];

    /**
     * أنواع الملفات الخطيرة
     */
    const DANGEROUS_TYPES = [
        'application/x-php',
        'application/x-httpd-php',
        'text/x-php',
        'application/php',
        'application/x-sh',
        'application/x-csh',
        'text/x-script.sh',
        'application/x-executable',
        'application/x-msdownload',
        'application/x-msdos-program',
        'application/x-msi',
        'application/x-winexe',
        'application/x-winhlp',
        'application/x-winhelp',
        'application/octet-stream'
    ];

    /**
     * امتدادات خطيرة
     */
    const DANGEROUS_EXTENSIONS = [
        'php', 'php3', 'php4', 'php5', 'phtml', 'asp', 'aspx', 'jsp', 'jspx',
        'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar',
        'sh', 'bash', 'csh', 'ksh', 'fish', 'zsh', 'pl', 'py', 'rb',
        'htaccess', 'htpasswd', 'ini', 'cfg', 'conf'
    ];

    /**
     * الحد الأقصى لحجم الملف (بالبايت) - متوافق مع الاستضافة المشتركة
     */
    const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB للتوافق مع معظم مزودي الاستضافة

    /**
     * فحص أمان الملف المرفوع
     */
    public static function validateUploadedFile(UploadedFile $file): array
    {
        $errors = [];

        // فحص حجم الملف
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            $errors[] = 'حجم الملف يتجاوز الحد المسموح (2 ميجابايت)';
        }

        // فحص نوع الملف
        $mimeType = $file->getMimeType();
        if (!in_array($mimeType, self::ALLOWED_IMAGE_TYPES)) {
            $errors[] = 'نوع الملف غير مسموح';
        }

        // فحص امتداد الملف
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            $errors[] = 'امتداد الملف غير مسموح';
        }

        // فحص الملفات الخطيرة
        if (in_array($mimeType, self::DANGEROUS_TYPES) || 
            in_array($extension, self::DANGEROUS_EXTENSIONS)) {
            $errors[] = 'نوع الملف خطير وغير مسموح';
        }

        // فحص محتوى الملف
        $contentCheck = self::scanFileContent($file);
        if (!$contentCheck['safe']) {
            $errors = array_merge($errors, $contentCheck['errors']);
        }

        // فحص اسم الملف
        $nameCheck = self::validateFileName($file->getClientOriginalName());
        if (!$nameCheck['safe']) {
            $errors = array_merge($errors, $nameCheck['errors']);
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * فحص محتوى الملف
     */
    private static function scanFileContent(UploadedFile $file): array
    {
        $errors = [];
        
        try {
            // قراءة أول 1024 بايت من الملف
            $handle = fopen($file->getPathname(), 'rb');
            $content = fread($handle, 1024);
            fclose($handle);

            // فحص وجود كود PHP
            if (preg_match('/<\?php|<\?=|<script/i', $content)) {
                $errors[] = 'الملف يحتوي على كود قابل للتنفيذ';
            }

            // فحص وجود كود JavaScript مشبوه
            if (preg_match('/javascript:|vbscript:|data:/i', $content)) {
                $errors[] = 'الملف يحتوي على كود مشبوه';
            }

            // فحص وجود HTML مشبوه
            if (preg_match('/<iframe|<object|<embed|<form/i', $content)) {
                $errors[] = 'الملف يحتوي على عناصر HTML مشبوهة';
            }

            // التحقق من أن الملف صورة حقيقية
            if (!self::isValidImage($file)) {
                $errors[] = 'الملف ليس صورة صحيحة';
            }

        } catch (\Exception $e) {
            $errors[] = 'فشل في فحص محتوى الملف';
            Log::error('File content scan failed', [
                'file' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);
        }

        return [
            'safe' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * التحقق من أن الملف صورة صحيحة
     */
    private static function isValidImage(UploadedFile $file): bool
    {
        try {
            $imageInfo = getimagesize($file->getPathname());
            return $imageInfo !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * فحص اسم الملف
     */
    private static function validateFileName(string $filename): array
    {
        $errors = [];

        // فحص الأحرف المشبوهة
        if (preg_match('/[<>:"|?*\\\\\/]/', $filename)) {
            $errors[] = 'اسم الملف يحتوي على أحرف غير مسموحة';
        }

        // فحص الطول
        if (strlen($filename) > 255) {
            $errors[] = 'اسم الملف طويل جداً';
        }

        // فحص النقاط المتعددة (محاولة إخفاء الامتداد)
        if (substr_count($filename, '.') > 1) {
            $errors[] = 'اسم الملف يحتوي على نقاط متعددة';
        }

        // فحص الأسماء المحجوزة
        $reservedNames = ['con', 'prn', 'aux', 'nul', 'com1', 'com2', 'lpt1', 'lpt2'];
        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        if (in_array(strtolower($nameWithoutExt), $reservedNames)) {
            $errors[] = 'اسم الملف محجوز';
        }

        return [
            'safe' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * إنشاء اسم ملف آمن
     */
    public static function generateSecureFilename(UploadedFile $file, string $prefix = ''): string
    {
        $extension = strtolower($file->getClientOriginalExtension());
        
        // التأكد من أن الامتداد مسموح
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            $extension = 'jpg'; // امتداد افتراضي آمن
        }

        return $prefix . time() . '_' . Str::random(16) . '.' . $extension;
    }

    /**
     * حفظ الملف بشكل آمن
     */
    public static function secureStore(UploadedFile $file, string $directory = 'uploads'): array
    {
        try {
            // فحص الملف
            $validation = self::validateUploadedFile($file);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'errors' => $validation['errors']
                ];
            }

            // إنشاء اسم ملف آمن
            $filename = self::generateSecureFilename($file);
            
            // إنشاء مسار آمن
            $path = $directory . '/' . date('Y/m');
            
            // حفظ الملف
            $storedPath = $file->storeAs($path, $filename, 'public');

            // تسجيل العملية
            Log::info('File uploaded securely', [
                'original_name' => $file->getClientOriginalName(),
                'stored_path' => $storedPath,
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ]);

            return [
                'success' => true,
                'path' => $storedPath,
                'filename' => $filename,
                'size' => $file->getSize()
            ];

        } catch (\Exception $e) {
            Log::error('Secure file storage failed', [
                'file' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'errors' => ['فشل في حفظ الملف']
            ];
        }
    }

    /**
     * حذف الملف بشكل آمن
     */
    public static function secureDelete(string $path): bool
    {
        try {
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                
                Log::info('File deleted securely', ['path' => $path]);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('Secure file deletion failed', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * فحص صلاحيات المجلدات
     */
    public static function checkDirectoryPermissions(): array
    {
        $directories = [
            'storage/app/public',
            'storage/logs',
            'bootstrap/cache',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views'
        ];

        $results = [];

        foreach ($directories as $dir) {
            $fullPath = base_path($dir);
            $results[$dir] = [
                'exists' => is_dir($fullPath),
                'readable' => is_readable($fullPath),
                'writable' => is_writable($fullPath),
                'permissions' => is_dir($fullPath) ? substr(sprintf('%o', fileperms($fullPath)), -4) : null
            ];
        }

        return $results;
    }

    /**
     * فحص الملفات الحساسة
     */
    public static function checkSensitiveFiles(): array
    {
        $sensitiveFiles = [
            '.env',
            '.env.example',
            'composer.json',
            'composer.lock',
            'package.json',
            'artisan',
            'server.php',
            'phpunit.xml'
        ];

        $results = [];

        foreach ($sensitiveFiles as $file) {
            $fullPath = base_path($file);
            $webAccessible = self::isFileWebAccessible($file);

            $results[$file] = [
                'exists' => file_exists($fullPath),
                'web_accessible' => $webAccessible,
                'permissions' => file_exists($fullPath) ? substr(sprintf('%o', fileperms($fullPath)), -4) : null,
                'size' => file_exists($fullPath) ? filesize($fullPath) : null,
                'risk_level' => $webAccessible ? 'high' : 'low'
            ];
        }

        return $results;
    }

    /**
     * التحقق من إمكانية الوصول للملف عبر الويب
     */
    private static function isFileWebAccessible(string $filename): bool
    {
        try {
            $url = url($filename);
            $headers = @get_headers($url);

            if ($headers && strpos($headers[0], '200') !== false) {
                return true;
            }
        } catch (\Exception $e) {
            // تجاهل الأخطاء
        }

        return false;
    }

    /**
     * إنشاء ملف .htaccess لحماية المجلدات
     */
    public static function createProtectionFiles(): array
    {
        $protectedDirs = [
            'storage/app',
            'storage/logs',
            'bootstrap/cache'
        ];

        $results = [];
        $htaccessContent = "# حماية المجلد\nDeny from all\n<Files ~ \"\\.(jpg|jpeg|png|gif|webp)$\">\n    Allow from all\n</Files>";

        foreach ($protectedDirs as $dir) {
            $dirPath = base_path($dir);
            $htaccessPath = $dirPath . '/.htaccess';

            try {
                if (!is_dir($dirPath)) {
                    mkdir($dirPath, 0755, true);
                }

                if (!file_exists($htaccessPath)) {
                    file_put_contents($htaccessPath, $htaccessContent);
                    $results[$dir] = 'تم إنشاء ملف الحماية';
                } else {
                    $results[$dir] = 'ملف الحماية موجود بالفعل';
                }
            } catch (\Exception $e) {
                $results[$dir] = 'فشل في إنشاء ملف الحماية: ' . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * تنظيف الملفات المؤقتة القديمة
     */
    public static function cleanupOldFiles(int $days = 30): array
    {
        $directories = [
            'storage/app/temp',
            'storage/logs',
            'storage/framework/cache/data'
        ];

        $results = [];
        $cutoffTime = time() - ($days * 24 * 60 * 60);

        foreach ($directories as $dir) {
            $fullPath = base_path($dir);
            $deletedCount = 0;
            $errorCount = 0;

            if (is_dir($fullPath)) {
                $files = glob($fullPath . '/*');

                foreach ($files as $file) {
                    if (is_file($file) && filemtime($file) < $cutoffTime) {
                        try {
                            unlink($file);
                            $deletedCount++;
                        } catch (\Exception $e) {
                            $errorCount++;
                        }
                    }
                }
            }

            $results[$dir] = [
                'deleted' => $deletedCount,
                'errors' => $errorCount
            ];
        }

        return $results;
    }
}
