<?php

namespace App\Http\Controllers;

use App\Services\AdManagementService;
use App\Services\NotificationService;
use App\Services\CacheService;
use App\Models\Ad;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

/**
 * كونترولر لوحة تحكم المستخدم
 * يدير جميع وظائف لوحة التحكم الشخصية للمستخدم
 */
class UserDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * الصفحة الرئيسية للوحة التحكم
     */
    public function index()
    {
        $userId = auth()->id();

        // الحصول على إحصائيات المستخدم
        $stats = AdManagementService::getAdStats($userId);

        // الحصول على الإعلانات الحديثة
        $recentAds = auth()->user()->ads()
            ->with('category')
            ->latest()
            ->limit(5)
            ->get();

        // الحصول على الإشعارات الحديثة
        $notifications = NotificationService::getRecentForUser($userId, 5);

        // عدد الإشعارات غير المقروءة
        $unreadNotifications = NotificationService::getUnreadCount($userId);

        return view('dashboard.index', compact(
            'stats',
            'recentAds',
            'notifications',
            'unreadNotifications'
        ));
    }

    /**
     * صفحة إعلاناتي
     */
    public function myAds(Request $request)
    {
        $status = $request->get('status', 'all');
        $perPage = min($request->get('per_page', 12), 50);

        $query = auth()->user()->ads()->with('category');

        // فلتر حسب الحالة
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $ads = $query->latest()->paginate($perPage);

        // إحصائيات سريعة
        $stats = AdManagementService::getAdStats(auth()->id());

        return view('dashboard.my-ads', compact('ads', 'stats', 'status'));
    }

    /**
     * إنشاء إعلان جديد
     */
    public function createAd()
    {
        $categories = CacheService::getActiveCategories();

        return view('dashboard.create-ad', compact('categories'));
    }

    /**
     * حفظ إعلان جديد
     */
    public function storeAd(Request $request)
    {
        $request->validate([
            'title_ar' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'description_ar' => 'required|string|max:2000',
            'description_en' => 'nullable|string|max:2000',
            'category_id' => 'required|exists:categories,id',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'location' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'expires_at' => 'nullable|date|after:today'
        ]);

        $result = AdManagementService::createAd(
            $request->all(),
            $request->file('image')
        );

        if ($result['success']) {
            return redirect()->route('dashboard.ads.index')
                ->with('success', $result['message']);
        } else {
            return back()
                ->withInput()
                ->with('error', $result['message']);
        }
    }

    /**
     * تعديل إعلان
     */
    public function editAd(Ad $ad)
    {
        // التحقق من الصلاحيات باستخدام فحص محسن
        $this->authorizeAdAccess($ad, 'update');

        // فحص إضافي لحالة الإعلان
        if ($ad->status === 'rejected') {
            return redirect()->route('dashboard.ads.index')
                ->with('error', __('Cannot edit rejected ads. Please create a new one.'));
        }

        if ($ad->status === 'expired') {
            return redirect()->route('dashboard.ads.index')
                ->with('error', __('Cannot edit expired ads. Please create a new one.'));
        }

        $categories = CacheService::getActiveCategories();

        return view('dashboard.edit-ad', compact('ad', 'categories'));
    }

    /**
     * تحديث إعلان
     */
    public function updateAd(Request $request, Ad $ad)
    {
        // التحقق من الصلاحيات باستخدام فحص محسن
        $this->authorizeAdAccess($ad, 'update');

        $request->validate([
            'title_ar' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'description_ar' => 'required|string|max:2000',
            'description_en' => 'nullable|string|max:2000',
            'category_id' => 'required|exists:categories,id',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'location' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'expires_at' => 'nullable|date|after:today'
        ]);

        $result = AdManagementService::updateAd(
            $ad,
            $request->all(),
            $request->file('image')
        );

        if ($result['success']) {
            return redirect()->route('dashboard.ads.index')
                ->with('success', $result['message']);
        } else {
            return back()
                ->withInput()
                ->with('error', $result['message']);
        }
    }

    /**
     * حذف إعلان
     */
    public function deleteAd(Ad $ad): JsonResponse
    {
        try {
            // التحقق من الصلاحيات باستخدام فحص محسن
            $this->authorizeAdAccess($ad, 'delete');
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('Access denied. You can only delete your own ads.')
            ], 403);
        }

        $result = AdManagementService::deleteAd($ad);

        return response()->json($result);
    }

    /**
     * صفحة الإشعارات
     */
    public function notifications(Request $request)
    {
        $type = $request->get('type', 'all');
        $perPage = min($request->get('per_page', 15), 50);

        $query = auth()->user()->notifications();

        // فلتر حسب النوع
        if ($type !== 'all') {
            if ($type === 'unread') {
                $query->unread();
            } else {
                $query->ofType($type);
            }
        }

        $notifications = $query->latest()->paginate($perPage);

        // عدد الإشعارات غير المقروءة
        $unreadCount = NotificationService::getUnreadCount(auth()->id());

        return view('dashboard.notifications', compact('notifications', 'unreadCount', 'type'));
    }

    /**
     * عرض تفاصيل إشعار محدد
     */
    public function showNotification(int $notificationId)
    {
        $notification = auth()->user()->notifications()->findOrFail($notificationId);

        // وضع علامة مقروء تلقائياً عند عرض التفاصيل
        if (!$notification->isRead()) {
            $notification->markAsRead();
        }

        return view('dashboard.notification-details', compact('notification'));
    }

    /**
     * وضع علامة مقروء على إشعار
     */
    public function markNotificationAsRead(int $notificationId): JsonResponse
    {
        $result = NotificationService::markAsRead($notificationId, auth()->id());

        return response()->json([
            'success' => $result,
            'message' => $result ? 'تم وضع علامة مقروء' : 'فشل في العملية'
        ]);
    }

    /**
     * وضع علامة مقروء على جميع الإشعارات
     */
    public function markAllNotificationsAsRead(): JsonResponse
    {
        $result = NotificationService::markAllAsRead(auth()->id());

        return response()->json([
            'success' => $result,
            'message' => $result ? 'تم وضع علامة مقروء على جميع الإشعارات' : 'فشل في العملية'
        ]);
    }

    /**
     * حذف إشعار
     */
    public function deleteNotification(int $notificationId): JsonResponse
    {
        $result = NotificationService::deleteNotification($notificationId, auth()->id());

        return response()->json([
            'success' => $result,
            'message' => $result ? 'تم حذف الإشعار' : 'فشل في حذف الإشعار'
        ]);
    }

    /**
     * صفحة الملف الشخصي
     */
    public function profile()
    {
        $user = auth()->user();

        return view('dashboard.profile', compact('user'));
    }

    /**
     * تحديث الملف الشخصي
     */
    public function updateProfile(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . auth()->id(),
            'current_password' => 'nullable|string',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        $user = auth()->user();

        // التحقق من كلمة المرور الحالية إذا تم إدخال كلمة مرور جديدة
        if ($request->filled('password')) {
            if (!$request->filled('current_password') ||
                !Hash::check($request->current_password, (string)$user->password)) {
                return back()->with('error', 'كلمة المرور الحالية غير صحيحة');
            }
        }

        // تحديث البيانات
        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        // تسجيل تغيير البريد الإلكتروني (تم تعطيل SecurityMonitor لتبسيط الكود)
        if ($request->email !== $user->email) {
            // يمكن إضافة logging هنا إذا لزم الأمر
            Log::info('Email changed', ['user_id' => $user->id, 'old_email' => $user->email, 'new_email' => $request->email]);
        }

        $user->update($updateData);

        return back()->with('success', 'تم تحديث الملف الشخصي بنجاح');
    }

    /**
     * الحصول على الإشعارات الحديثة (AJAX)
     */
    public function getRecentNotifications(): JsonResponse
    {
        $notifications = NotificationService::getRecentForUser(auth()->id(), 5);
        $unreadCount = NotificationService::getUnreadCount(auth()->id());

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => $unreadCount
        ]);
    }

    /**
     * الحصول على إحصائيات سريعة (AJAX)
     */
    public function getQuickStats(): JsonResponse
    {
        $stats = AdManagementService::getAdStats(auth()->id());

        return response()->json($stats);
    }

    /**
     * التحقق من صلاحيات الوصول للإعلان
     *
     * @param Ad $ad الإعلان المراد فحص الصلاحيات عليه
     * @param string $action نوع العملية (view, update, delete)
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    private function authorizeAdAccess(Ad $ad, string $action): void
    {
        $user = auth()->user();

        // المدير يمكنه الوصول لجميع الإعلانات
        if ($user->is_admin) {
            return;
        }

        // التحقق من ملكية الإعلان
        if ($ad->user_id !== $user->id) {
            abort(403, __('Access denied. You can only :action your own ads.', ['action' => $action]));
        }

        // فحوصات إضافية حسب نوع العملية
        switch ($action) {
            case 'update':
                if (in_array($ad->status, ['rejected', 'expired'])) {
                    abort(403, __('Cannot edit ads with status: :status', ['status' => $ad->status]));
                }
                break;

            case 'delete':
                // يمكن حذف الإعلان في أي حالة إذا كان المستخدم يملكه
                break;

            case 'view':
                // يمكن عرض الإعلان إذا كان المستخدم يملكه
                break;
        }
    }
}
