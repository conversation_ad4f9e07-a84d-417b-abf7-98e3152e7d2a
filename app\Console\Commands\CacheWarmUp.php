<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheService;

/**
 * أمر تسخين الكاش
 * يقوم بتحميل البيانات المهمة في الكاش مسبقاً لتحسين الأداء
 */
class CacheWarmUp extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'cache:warm-up';

    /**
     * وصف الأمر
     */
    protected $description = 'تسخين الكاش بتحميل البيانات المهمة مسبقاً';

    /**
     * تنفيذ الأمر
     */
    public function handle()
    {
        $this->info('بدء تسخين الكاش...');

        try {
            // تسخين الكاش
            $result = CacheService::warmUpCache();

            if ($result) {
                $this->info('✅ تم تسخين الكاش بنجاح!');
                
                // عرض معلومات الكاش
                $this->showCacheInfo();
            } else {
                $this->error('❌ فشل في تسخين الكاش');
                return 1;
            }

        } catch (\Exception $e) {
            $this->error('❌ خطأ في تسخين الكاش: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * عرض معلومات الكاش
     */
    private function showCacheInfo()
    {
        $this->info('📊 معلومات الكاش:');
        
        $cacheInfo = CacheService::getCacheInfo();
        
        foreach ($cacheInfo as $key => $info) {
            $status = $info['exists'] ? '✅' : '❌';
            $size = $info['exists'] ? number_format($info['size']) . ' bytes' : 'غير موجود';
            
            $this->line("  {$status} {$key}: {$size}");
        }
    }
}
