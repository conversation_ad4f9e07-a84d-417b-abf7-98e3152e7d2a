<?php

namespace App\Services;

use App\Models\SecurityLog;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;

/**
 * خدمة مراقبة الأمان
 * تراقب الأنشطة المشبوهة وتسجل الأحداث الأمنية
 */
class SecurityMonitor
{
    /**
     * مراقبة محاولات تسجيل الدخول
     */
    public static function monitorLoginAttempt(string $email, bool $success, Request $request): void
    {
        // تسجيل محاولة تسجيل الدخول
        SecurityLog::logLoginAttempt($email, $success, [
            'path' => $request->path(),
            'method' => $request->method(),
            'referer' => $request->header('referer'),
        ]);

        // إذا فشل تسجيل الدخول، تحقق من النشاط المشبوه
        if (!$success) {
            self::checkSuspiciousLoginActivity($email, $request->ip());
        }
    }

    /**
     * فحص النشاط المشبوه في تسجيل الدخول
     */
    private static function checkSuspiciousLoginActivity(string $email, string $ip): void
    {
        $recentFailures = SecurityLog::where('action', SecurityLog::ACTION_LOGIN_FAILED)
            ->where('ip_address', $ip)
            ->where('created_at', '>=', now()->subHour())
            ->count();

        // إذا كان هناك أكثر من 5 محاولات فاشلة في الساعة الواحدة
        if ($recentFailures >= 5) {
            SecurityLog::logSuspiciousActivity(
                "محاولات تسجيل دخول متكررة فاشلة من IP: {$ip}",
                [
                    'email' => $email,
                    'failure_count' => $recentFailures,
                    'time_window' => '1 hour'
                ]
            );
        }
    }

    /**
     * مراقبة رفع الملفات
     */
    public static function monitorFileUpload(Request $request, $file, bool $success): void
    {
        $details = [
            'filename' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'extension' => $file->getClientOriginalExtension(),
            'success' => $success,
        ];

        // فحص الملفات المشبوهة
        if (self::isSuspiciousFile($file)) {
            SecurityLog::logSuspiciousActivity(
                "محاولة رفع ملف مشبوه: " . $file->getClientOriginalName(),
                $details
            );
        } else {
            SecurityLog::logEvent(
                SecurityLog::ACTION_FILE_UPLOAD_SUSPICIOUS,
                "رفع ملف: " . $file->getClientOriginalName(),
                SecurityLog::SEVERITY_LOW,
                $details
            );
        }
    }

    /**
     * فحص ما إذا كان الملف مشبوهاً
     */
    private static function isSuspiciousFile($file): bool
    {
        $suspiciousExtensions = [
            'php', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar'
        ];

        $extension = strtolower($file->getClientOriginalExtension());
        
        // فحص الامتداد
        if (in_array($extension, $suspiciousExtensions)) {
            return true;
        }

        // فحص حجم الملف (أكبر من 10 ميجا)
        if ($file->getSize() > 10 * 1024 * 1024) {
            return true;
        }

        // فحص نوع MIME
        $suspiciousMimes = [
            'application/x-executable',
            'application/x-msdownload',
            'application/x-php',
        ];

        if (in_array($file->getMimeType(), $suspiciousMimes)) {
            return true;
        }

        return false;
    }

    /**
     * مراقبة الوصول للصفحات الإدارية
     */
    public static function monitorAdminAccess(Request $request): void
    {
        SecurityLog::logEvent(
            SecurityLog::ACTION_ADMIN_ACCESS,
            "وصول إلى صفحة إدارية: " . $request->path(),
            SecurityLog::SEVERITY_MEDIUM,
            [
                'path' => $request->path(),
                'method' => $request->method(),
                'query_params' => $request->query(),
                'referer' => $request->header('referer'),
            ]
        );
    }

    /**
     * مراقبة تغيير كلمة المرور
     */
    public static function monitorPasswordChange(int $userId): void
    {
        SecurityLog::logEvent(
            SecurityLog::ACTION_PASSWORD_CHANGE,
            "تم تغيير كلمة المرور للمستخدم ID: {$userId}",
            SecurityLog::SEVERITY_MEDIUM,
            ['user_id' => $userId],
            $userId
        );
    }

    /**
     * مراقبة تغيير البريد الإلكتروني
     */
    public static function monitorEmailChange(int $userId, string $oldEmail, string $newEmail): void
    {
        SecurityLog::logEvent(
            SecurityLog::ACTION_EMAIL_CHANGE,
            "تم تغيير البريد الإلكتروني للمستخدم ID: {$userId}",
            SecurityLog::SEVERITY_HIGH,
            [
                'user_id' => $userId,
                'old_email' => $oldEmail,
                'new_email' => $newEmail,
            ],
            $userId
        );
    }

    /**
     * فحص محاولات SQL Injection
     */
    public static function checkSqlInjection(Request $request): void
    {
        $sqlPatterns = [
            '/(\bUNION\b.*\bSELECT\b)/i',
            '/(\bSELECT\b.*\bFROM\b.*\bWHERE\b)/i',
            '/(\bINSERT\b.*\bINTO\b)/i',
            '/(\bUPDATE\b.*\bSET\b)/i',
            '/(\bDELETE\b.*\bFROM\b)/i',
            '/(\bDROP\b.*\bTABLE\b)/i',
            '/(\'\s*OR\s*\'\s*=\s*\')/i',
            '/(\'\s*OR\s*1\s*=\s*1)/i',
        ];

        $allInput = array_merge($request->all(), [$request->getRequestUri()]);
        
        foreach ($allInput as $key => $value) {
            if (is_string($value)) {
                foreach ($sqlPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        SecurityLog::logSuspiciousActivity(
                            "محاولة SQL Injection محتملة",
                            [
                                'field' => $key,
                                'value' => $value,
                                'pattern' => $pattern,
                                'path' => $request->path(),
                            ]
                        );
                        break;
                    }
                }
            }
        }
    }

    /**
     * فحص محاولات XSS
     */
    public static function checkXssAttempt(Request $request): void
    {
        $xssPatterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe\b/i',
            '/<object\b/i',
            '/<embed\b/i',
            '/<form\b/i',
        ];

        $allInput = array_merge($request->all(), [$request->getRequestUri()]);
        
        foreach ($allInput as $key => $value) {
            if (is_string($value)) {
                foreach ($xssPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        SecurityLog::logSuspiciousActivity(
                            "محاولة XSS محتملة",
                            [
                                'field' => $key,
                                'value' => $value,
                                'pattern' => $pattern,
                                'path' => $request->path(),
                            ]
                        );
                        break;
                    }
                }
            }
        }
    }

    /**
     * الحصول على إحصائيات الأمان
     */
    public static function getSecurityStats(): array
    {
        return Cache::remember('security_stats', 300, function () {
            return [
                'total_events' => SecurityLog::count(),
                'critical_events' => SecurityLog::bySeverity(SecurityLog::SEVERITY_CRITICAL)->count(),
                'high_events' => SecurityLog::bySeverity(SecurityLog::SEVERITY_HIGH)->count(),
                'unresolved_events' => SecurityLog::unresolved()->count(),
                'recent_events' => SecurityLog::recent(24)->count(),
                'login_failures_today' => SecurityLog::byAction(SecurityLog::ACTION_LOGIN_FAILED)
                    ->whereDate('created_at', today())->count(),
                'rate_limit_exceeded_today' => SecurityLog::byAction(SecurityLog::ACTION_RATE_LIMIT_EXCEEDED)
                    ->whereDate('created_at', today())->count(),
            ];
        });
    }

    /**
     * تنظيف السجلات القديمة
     */
    public static function cleanOldLogs(int $daysToKeep = 90): int
    {
        $deletedCount = SecurityLog::where('created_at', '<', now()->subDays($daysToKeep))
            ->where('severity', '!=', SecurityLog::SEVERITY_CRITICAL)
            ->delete();

        Log::info("تم حذف {$deletedCount} سجل أمني قديم");
        
        return $deletedCount;
    }
}
