<?php $__env->startSection('title', __('My Favorites')); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4 favorites-page">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-heart text-danger me-2"></i>
                        <?php echo e(__('My Favorites')); ?>

                    </h1>
                    <p class="text-muted">
                        <?php echo e(__('Ads you added to your favorites list')); ?>

                    </p>
                </div>
                
                <!-- إحصائيات سريعة -->
                <div class="text-end">
                    <div class="badge bg-primary fs-6 px-3 py-2">
                        <i class="fas fa-heart me-1"></i>
                        <span class="favorites-count"><?php echo e($stats['active_favorites']); ?></span>
                        <?php echo e(__('favorite ads')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر وخيارات العرض -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body py-3">
                    <div class="row align-items-center">
                        <!-- خيارات العرض -->
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0"><?php echo e(__('Display')); ?>:</label>
                                <select class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                                    <option value="12" <?php echo e($perPage == 12 ? 'selected' : ''); ?>>12 <?php echo e(__('ads per page')); ?></option>
                                    <option value="24" <?php echo e($perPage == 24 ? 'selected' : ''); ?>>24 <?php echo e(__('ads per page')); ?></option>
                                    <option value="48" <?php echo e($perPage == 48 ? 'selected' : ''); ?>>48 <?php echo e(__('ads per page')); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="col-md-6 text-end">
                            <?php if($favorites->count() > 0): ?>
                                <a href="<?php echo e(route('ads.index')); ?>" class="btn btn-outline-primary btn-sm me-2">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    <?php echo e(__('browse_ads')); ?>

                                </a>
                                <button class="btn btn-outline-danger btn-sm" onclick="clearAllFavorites()">
                                    <i class="fas fa-trash me-1"></i>
                                    <?php echo e(__('Clear All')); ?>

                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الإعلانات المفضلة -->
    <?php if($favorites->count() > 0): ?>
        <div class="ads-grid" id="favorites-container">
            <div class="row" id="favorites-row">
                <?php $__currentLoopData = $favorites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $favorite): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php echo $__env->make('components.ad-card.index', [
                        'ad' => $favorite->ad,
                        'favorite' => $favorite,
                        'variant' => 'favorites',
                        'columns' => 4,                    // 4 أعمدة في الشاشات الكبيرة
                        'showFavorites' => false,
                        'descriptionLength' => 80,
                        'showPricing' => true,
                        'showMeta' => true,
                        'showActions' => true,
                        'showExpiry' => false,
                        'favoriteId' => $favorite->id,    // تمرير معرف المفضلة
                        'customAttributes' => [           // إضافة البيانات المطلوبة
                            'data-ad-id' => $favorite->ad->id,
                            'data-favorite-id' => $favorite->id,
                            'class' => 'favorite-card-wrapper'
                        ]
                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Pagination -->
        <?php if($favorites->hasPages()): ?>
            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex justify-content-center">
                        <?php echo e($favorites->appends(request()->query())->links('pagination.custom')); ?>

                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php else: ?>
        <!-- رسالة عدم وجود مفضلة -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="empty-state">
                        <i class="fas fa-heart-broken fa-5x text-muted mb-4"></i>
                        <h3 class="text-muted"><?php echo e(__('No favorites yet')); ?></h3>
                        <p class="text-muted mb-4">
                            <?php echo e(__('No favorites added yet')); ?>

                        </p>
                        <a href="<?php echo e(route('ads.index')); ?>" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            <?php echo e(__('Browse Ads')); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>

<link rel="stylesheet" href="<?php echo e(asset('css/ad-cards.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('css/page-specific.css')); ?>">
<style>
/* أنماط خاصة بصفحة المفضلة فقط - الحد الأدنى المطلوب */

/* تخصيص بسيط لصفحة المفضلة */
.favorites-page .ads-grid {
    margin-top: 2rem;
}

/* تحسين للموبايل فقط */
@media (max-width: 768px) {
    .favorites-page .ads-grid .row {
        margin: 0 -10px;
    }

    .favorites-page .ads-grid [class*="col-"] {
        padding: 0 10px;
    }
}

/* تم نقل معظم الأنماط إلى النظام الموحد في ad-cards.css */

/* أنماط خاصة بصفحة المفضلة فقط */
.favorites-page .loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.favorites-page .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تم نقل جميع الأنماط الأخرى إلى النظام الموحد */
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// ===== دوال صفحة المفضلة المحسنة =====

// تغيير عدد العناصر في الصفحة
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    window.location.href = url.toString();
}

// مسح جميع المفضلة - استخدام النظام الموحد الجديد
function clearAllFavorites() {
    if (window.FavoritesSystem && window.FavoritesSystem.clearAll) {
        window.FavoritesSystem.clearAll();
    } else {
        console.error('النظام الموحد للمفضلة غير متاح');
        showToast('<?php echo app('translator')->get('Unexpected error occurred'); ?>', 'error');
    }
}

// دالة مشاركة الإعلان
function shareAd(url, title) {
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        }).catch(console.error);
    } else {
        navigator.clipboard.writeText(url).then(function() {
            showToast('<?php echo app('translator')->get('Link copied to clipboard'); ?>', 'success');
        }).catch(function() {
            window.open('https://wa.me/?text=' + encodeURIComponent(title + ' - ' + url), '_blank');
        });
    }
}

// تحسينات JavaScript للمفضلة - النظام الجديد المحسن
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تهيئة صفحة المفضلة');

    // إضافة تأثير التحميل التدريجي للبطاقات
    const cards = document.querySelectorAll('.favorite-card-wrapper');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // انتظار تحميل النظام الموحد
    function waitForFavoritesSystem() {
        if (window.FavoritesSystem && window.FavoritesSystem.isInitialized()) {
            console.log('✅ النظام الموحد متاح ومهيأ');
            setupFavoritesPageHandlers();
        } else {
            console.log('⏳ انتظار تحميل النظام الموحد...');
            setTimeout(waitForFavoritesSystem, 100);
        }
    }

    waitForFavoritesSystem();
});

function setupFavoritesPageHandlers() {
    // إعادة تهيئة النظام للصفحة الحالية
    window.FavoritesSystem.reinitialize();

    // معالجة أزرار الإزالة المخصصة
    document.querySelectorAll('.btn-remove-favorite, .btn-favorite-remove').forEach(button => {
        if (button.hasAttribute('data-favorites-page-initialized')) {
            return;
        }

        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const wrapper = this.closest('.favorite-card-wrapper') || this.closest('[data-favorite-id]');
            const adId = this.dataset.adId || wrapper?.dataset.adId;
            const favoriteId = this.dataset.favoriteId || wrapper?.dataset.favoriteId;

            console.log('🗑️ إزالة المفضلة:', { adId, favoriteId });

            if (adId && favoriteId) {
                window.FavoritesSystem.removeFromPage(adId, favoriteId, this);
            } else {
                console.error('❌ بيانات الإعلان أو المفضلة مفقودة', { adId, favoriteId });
            }
        });

        button.setAttribute('data-favorites-page-initialized', 'true');
    });

    console.log('✅ تم تهيئة معالجات صفحة المفضلة');
}

    // تحسين تجربة المشاركة
    window.shareAd = function(url, title) {
        if (navigator.share) {
            navigator.share({
                title: title,
                url: url
            }).catch(console.error);
        } else {
            navigator.clipboard.writeText(url).then(function() {
                showToast('<?php echo app('translator')->get('Link copied to clipboard'); ?>', 'success');
            }).catch(function() {
                // فتح واتساب كبديل
                window.open('https://wa.me/?text=' + encodeURIComponent(title + ' - ' + url), '_blank');
            });
        }
    };

    // تم إزالة adjustGridLayout لأنه يتداخل مع Bootstrap Grid
    // نستخدم Bootstrap Grid Classes بدلاً من CSS Grid

    // تحسين تجربة التمرير
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });

    // مراقبة البطاقات للتأثير التدريجي
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>
<?php $__env->stopPush(); ?>


<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/favorites/index.blade.php ENDPATH**/ ?>