<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج التقييمات والمراجعات
 * يدير تقييمات المستخدمين للإعلانات والخدمات
 */
class Review extends Model
{
    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',          // معرف المستخدم الذي كتب التقييم
        'reviewable_id',    // معرف العنصر المُقيم
        'reviewable_type',  // نوع العنصر المُقيم (Ad, User, etc.)
        'rating',           // التقييم (1-5)
        'title',            // عنوان المراجعة
        'comment',          // نص المراجعة
        'is_approved',      // حالة الموافقة
        'is_verified',      // تقييم موثق (من عميل حقيقي)
        'helpful_count',    // عدد الأشخاص الذين وجدوا التقييم مفيداً
        'reported_count',   // عدد البلاغات
        'approved_at',      // تاريخ الموافقة
        'approved_by',      // من وافق على التقييم
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'is_approved' => 'boolean',
        'is_verified' => 'boolean',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * علاقة مع المستخدم الذي كتب التقييم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * علاقة مع المستخدم الذي وافق على التقييم
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * علاقة متعددة الأشكال مع العنصر المُقيم
     */
    public function reviewable()
    {
        return $this->morphTo();
    }

    /**
     * البحث في التقييمات المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * البحث في التقييمات المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }

    /**
     * البحث في التقييمات الموثقة
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * البحث حسب التقييم
     */
    public function scopeByRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * البحث في التقييمات عالية الجودة
     */
    public function scopeHighQuality($query)
    {
        return $query->where('rating', '>=', 4)
                    ->whereNotNull('comment')
                    ->where('comment', '!=', '');
    }

    /**
     * البحث في التقييمات الحديثة
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * ترتيب حسب الأكثر فائدة
     */
    public function scopeOrderByHelpful($query)
    {
        return $query->orderBy('helpful_count', 'desc');
    }

    /**
     * التحقق من أن التقييم معتمد
     */
    public function isApproved(): bool
    {
        return $this->is_approved;
    }

    /**
     * التحقق من أن التقييم موثق
     */
    public function isVerified(): bool
    {
        return $this->is_verified;
    }

    /**
     * التحقق من أن التقييم إيجابي
     */
    public function isPositive(): bool
    {
        return $this->rating >= 4;
    }

    /**
     * التحقق من أن التقييم سلبي
     */
    public function isNegative(): bool
    {
        return $this->rating <= 2;
    }

    /**
     * الحصول على نص التقييم
     */
    public function getRatingTextAttribute(): string
    {
        return match($this->rating) {
            5 => 'ممتاز',
            4 => 'جيد جداً',
            3 => 'جيد',
            2 => 'مقبول',
            1 => 'ضعيف',
            default => 'غير محدد'
        };
    }

    /**
     * الحصول على لون التقييم
     */
    public function getRatingColorAttribute(): string
    {
        return match($this->rating) {
            5 => 'success',
            4 => 'info',
            3 => 'warning',
            2 => 'orange',
            1 => 'danger',
            default => 'secondary'
        };
    }

    /**
     * الحصول على أيقونات النجوم
     */
    public function getStarsHtmlAttribute(): string
    {
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $stars .= '<i class="fas fa-star text-warning"></i>';
            } else {
                $stars .= '<i class="far fa-star text-muted"></i>';
            }
        }
        return $stars;
    }

    /**
     * اعتماد التقييم
     */
    public function approve(?int $approvedBy = null): bool
    {
        return $this->update([
            'is_approved' => true,
            'approved_at' => now(),
            'approved_by' => $approvedBy ?? auth()->id(),
        ]);
    }

    /**
     * رفض التقييم
     */
    public function reject(): bool
    {
        return $this->update([
            'is_approved' => false,
            'approved_at' => null,
            'approved_by' => null,
        ]);
    }

    /**
     * وضع علامة موثق
     */
    public function verify(): bool
    {
        return $this->update(['is_verified' => true]);
    }

    /**
     * إزالة علامة موثق
     */
    public function unverify(): bool
    {
        return $this->update(['is_verified' => false]);
    }

    /**
     * زيادة عداد الفائدة
     */
    public function incrementHelpful(): bool
    {
        return $this->increment('helpful_count');
    }

    /**
     * زيادة عداد البلاغات
     */
    public function incrementReported(): bool
    {
        return $this->increment('reported_count');
    }

    /**
     * إنشاء تقييم جديد
     */
    public static function createReview(
        int $userId,
        $reviewable,
        int $rating,
        ?string $title = null,
        ?string $comment = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'reviewable_id' => $reviewable->id,
            'reviewable_type' => get_class($reviewable),
            'rating' => $rating,
            'title' => $title,
            'comment' => $comment,
            'is_approved' => false, // يحتاج موافقة
        ]);
    }

    /**
     * الحصول على متوسط التقييمات لعنصر معين
     */
    public static function getAverageRating($reviewable): float
    {
        return self::where('reviewable_id', $reviewable->id)
            ->where('reviewable_type', get_class($reviewable))
            ->approved()
            ->avg('rating') ?? 0;
    }

    /**
     * الحصول على عدد التقييمات لعنصر معين
     */
    public static function getReviewsCount($reviewable): int
    {
        return self::where('reviewable_id', $reviewable->id)
            ->where('reviewable_type', get_class($reviewable))
            ->approved()
            ->count();
    }

    /**
     * الحصول على توزيع التقييمات (1-5 نجوم)
     */
    public static function getRatingDistribution($reviewable): array
    {
        $distribution = [];
        
        for ($i = 1; $i <= 5; $i++) {
            $count = self::where('reviewable_id', $reviewable->id)
                ->where('reviewable_type', get_class($reviewable))
                ->approved()
                ->where('rating', $i)
                ->count();
            
            $distribution[$i] = $count;
        }
        
        return $distribution;
    }

    /**
     * التحقق من وجود تقييم من مستخدم لعنصر معين
     */
    public static function hasUserReviewed(int $userId, $reviewable): bool
    {
        return self::where('user_id', $userId)
            ->where('reviewable_id', $reviewable->id)
            ->where('reviewable_type', get_class($reviewable))
            ->exists();
    }

    /**
     * الحصول على تقييم المستخدم لعنصر معين
     */
    public static function getUserReview(int $userId, $reviewable): ?self
    {
        return self::where('user_id', $userId)
            ->where('reviewable_id', $reviewable->id)
            ->where('reviewable_type', get_class($reviewable))
            ->first();
    }

    /**
     * تنظيف التقييمات القديمة غير المعتمدة
     */
    public static function cleanOldPendingReviews(int $daysToKeep = 30): int
    {
        return self::pending()
            ->where('created_at', '<', now()->subDays($daysToKeep))
            ->delete();
    }

    /**
     * الحصول على إحصائيات التقييمات
     */
    public static function getStats(): array
    {
        return [
            'total' => self::count(),
            'approved' => self::approved()->count(),
            'pending' => self::pending()->count(),
            'verified' => self::verified()->count(),
            'average_rating' => round(self::approved()->avg('rating') ?? 0, 2),
            'today' => self::whereDate('created_at', today())->count(),
            'this_week' => self::where('created_at', '>=', now()->startOfWeek())->count(),
        ];
    }
}
