-- ===================================================================
-- تحليل بيانات المستخدمين لحل مشكلة تسجيل الخروج التلقائي
-- تاريخ الإنشاء: 2025-01-09
-- الهدف: فحص المستخدمين المتأثرين بمشكلة CheckAccountSecurity
-- ===================================================================

-- 1. فحص المستخدمين بدون password_changed_at
-- هؤلاء المستخدمون سيعتبرون أن كلمة المرور منتهية الصلاحية
SELECT 
    'users_without_password_changed_at' as analysis_type,
    COUNT(*) as total_count
FROM users 
WHERE password_changed_at IS NULL;

-- تفاصيل المستخدمين بدون password_changed_at
SELECT 
    id,
    name,
    email,
    is_admin,
    created_at,
    password_changed_at,
    force_password_change,
    last_login_at,
    CASE 
        WHEN is_admin = 1 THEN 'مدير'
        ELSE 'مستخدم عادي'
    END as user_type
FROM users 
WHERE password_changed_at IS NULL
ORDER BY created_at DESC;

-- ===================================================================

-- 2. فحص المستخدمين بكلمات مرور منتهية الصلاحية
-- المديرين: 90 يوم، المستخدمين العاديين: 180 يوم
SELECT 
    'expired_passwords_summary' as analysis_type,
    SUM(CASE WHEN is_admin = 1 THEN 1 ELSE 0 END) as expired_admins,
    SUM(CASE WHEN is_admin = 0 THEN 1 ELSE 0 END) as expired_regular_users,
    COUNT(*) as total_expired
FROM users 
WHERE password_changed_at IS NOT NULL
  AND (
      (is_admin = 1 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 90 DAY))
      OR 
      (is_admin = 0 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 180 DAY))
  );

-- تفاصيل المستخدمين بكلمات مرور منتهية الصلاحية
SELECT 
    id,
    name,
    email,
    is_admin,
    password_changed_at,
    CASE 
        WHEN is_admin = 1 THEN DATE_ADD(password_changed_at, INTERVAL 90 DAY)
        ELSE DATE_ADD(password_changed_at, INTERVAL 180 DAY)
    END as password_expires_at,
    DATEDIFF(NOW(), 
        CASE 
            WHEN is_admin = 1 THEN DATE_ADD(password_changed_at, INTERVAL 90 DAY)
            ELSE DATE_ADD(password_changed_at, INTERVAL 180 DAY)
        END
    ) as days_expired,
    last_login_at,
    CASE 
        WHEN is_admin = 1 THEN 'مدير'
        ELSE 'مستخدم عادي'
    END as user_type
FROM users 
WHERE password_changed_at IS NOT NULL
  AND (
      (is_admin = 1 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 90 DAY))
      OR 
      (is_admin = 0 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 180 DAY))
  )
ORDER BY days_expired DESC;

-- ===================================================================

-- 3. فحص المستخدمين المقفلين حالياً
SELECT 
    'locked_users_summary' as analysis_type,
    COUNT(*) as total_locked_users
FROM users 
WHERE locked_until IS NOT NULL AND locked_until > NOW();

-- تفاصيل المستخدمين المقفلين
SELECT 
    id,
    name,
    email,
    is_admin,
    locked_until,
    failed_login_attempts,
    TIMESTAMPDIFF(MINUTE, NOW(), locked_until) as minutes_until_unlock,
    last_login_at,
    CASE 
        WHEN is_admin = 1 THEN 'مدير'
        ELSE 'مستخدم عادي'
    END as user_type
FROM users 
WHERE locked_until IS NOT NULL AND locked_until > NOW()
ORDER BY locked_until DESC;

-- ===================================================================

-- 4. فحص المستخدمين مع force_password_change = true
SELECT 
    'forced_password_change_summary' as analysis_type,
    COUNT(*) as total_forced_change
FROM users 
WHERE force_password_change = 1;

-- تفاصيل المستخدمين مع force_password_change
SELECT 
    id,
    name,
    email,
    is_admin,
    force_password_change,
    password_changed_at,
    last_login_at,
    CASE 
        WHEN is_admin = 1 THEN 'مدير'
        ELSE 'مستخدم عادي'
    END as user_type
FROM users 
WHERE force_password_change = 1
ORDER BY last_login_at DESC;

-- ===================================================================

-- 5. تحليل شامل للمستخدمين المتأثرين بـ CheckAccountSecurity
SELECT 
    'comprehensive_analysis' as analysis_type,
    COUNT(*) as total_users,
    SUM(CASE WHEN password_changed_at IS NULL THEN 1 ELSE 0 END) as users_without_password_date,
    SUM(CASE WHEN force_password_change = 1 THEN 1 ELSE 0 END) as users_forced_change,
    SUM(CASE WHEN locked_until IS NOT NULL AND locked_until > NOW() THEN 1 ELSE 0 END) as locked_users,
    SUM(CASE 
        WHEN password_changed_at IS NOT NULL AND (
            (is_admin = 1 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 90 DAY))
            OR 
            (is_admin = 0 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 180 DAY))
        ) THEN 1 ELSE 0 
    END) as expired_password_users,
    SUM(CASE 
        WHEN password_changed_at IS NULL 
        OR force_password_change = 1 
        OR (locked_until IS NOT NULL AND locked_until > NOW())
        OR (password_changed_at IS NOT NULL AND (
            (is_admin = 1 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 90 DAY))
            OR 
            (is_admin = 0 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 180 DAY))
        )) THEN 1 ELSE 0 
    END) as total_affected_users
FROM users;

-- ===================================================================

-- 6. المستخدمين الذين سيتأثرون بـ CheckAccountSecurity (سيتم تسجيل خروجهم)
SELECT 
    id,
    name,
    email,
    is_admin,
    password_changed_at,
    force_password_change,
    locked_until,
    failed_login_attempts,
    last_login_at,
    CASE 
        WHEN password_changed_at IS NULL THEN 'بدون تاريخ تغيير كلمة المرور'
        WHEN force_password_change = 1 THEN 'مطلوب تغيير كلمة المرور'
        WHEN locked_until IS NOT NULL AND locked_until > NOW() THEN 'حساب مقفل'
        WHEN is_admin = 1 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 90 DAY) THEN 'كلمة مرور منتهية الصلاحية (مدير)'
        WHEN is_admin = 0 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 180 DAY) THEN 'كلمة مرور منتهية الصلاحية (مستخدم)'
        ELSE 'غير متأثر'
    END as reason_for_logout,
    CASE 
        WHEN is_admin = 1 THEN 'مدير'
        ELSE 'مستخدم عادي'
    END as user_type
FROM users 
WHERE password_changed_at IS NULL 
   OR force_password_change = 1 
   OR (locked_until IS NOT NULL AND locked_until > NOW())
   OR (password_changed_at IS NOT NULL AND (
       (is_admin = 1 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 90 DAY))
       OR 
       (is_admin = 0 AND password_changed_at < DATE_SUB(NOW(), INTERVAL 180 DAY))
   ))
ORDER BY 
    CASE 
        WHEN locked_until IS NOT NULL AND locked_until > NOW() THEN 1
        WHEN force_password_change = 1 THEN 2
        WHEN password_changed_at IS NULL THEN 3
        ELSE 4
    END,
    last_login_at DESC;

-- ===================================================================

-- 7. إحصائيات إضافية مفيدة
SELECT 
    'additional_statistics' as analysis_type,
    COUNT(*) as total_users,
    SUM(CASE WHEN is_admin = 1 THEN 1 ELSE 0 END) as total_admins,
    SUM(CASE WHEN is_admin = 0 THEN 1 ELSE 0 END) as total_regular_users,
    SUM(CASE WHEN last_login_at IS NOT NULL THEN 1 ELSE 0 END) as users_who_logged_in,
    SUM(CASE WHEN last_login_at > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as active_last_30_days,
    SUM(CASE WHEN last_login_at > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as active_last_7_days
FROM users;

-- ===================================================================
-- نهاية الاستعلامات
-- ===================================================================
