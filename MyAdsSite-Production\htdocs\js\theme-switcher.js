/**
 * مبدل الوضع المظلم (Theme Switcher)
 * يدير تبديل الوضع بين الفاتح والمظلم مع حفظ التفضيل
 */

class ThemeSwitcher {
    constructor() {
        this.currentTheme = this.getStoredTheme() || this.getPreferredTheme();
        this.init();
    }

    /**
     * تهيئة مبدل الوضع
     */
    init() {
        // تطبيق الوضع المحفوظ
        this.setTheme(this.currentTheme);
        
        // إنشاء زر التبديل
        this.createToggleButton();
        
        // إضافة مستمعي الأحداث
        this.addEventListeners();
        
        // مراقبة تغيير تفضيلات النظام
        this.watchSystemTheme();
    }

    /**
     * الحصول على الوضع المحفوظ في localStorage
     */
    getStoredTheme() {
        return localStorage.getItem('theme');
    }

    /**
     * الحصول على الوضع المفضل من النظام
     */
    getPreferredTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }

    /**
     * تطبيق الوضع
     */
    setTheme(theme) {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        // تحديث أيقونة الزر
        this.updateToggleIcon();
        
        // إرسال حدث تغيير الوضع
        this.dispatchThemeChangeEvent();
    }

    /**
     * تبديل الوضع
     */
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
        
        // إضافة تأثير انتقال سلس
        this.addTransitionEffect();
    }

    /**
     * إنشاء زر التبديل
     */
    createToggleButton() {
        // التحقق من وجود الزر مسبقاً
        if (document.getElementById('theme-toggle')) {
            return;
        }

        const button = document.createElement('button');
        button.id = 'theme-toggle';
        button.className = 'theme-toggle';
        button.setAttribute('aria-label', 'تبديل الوضع المظلم');
        button.setAttribute('title', 'تبديل الوضع المظلم');
        
        // إضافة الأيقونة
        this.updateToggleIcon(button);
        
        // إضافة الزر إلى الصفحة
        document.body.appendChild(button);
        
        // إضافة مستمع النقر
        button.addEventListener('click', () => {
            this.toggleTheme();
        });
    }

    /**
     * تحديث أيقونة زر التبديل
     */
    updateToggleIcon(button = null) {
        const toggleButton = button || document.getElementById('theme-toggle');
        if (!toggleButton) return;

        const icon = this.currentTheme === 'light' ? '🌙' : '☀️';
        const title = this.currentTheme === 'light' ? 'Enable Dark Mode' : 'Enable Light Mode';
        
        toggleButton.innerHTML = icon;
        toggleButton.setAttribute('title', title);
        toggleButton.setAttribute('aria-label', title);
    }

    /**
     * إضافة مستمعي الأحداث
     */
    addEventListeners() {
        // مستمع لاختصار لوحة المفاتيح (Ctrl/Cmd + Shift + D)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.toggleTheme();
            }
        });

        // مستمع لتغيير حالة الصفحة
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // التحقق من تغيير تفضيلات النظام عند العودة للصفحة
                const systemTheme = this.getPreferredTheme();
                const storedTheme = this.getStoredTheme();
                
                if (!storedTheme && systemTheme !== this.currentTheme) {
                    this.setTheme(systemTheme);
                }
            }
        });
    }

    /**
     * مراقبة تغيير تفضيلات النظام
     */
    watchSystemTheme() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                // تطبيق تفضيل النظام فقط إذا لم يكن هناك تفضيل محفوظ
                if (!this.getStoredTheme()) {
                    const newTheme = e.matches ? 'dark' : 'light';
                    this.setTheme(newTheme);
                }
            });
        }
    }

    /**
     * إضافة تأثير انتقال سلس
     */
    addTransitionEffect() {
        document.documentElement.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        
        setTimeout(() => {
            document.documentElement.style.transition = '';
        }, 300);
    }

    /**
     * إرسال حدث تغيير الوضع
     */
    dispatchThemeChangeEvent() {
        const event = new CustomEvent('themechange', {
            detail: {
                theme: this.currentTheme,
                timestamp: new Date().toISOString()
            }
        });
        
        document.dispatchEvent(event);
    }

    /**
     * الحصول على الوضع الحالي
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * التحقق من كون الوضع مظلماً
     */
    isDarkMode() {
        return this.currentTheme === 'dark';
    }

    /**
     * إعادة تعيين الوضع إلى تفضيل النظام
     */
    resetToSystemTheme() {
        localStorage.removeItem('theme');
        const systemTheme = this.getPreferredTheme();
        this.setTheme(systemTheme);
    }

    /**
     * تطبيق وضع محدد
     */
    applyTheme(theme) {
        if (theme === 'light' || theme === 'dark') {
            this.setTheme(theme);
        }
    }
}

/**
 * وظائف مساعدة للوضع المظلم
 */
const ThemeUtils = {
    /**
     * تحديث لون meta theme-color
     */
    updateMetaThemeColor(theme) {
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            const color = theme === 'dark' ? '#1a1a1a' : '#ffffff';
            metaThemeColor.setAttribute('content', color);
        }
    },

    /**
     * تحديث favicon حسب الوضع
     */
    updateFavicon(theme) {
        const favicon = document.querySelector('link[rel="icon"]');
        if (favicon) {
            const iconPath = theme === 'dark' ? '/favicon-dark.ico' : '/favicon.ico';
            favicon.setAttribute('href', iconPath);
        }
    },

    /**
     * حفظ تفضيل الوضع في الخادم (اختياري)
     */
    async saveThemePreference(theme) {
        try {
            await fetch('/api/user/theme-preference', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify({ theme })
            });
        } catch (error) {
            console.warn('فشل في حفظ تفضيل الوضع:', error);
        }
    }
};

// تهيئة مبدل الوضع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.themeSwitcher = new ThemeSwitcher();
    
    // إضافة مستمع لأحداث تغيير الوضع
    document.addEventListener('themechange', (e) => {
        const { theme } = e.detail;
        
        // تحديث meta theme-color
        ThemeUtils.updateMetaThemeColor(theme);
        
        // تحديث favicon
        ThemeUtils.updateFavicon(theme);
        
        // حفظ التفضيل في الخادم (إذا كان المستخدم مسجل دخول)
        if (document.querySelector('meta[name="user-authenticated"]')) {
            ThemeUtils.saveThemePreference(theme);
        }
        
        console.log(`تم تغيير الوضع إلى: ${theme}`);
    });
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ThemeSwitcher, ThemeUtils };
}
