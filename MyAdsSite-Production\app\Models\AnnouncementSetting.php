<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * نموذج إعدادات الإعلانات
 * يدير إعدادات شريط الإعلانات المتحرك
 */
class AnnouncementSetting extends Model
{
    protected $fillable = [
        'is_enabled',
        'animation_type',
        'transition_duration',
        'scroll_speed'
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'transition_duration' => 'integer',
        'scroll_speed' => 'integer'
    ];

    /**
     * الحصول على الإعدادات الحالية أو إنشاء إعدادات افتراضية
     */
    public static function current()
    {
        $settings = self::first();

        if (!$settings) {
            $settings = self::create([
                'is_enabled' => true,
                'animation_type' => 'rotation',
                'transition_duration' => 4,
                'scroll_speed' => 20
            ]);
        }

        return $settings;
    }

    /**
     * تحديث الإعدادات
     */
    public static function updateSettings(array $data)
    {
        $settings = self::current();
        $settings->update($data);
        return $settings;
    }
}
