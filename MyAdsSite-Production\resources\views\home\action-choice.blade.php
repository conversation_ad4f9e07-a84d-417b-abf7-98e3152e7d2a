@extends('layouts.app')

@section('title', __('choose_action') . ' - ' . __('site_name'))
@section('description', __('what_would_you_like'))

@section('content')
<!-- القسم الرئيسي لاختيار الإجراء -->
<section class="action-choice-section py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 80vh;">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-70">
            <div class="col-lg-8 col-md-10">
                <!-- رأس الصفحة -->
                <div class="text-center text-white mb-5">
                    <h1 class="display-4 fw-bold arabic-text mb-3">
                        {{ __('choose_action') }}
                    </h1>
                    <p class="lead arabic-text mb-4">
                        {{ __('what_would_you_like') }}
                    </p>
                    
                    <!-- زر العودة للرئيسية -->
                    <a href="{{ route('home') }}" class="btn btn-outline-light btn-sm arabic-text mb-4">
                        <i class="fas fa-arrow-right me-2"></i>
                        {{ __('back_to_home') }}
                    </a>
                </div>

                <!-- بطاقات الاختيار -->
                <div class="row g-4">
                    <!-- بطاقة تصفح الإعلانات -->
                    <div class="col-lg-6">
                        <div class="action-card h-100">
                            <div class="card-body text-center p-5">
                                <!-- الأيقونة -->
                                <div class="action-icon mb-4">
                                    <i class="fas fa-search fa-5x text-primary"></i>
                                </div>
                                
                                <!-- العنوان والوصف -->
                                <h3 class="arabic-text fw-bold text-dark mb-3">
                                    {{ __('browse_ads') }}
                                </h3>
                                <p class="text-muted arabic-text mb-4">
                                    {{ __('browse_ads_desc') }}
                                </p>
                                
                                <!-- الزر -->
                                <a href="{{ route('categories.index') }}" class="btn btn-primary btn-lg px-5 py-3 arabic-text fw-bold action-btn">
                                    <i class="fas fa-th-large me-2"></i>
                                    {{ __('browse_categories') }}
                                </a>
                                
                                <!-- معلومات إضافية -->
                                <div class="action-features mt-4">
                                    <div class="row g-2">
                                        <div class="col-4">
                                            <div class="feature-mini text-center">
                                                <i class="fas fa-school text-info"></i>
                                                <small class="d-block arabic-text">{{ __('schools') }}</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="feature-mini text-center">
                                                <i class="fas fa-hospital text-danger"></i>
                                                <small class="d-block arabic-text">{{ __('hospitals') }}</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="feature-mini text-center">
                                                <i class="fas fa-building text-success"></i>
                                                <small class="d-block arabic-text">{{ __('companies') }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقة تسجيل الدخول -->
                    <div class="col-lg-6">
                        <div class="action-card h-100">
                            <div class="card-body text-center p-5">
                                <!-- الأيقونة -->
                                <div class="action-icon mb-4">
                                    <i class="fas fa-user-circle fa-5x text-success"></i>
                                </div>
                                
                                <!-- العنوان والوصف -->
                                <h3 class="arabic-text fw-bold text-dark mb-3">
                                    {{ __('login_register') }}
                                </h3>
                                <p class="text-muted arabic-text mb-4">
                                    {{ __('login_register_desc') }}
                                </p>
                                
                                <!-- الأزرار -->
                                <div class="action-buttons">
                                    <a href="{{ route('login') }}" class="btn btn-success btn-lg px-4 py-3 arabic-text fw-bold action-btn mb-2">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        {{ __('Login') }}
                                    </a>
                                    <br>
                                    <a href="{{ route('auth.register') }}" class="btn btn-outline-success btn-lg px-4 py-3 arabic-text fw-bold action-btn">
                                        <i class="fas fa-user-plus me-2"></i>
                                        {{ __('Register') }}
                                    </a>
                                </div>
                                
                                <!-- معلومات إضافية -->
                                <div class="action-features mt-4">
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <div class="feature-mini text-center">
                                                <i class="fas fa-plus-circle text-warning"></i>
                                                <small class="d-block arabic-text">{{ __('register_free') }}</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="feature-mini text-center">
                                                <i class="fas fa-shield-alt text-info"></i>
                                                <small class="d-block arabic-text">{{ __('trusted_secure') }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="text-center mt-5">
                    <div class="info-section">
                        <h5 class="text-white arabic-text mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ __('why_our_site') }}
                        </h5>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="info-item">
                                    <i class="fas fa-search text-warning"></i>
                                    <small class="text-white arabic-text">{{ __('easy_fast_search') }}</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <i class="fas fa-shield-alt text-warning"></i>
                                    <small class="text-white arabic-text">{{ __('trusted_secure') }}</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <i class="fas fa-mobile-alt text-warning"></i>
                                    <small class="text-white arabic-text">{{ __('mobile_compatible') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* تنسيق القسم الرئيسي */
.action-choice-section {
    position: relative;
    overflow: hidden;
}

.action-choice-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* بطاقات الاختيار */
.action-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.action-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.action-card:hover::before {
    left: 100%;
}

/* أيقونات الإجراءات */
.action-icon {
    position: relative;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* أزرار الإجراءات */
.action-btn {
    border-radius: 50px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* الميزات الصغيرة */
.feature-mini {
    padding: 0.5rem;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.feature-mini:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

.feature-mini i {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

/* قسم المعلومات */
.info-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.info-item i {
    font-size: 1.5rem;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .action-choice-section {
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .action-card .card-body {
        padding: 2rem 1.5rem;
    }
    
    .action-icon i {
        font-size: 3rem;
    }
    
    .action-btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .info-section {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .action-card .card-body {
        padding: 1.5rem 1rem;
    }
    
    .feature-mini {
        padding: 0.25rem;
    }
    
    .feature-mini i {
        font-size: 1rem;
    }
    
    .feature-mini small {
        font-size: 0.7rem;
    }
}
</style>
@endpush
