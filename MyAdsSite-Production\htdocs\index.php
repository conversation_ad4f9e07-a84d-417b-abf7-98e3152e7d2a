<?php

use Illuminate\Http\Request;

define('LARAVEL_START', microtime(true));

// Determine if the application is in maintenance mode...
if (file_exists($maintenance = __DIR__.'/../storage/framework/maintenance.php')) {
    require $maintenance;
}

// Register the Composer autoloader...
if (file_exists(__DIR__.'/../vendor/autoload.php')) {
    require __DIR__.'/../vendor/autoload.php';
} else {
    die('❌ ملف vendor/autoload.php غير موجود. يرجى تشغيل install_composer.php أولاً.');
}

// Bootstrap Laravel and handle the request...
if (file_exists(__DIR__.'/../bootstrap/app.php')) {
    try {
        (require_once __DIR__.'/../bootstrap/app.php')
            ->handleRequest(Request::capture());
    } catch (Exception $e) {
        echo '<h1>خطأ في Laravel</h1>';
        echo '<p>الرسالة: ' . $e->getMessage() . '</p>';
        echo '<p>الملف: ' . $e->getFile() . '</p>';
        echo '<p>السطر: ' . $e->getLine() . '</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
        echo '<br><a href="setup.php">تشغيل الإعداد الأولي</a>';
    }
} else {
    die('❌ ملف bootstrap/app.php غير موجود. تأكد من رفع جميع ملفات Laravel.');
}
