<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

/**
 * نموذج التقييمات
 * يحتوي على تقييمات المستخدمين للإعلانات
 */
class Rating extends Model
{
    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',
        'ad_id',
        'rating',
        'comment',
        'is_verified',
        'is_helpful',
        'helpful_count',
        'quality_rating',
        'price_rating',
        'service_rating',
        'interaction_data',
        'last_updated_at',
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'user_id' => 'integer',
        'ad_id' => 'integer',
        'rating' => 'integer',
        'is_verified' => 'boolean',
        'is_helpful' => 'boolean',
        'helpful_count' => 'integer',
        'interaction_data' => 'array',
        'last_updated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * القيم الافتراضية للحقول
     */
    protected $attributes = [
        'is_verified' => false,
        'is_helpful' => false,
        'helpful_count' => 0,
    ];

    /**
     * علاقة متعدد إلى واحد مع المستخدمين
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * علاقة متعدد إلى واحد مع الإعلانات
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * البحث في تقييمات مستخدم معين
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * البحث في تقييمات إعلان معين
     */
    public function scopeByAd(Builder $query, int $adId): Builder
    {
        return $query->where('ad_id', $adId);
    }

    /**
     * البحث في التقييمات حسب النجوم
     */
    public function scopeByRating(Builder $query, int $rating): Builder
    {
        return $query->where('rating', $rating);
    }

    /**
     * البحث في التقييمات الموثقة
     */
    public function scopeVerified(Builder $query): Builder
    {
        return $query->where('is_verified', true);
    }

    /**
     * البحث في التقييمات المفيدة
     */
    public function scopeHelpful(Builder $query): Builder
    {
        return $query->where('is_helpful', true);
    }

    /**
     * ترتيب حسب الأحدث
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * ترتيب حسب الأقدم
     */
    public function scopeOldest(Builder $query): Builder
    {
        return $query->orderBy('created_at', 'asc');
    }

    /**
     * ترتيب حسب التقييم (الأعلى أولاً)
     */
    public function scopeHighestRated(Builder $query): Builder
    {
        return $query->orderBy('rating', 'desc');
    }

    /**
     * ترتيب حسب التقييم (الأقل أولاً)
     */
    public function scopeLowestRated(Builder $query): Builder
    {
        return $query->orderBy('rating', 'asc');
    }

    /**
     * ترتيب حسب الأكثر فائدة
     */
    public function scopeMostHelpful(Builder $query): Builder
    {
        return $query->orderBy('helpful_count', 'desc');
    }

    /**
     * التحقق من وجود تقييم من مستخدم لإعلان معين
     */
    public static function hasUserRated(int $userId, int $adId): bool
    {
        return static::where('user_id', $userId)
                    ->where('ad_id', $adId)
                    ->exists();
    }

    /**
     * الحصول على تقييم مستخدم لإعلان معين
     */
    public static function getUserRating(int $userId, int $adId): ?self
    {
        return static::where('user_id', $userId)
                    ->where('ad_id', $adId)
                    ->first();
    }

    /**
     * إضافة أو تحديث تقييم
     */
    public static function addOrUpdateRating(int $userId, int $adId, array $data): self
    {
        return static::updateOrCreate(
            ['user_id' => $userId, 'ad_id' => $adId],
            array_merge($data, ['last_updated_at' => now()])
        );
    }

    /**
     * حساب متوسط التقييم لإعلان معين
     */
    public static function getAverageRating(int $adId): float
    {
        $cacheKey = "ad_rating_average_{$adId}";

        return Cache::remember($cacheKey, 3600, function () use ($adId) {
            return static::where('ad_id', $adId)->avg('rating') ?: 0;
        });
    }

    /**
     * الحصول على عدد التقييمات لإعلان معين
     */
    public static function getRatingsCount(int $adId): int
    {
        $cacheKey = "ad_rating_count_{$adId}";

        return Cache::remember($cacheKey, 3600, function () use ($adId) {
            return static::where('ad_id', $adId)->count();
        });
    }

    /**
     * الحصول على توزيع التقييمات لإعلان معين
     */
    public static function getRatingDistribution(int $adId): array
    {
        $cacheKey = "ad_rating_distribution_{$adId}";

        return Cache::remember($cacheKey, 3600, function () use ($adId) {
            $distribution = [];
            for ($i = 1; $i <= 5; $i++) {
                $distribution[$i] = static::where('ad_id', $adId)
                                         ->where('rating', $i)
                                         ->count();
            }
            return $distribution;
        });
    }

    /**
     * الحصول على إحصائيات شاملة لإعلان معين
     */
    public static function getAdRatingStats(int $adId): array
    {
        return [
            'average' => static::getAverageRating($adId),
            'count' => static::getRatingsCount($adId),
            'distribution' => static::getRatingDistribution($adId),
        ];
    }

    /**
     * مسح الكاش عند إضافة أو تحديث تقييم
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($rating) {
            Cache::forget("ad_rating_average_{$rating->ad_id}");
            Cache::forget("ad_rating_count_{$rating->ad_id}");
            Cache::forget("ad_rating_distribution_{$rating->ad_id}");
        });

        static::deleted(function ($rating) {
            Cache::forget("ad_rating_average_{$rating->ad_id}");
            Cache::forget("ad_rating_count_{$rating->ad_id}");
            Cache::forget("ad_rating_distribution_{$rating->ad_id}");
        });
    }

    /**
     * الحصول على نص التقييم
     */
    public function getRatingTextAttribute(): string
    {
        return match($this->rating) {
            5 => 'ممتاز',
            4 => 'جيد جداً',
            3 => 'جيد',
            2 => 'مقبول',
            1 => 'ضعيف',
            default => 'غير محدد'
        };
    }

    /**
     * الحصول على لون التقييم
     */
    public function getRatingColorAttribute(): string
    {
        return match($this->rating) {
            5 => 'success',
            4 => 'info',
            3 => 'warning',
            2 => 'orange',
            1 => 'danger',
            default => 'secondary'
        };
    }
}
