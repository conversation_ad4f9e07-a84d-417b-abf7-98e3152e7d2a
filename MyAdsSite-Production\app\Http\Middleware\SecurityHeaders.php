<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware لإضافة Security Headers
 * يضيف headers أمنية لحماية التطبيق من الهجمات الشائعة
 */
class SecurityHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // التحقق من تفعيل Security Headers
        if (!config('myadssite.security.enable_security_headers', true)) {
            return $response;
        }

        // منع تحديد نوع المحتوى تلقائياً (MIME Type Sniffing)
        $response->headers->set('X-Content-Type-Options', 'nosniff');

        // منع تضمين الصفحة في iframe (Clickjacking Protection)
        // استخدام SAMEORIGIN بدلاً من DENY للتوافق مع الاستضافة
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');

        // تفعيل حماية XSS في المتصفحات القديمة
        $response->headers->set('X-XSS-Protection', '1; mode=block');

        // منع تنزيل الملفات التنفيذية
        $response->headers->set('X-Download-Options', 'noopen');

        // منع تخمين نوع المحتوى
        $response->headers->set('X-Permitted-Cross-Domain-Policies', 'none');

        // فرض استخدام HTTPS (فقط إذا كان HTTPS مفعل فعلياً)
        // تجنب فرض HTTPS في الاستضافة المشتركة التي قد لا تدعمه
        if ($request->isSecure()) {
            $response->headers->set(
                'Strict-Transport-Security',
                'max-age=31536000; includeSubDomains'
            );
        }

        // سياسة أمان المحتوى (Content Security Policy)
        $csp = $this->getContentSecurityPolicy($request);
        $response->headers->set('Content-Security-Policy', $csp);

        // CSP Report-Only للاختبار (في بيئة التطوير)
        if (config('app.debug')) {
            $response->headers->set('Content-Security-Policy-Report-Only', $csp);
        }

        // سياسة الإحالة (Referrer Policy)
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // سياسة الأذونات (Permissions Policy)
        $permissionsPolicy = $this->getPermissionsPolicy();
        $response->headers->set('Permissions-Policy', $permissionsPolicy);

        // ملاحظة: تم نقل إدارة Cache headers إلى CacheHeaders middleware
        // لتجنب التضارب والتكرار

        // إضافة header مخصص لتحديد إصدار التطبيق (للمراقبة)
        if (config('app.debug')) {
            $response->headers->set('X-App-Version', config('app.version', '1.0.0'));
        }

        // إزالة headers تكشف معلومات الخادم
        $response->headers->remove('Server');
        $response->headers->remove('X-Powered-By');
        $response->headers->remove('X-AspNet-Version');
        $response->headers->remove('X-AspNetMvc-Version');

        // إضافة header للحماية من DNS Rebinding
        $response->headers->set('X-DNS-Prefetch-Control', 'off');

        return $response;
    }

    /**
     * إنشاء سياسة أمان المحتوى (مبسطة للتوافق مع الاستضافة)
     */
    private function getContentSecurityPolicy(Request $request): string
    {
        // CSP محسن ومتوافق مع جميع مزودي الاستضافة - إصلاح شامل لمشاكل الخطوط والمصادر
        $policies = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com https://stackpath.bootstrapcdn.com https://unpkg.com https://fonts.googleapis.com https://maxcdn.bootstrapcdn.com",
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com https://fonts.gstatic.com https://stackpath.bootstrapcdn.com https://use.fontawesome.com https://maxcdn.bootstrapcdn.com",
            "font-src 'self' data: https://fonts.gstatic.com https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com https://stackpath.bootstrapcdn.com https://maxcdn.bootstrapcdn.com https://ka-f.fontawesome.com",
            "img-src 'self' data: blob: https: http:",
            "connect-src 'self' https: wss: ws:",
            "media-src 'self' data: blob:",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'",
            "frame-ancestors 'none'",
            "worker-src 'none'",
            "manifest-src 'self'",
        ];

        return implode('; ', $policies);
    }

    /**
     * إنشاء سياسة الأذونات (محدثة للمتصفحات الحديثة)
     */
    private function getPermissionsPolicy(): string
    {
        // استخدام الميزات المدعومة فقط في المتصفحات الحديثة
        $policies = [
            'accelerometer=()',
            'autoplay=()',
            'camera=()',
            'display-capture=()',
            'encrypted-media=()',
            'fullscreen=(self)',
            'geolocation=()',
            'gyroscope=()',
            'magnetometer=()',
            'microphone=()',
            'midi=()',
            'payment=()',
            'picture-in-picture=()',
            'publickey-credentials-get=()',
            'screen-wake-lock=()',
            'usb=()',
            'web-share=()',
            'xr-spatial-tracking=()',
        ];

        return implode(', ', $policies);
    }

    /**
     * تحديد ما إذا كانت الصفحة خاصة (تحتاج حماية إضافية)
     */
    private function isPrivatePage(Request $request): bool
    {
        $privatePaths = [
            '/admin',
            '/dashboard',
            '/profile',
            '/my-ads',
            '/login',
            '/register',
            '/auth',
            '/api',
            '/settings',
            '/notifications',
        ];

        $currentPath = $request->getPathInfo();

        // التحقق من المسارات الخاصة
        foreach ($privatePaths as $path) {
            if (str_starts_with($currentPath, $path)) {
                return true;
            }
        }

        // التحقق من المسارات التي تحتوي على معرفات حساسة
        if (preg_match('/\/(edit|update|delete|create)/', $currentPath)) {
            return true;
        }

        // التحقق من وجود معاملات حساسة
        if ($request->has(['password', 'token', 'key', 'secret'])) {
            return true;
        }

        // التحقق من طرق HTTP الحساسة
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            return true;
        }

        return false;
    }
}
