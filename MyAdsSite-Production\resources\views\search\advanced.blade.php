@extends('layouts.app')

@section('title', 'البحث المتقدم')

@section('content')
<div class="container py-4">
    <div class="row">
        <!-- فلاتر البحث المتقدم -->
        <div class="col-lg-3">
            <div class="card border-0 shadow-sm sticky-top" style="top: 100px;">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث المتقدم
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('search.advanced') }}" id="advancedSearchForm">
                        <!-- التصنيفات -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">التصنيفات</label>
                            @foreach($categories as $category)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           name="categories[]" value="{{ $category->id }}"
                                           id="category_{{ $category->id }}"
                                           {{ in_array($category->id, $filters['categories'] ?? []) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="category_{{ $category->id }}">
                                        {{ $category->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>

                        <!-- المواقع -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">المواقع الشائعة</label>
                            @foreach($locations as $location)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           name="locations[]" value="{{ $location }}"
                                           id="location_{{ md5($location) }}"
                                           {{ in_array($location, $filters['locations'] ?? []) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="location_{{ md5($location) }}">
                                        {{ $location }}
                                    </label>
                                </div>
                            @endforeach
                        </div>

                        <!-- نطاق التاريخ -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">تاريخ النشر</label>
                            <select name="date_range" class="form-select">
                                <option value="">جميع التواريخ</option>
                                <option value="today" {{ ($filters['date_range'] ?? '') == 'today' ? 'selected' : '' }}>اليوم</option>
                                <option value="week" {{ ($filters['date_range'] ?? '') == 'week' ? 'selected' : '' }}>هذا الأسبوع</option>
                                <option value="month" {{ ($filters['date_range'] ?? '') == 'month' ? 'selected' : '' }}>هذا الشهر</option>
                                <option value="year" {{ ($filters['date_range'] ?? '') == 'year' ? 'selected' : '' }}>هذا العام</option>
                            </select>
                        </div>

                        <!-- نطاق المشاهدات -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">عدد المشاهدات</label>
                            <select name="views_range" class="form-select">
                                <option value="">جميع المشاهدات</option>
                                <option value="low" {{ ($filters['views_range'] ?? '') == 'low' ? 'selected' : '' }}>أقل من 100</option>
                                <option value="medium" {{ ($filters['views_range'] ?? '') == 'medium' ? 'selected' : '' }}>100 - 1000</option>
                                <option value="high" {{ ($filters['views_range'] ?? '') == 'high' ? 'selected' : '' }}>أكثر من 1000</option>
                            </select>
                        </div>

                        <!-- الإعلانات المميزة فقط -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       name="featured_only" value="1"
                                       id="featured_only"
                                       {{ ($filters['featured_only'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="featured_only">
                                    الإعلانات المميزة فقط
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                تطبيق الفلاتر
                            </button>
                            <a href="{{ route('search.advanced') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- النتائج -->
        <div class="col-lg-9">
            @if(!empty($filters))
                <!-- معلومات النتائج -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">نتائج البحث المتقدم</h4>
                    <span class="text-muted">
                        {{ number_format($totalResults) }} نتيجة
                    </span>
                </div>

                @if($totalResults > 0)
                    <!-- عرض النتائج -->
                    <div class="row">
                        @foreach($results as $ad)
                            <div class="col-md-6 col-xl-4 mb-4">
                                @include('ads.card', ['ad' => $ad, 'noWrapper' => true])
                            </div>
                        @endforeach
                    </div>

                    <!-- التصفح -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $results->appends(request()->query())->links() }}
                    </div>
                @else
                    <!-- لا توجد نتائج -->
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد نتائج</h4>
                        <p class="text-muted">جرب تعديل الفلاتر أو إزالة بعضها</p>
                    </div>
                @endif
            @else
                <!-- رسالة ترحيب -->
                <div class="text-center py-5">
                    <i class="fas fa-filter fa-3x text-primary mb-3"></i>
                    <h4>البحث المتقدم</h4>
                    <p class="text-muted">استخدم الفلاتر على اليسار للبحث المتقدم في الإعلانات</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق الفلاتر تلقائياً عند التغيير
    const form = document.getElementById('advancedSearchForm');
    const inputs = form.querySelectorAll('input[type="checkbox"], select');

    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // تأخير قصير لتحسين الأداء
            setTimeout(() => {
                form.submit();
            }, 300);
        });
    });
});
</script>
@endpush
