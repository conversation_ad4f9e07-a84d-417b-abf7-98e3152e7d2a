
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'ad',
    'config' => [],
    'showIcons' => true,        // إظهار الأيقونات
    'showDetails' => false,     // إظهار التفاصيل النصية
    'showReveal' => false,      // إظهار نظام الكشف المتدرج
    'iconStyle' => 'circular'   // نمط الأيقونات: circular, square, minimal
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'ad',
    'config' => [],
    'showIcons' => true,        // إظهار الأيقونات
    'showDetails' => false,     // إظهار التفاصيل النصية
    'showReveal' => false,      // إظهار نظام الكشف المتدرج
    'iconStyle' => 'circular'   // نمط الأيقونات: circular, square, minimal
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // الحصول على معلومات التواصل من جدول user_contacts
    $currentUser = auth()->user();
    $adOwner = $ad->user;

    // جلب معلومات التواصل المرئية للمستخدم الحالي
    $visibleContacts = $adOwner->getVisibleContactsFor($currentUser)->get();

    // تصنيف معلومات التواصل حسب النوع
    $contactsByType = $visibleContacts->groupBy('contact_type');

    // الحصول على معلومات التواصل الأساسية
    $primaryPhone = $contactsByType->get('phone', collect())->where('is_primary', true)->first()
                   ?? $contactsByType->get('phone', collect())->first();
    $primaryWhatsApp = $contactsByType->get('whatsapp', collect())->where('is_primary', true)->first()
                      ?? $contactsByType->get('whatsapp', collect())->first();
    $primaryEmail = $contactsByType->get('email', collect())->where('is_primary', true)->first()
                   ?? $contactsByType->get('email', collect())->first();

    // التحقق من وجود معلومات التواصل
    $hasPhone = $primaryPhone !== null;
    $hasWhatsApp = $primaryWhatsApp !== null;
    $hasEmail = $primaryEmail !== null;

    // دعم contact_info القديم كـ fallback
    $hasLegacyContact = !empty($ad->contact_info) && (!$hasPhone && !$hasEmail && !$hasWhatsApp);

    // تحضير أرقام الهاتف والواتساب
    $phoneNumber = $hasPhone ? $primaryPhone->contact_value : '';
    $whatsappNumber = $hasWhatsApp ? $primaryWhatsApp->contact_value : ($hasPhone ? $phoneNumber : '');
    $emailAddress = $hasEmail ? $primaryEmail->contact_value : '';

    // تنظيف رقم الواتساب (إزالة المسافات والرموز)
    if ($whatsappNumber) {
        $whatsappNumber = preg_replace('/[^0-9+]/', '', $whatsappNumber);

        // إضافة رمز الدولة إذا لم يكن موجود (افتراضي: اليمن +967)
        if (!str_starts_with($whatsappNumber, '+')) {
            if (str_starts_with($whatsappNumber, '967')) {
                $whatsappNumber = '+' . $whatsappNumber;
            } elseif (str_starts_with($whatsappNumber, '0')) {
                $whatsappNumber = '+967' . substr($whatsappNumber, 1);
            } else {
                $whatsappNumber = '+967' . $whatsappNumber;
            }
        }
    }

    // رسالة واتساب افتراضية
    $whatsappMessage = "مرحباً، أنا مهتم بإعلانك: " . $ad->title;

    // تحديد نوع العرض
    $isDetailed = ($config['variant'] ?? 'default') === 'detailed';
    $isCompact = ($config['variant'] ?? 'default') === 'compact';

    // تحديد ما يجب عرضه
    $showDetails = $showDetails || $isDetailed;
    $showIcons = $showIcons && ($hasPhone || $hasEmail || $hasWhatsApp);

    // نظام بسيط: إما تظهر للجميع أو تختفي عن الجميع
    try {
        $privacySettings = $adOwner->privacySettings;
        $shouldHideContacts = false;
        $shouldMask = false;
        $isOwner = $currentUser && $currentUser->id === $adOwner->id;

        if ($privacySettings) {
            // نظام بسيط: إذا أخفاها عن الضيوف أو المسجلين = مخفية للجميع
            $shouldHideContacts = !$privacySettings->show_contacts_to_guests || !$privacySettings->show_contacts_to_registered;

            // إذا كانت المعلومات مسموح بعرضها، تحقق من الحاجة للتقنيع للضيوف فقط
            if (!$shouldHideContacts) {
                $shouldMask = $privacySettings->require_verification_to_view && !$currentUser;
            }
        } else {
            // إعدادات افتراضية: إظهار للجميع
            $shouldHideContacts = false;
        }
    } catch (\Exception $e) {
        // في حالة حدوث خطأ، استخدم إعدادات افتراضية آمنة
        $shouldHideContacts = false;
        $shouldMask = false;
        \Log::warning('Error accessing privacy settings in contact component', [
            'user_id' => $adOwner->id,
            'error' => $e->getMessage()
        ]);
    }

    $maskedPhone = $hasPhone && $shouldMask ? substr($phoneNumber, 0, 3) . '****' . substr($phoneNumber, -2) : $phoneNumber;
    $maskedEmail = $hasEmail && $shouldMask ? substr($emailAddress, 0, 3) . '****@' . substr(strstr($emailAddress, '@'), 1) : $emailAddress;
    $maskedWhatsApp = $hasWhatsApp && $shouldMask ? substr($primaryWhatsApp->contact_value, 0, 3) . '****' . substr($primaryWhatsApp->contact_value, -2) : ($primaryWhatsApp->contact_value ?? '');
?>


<?php if(($hasPhone || $hasEmail || $hasWhatsApp || $hasLegacyContact) && !$shouldHideContacts): ?>
    <div class="contact-section <?php echo e($isDetailed ? 'contact-detailed' : 'contact-compact'); ?>">

        
        <?php if($showIcons): ?>
            <div class="contact-icons-section">
                <div class="contact-icons-container">
                    
                    <?php if($hasPhone): ?>
                        <div class="contact-icon-item">
                            <a href="tel:<?php echo e($phoneNumber); ?>"
                               class="contact-icon phone-icon <?php echo e($iconStyle); ?>"
                               title="اتصال مباشر: <?php echo e($primaryPhone->display_label ?? 'هاتف'); ?>"
                               onclick="trackContactClick('phone', <?php echo e($ad->id); ?>, <?php echo e($primaryPhone->id); ?>)">
                                <i class="fas fa-phone"></i>
                                <span class="icon-label"><?php echo e($primaryPhone->display_label ?? 'اتصال'); ?></span>
                            </a>
                        </div>
                    <?php endif; ?>

                    
                    <?php if($hasWhatsApp): ?>
                        <div class="contact-icon-item">
                            <a href="https://wa.me/<?php echo e($whatsappNumber); ?>?text=<?php echo e(urlencode($whatsappMessage)); ?>"
                               class="contact-icon whatsapp-icon <?php echo e($iconStyle); ?>"
                               title="تواصل عبر الواتساب: <?php echo e($primaryWhatsApp->display_label ?? 'واتساب'); ?>"
                               target="_blank"
                               onclick="trackContactClick('whatsapp', <?php echo e($ad->id); ?>, <?php echo e($primaryWhatsApp->id); ?>)">
                                <i class="fab fa-whatsapp"></i>
                                <span class="icon-label"><?php echo e($primaryWhatsApp->display_label ?? 'واتساب'); ?></span>
                            </a>
                        </div>
                    <?php endif; ?>

                    
                    <?php if($hasEmail): ?>
                        <div class="contact-icon-item">
                            <a href="mailto:<?php echo e($emailAddress); ?>?subject=<?php echo e(urlencode('استفسار حول: ' . $ad->title)); ?>"
                               class="contact-icon email-icon <?php echo e($iconStyle); ?>"
                               title="إرسال إيميل: <?php echo e($primaryEmail->display_label ?? 'إيميل'); ?>"
                               onclick="trackContactClick('email', <?php echo e($ad->id); ?>, <?php echo e($primaryEmail->id); ?>)">
                                <i class="fas fa-envelope"></i>
                                <span class="icon-label"><?php echo e($primaryEmail->display_label ?? 'إيميل'); ?></span>
                            </a>
                        </div>
                    <?php endif; ?>

                    
                    <?php $__currentLoopData = $visibleContacts->whereNotIn('contact_type', ['phone', 'whatsapp', 'email']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="contact-icon-item">
                            <?php
                                $contactUrl = '';
                                $iconClass = 'fas fa-link';
                                $targetBlank = '';

                                switch($contact->contact_type) {
                                    case 'telegram':
                                        $contactUrl = 'https://t.me/' . ltrim($contact->contact_value, '@');
                                        $iconClass = 'fab fa-telegram';
                                        $targetBlank = 'target="_blank"';
                                        break;
                                    case 'instagram':
                                        $contactUrl = 'https://instagram.com/' . ltrim($contact->contact_value, '@');
                                        $iconClass = 'fab fa-instagram';
                                        $targetBlank = 'target="_blank"';
                                        break;
                                    case 'facebook':
                                        $contactUrl = 'https://facebook.com/' . ltrim($contact->contact_value, '@');
                                        $iconClass = 'fab fa-facebook';
                                        $targetBlank = 'target="_blank"';
                                        break;
                                    case 'twitter':
                                        $contactUrl = 'https://twitter.com/' . ltrim($contact->contact_value, '@');
                                        $iconClass = 'fab fa-twitter';
                                        $targetBlank = 'target="_blank"';
                                        break;
                                    case 'linkedin':
                                        $contactUrl = 'https://linkedin.com/in/' . ltrim($contact->contact_value, '@');
                                        $iconClass = 'fab fa-linkedin';
                                        $targetBlank = 'target="_blank"';
                                        break;
                                    case 'website':
                                        $contactUrl = str_starts_with($contact->contact_value, 'http') ? $contact->contact_value : 'https://' . $contact->contact_value;
                                        $iconClass = 'fas fa-globe';
                                        $targetBlank = 'target="_blank"';
                                        break;
                                    default:
                                        $contactUrl = $contact->contact_value;
                                        $iconClass = 'fas fa-link';
                                        break;
                                }
                            ?>

                            <a href="<?php echo e($contactUrl); ?>"
                               class="contact-icon <?php echo e($contact->contact_type); ?>-icon <?php echo e($iconStyle); ?>"
                               title="<?php echo e($contact->display_label ?? ucfirst($contact->contact_type)); ?>"
                               <?php echo e($targetBlank); ?>

                               onclick="trackContactClick('<?php echo e($contact->contact_type); ?>', <?php echo e($ad->id); ?>, <?php echo e($contact->id); ?>)">
                                <i class="<?php echo e($iconClass); ?>"></i>
                                <span class="icon-label"><?php echo e($contact->display_label ?? ucfirst($contact->contact_type)); ?></span>
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>

        
        <?php if($showDetails): ?>
            <div class="contact-details-section">
                
                <?php $__currentLoopData = $visibleContacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="contact-item <?php echo e($contact->contact_type); ?>-contact">
                        <?php
                            $contactIcon = match($contact->contact_type) {
                                'phone' => 'fas fa-phone text-success',
                                'whatsapp' => 'fab fa-whatsapp text-success',
                                'email' => 'fas fa-envelope text-primary',
                                'telegram' => 'fab fa-telegram text-info',
                                'instagram' => 'fab fa-instagram text-danger',
                                'facebook' => 'fab fa-facebook text-primary',
                                'twitter' => 'fab fa-twitter text-info',
                                'linkedin' => 'fab fa-linkedin text-primary',
                                'website' => 'fas fa-globe text-secondary',
                                default => 'fas fa-link text-muted'
                            };

                            $displayValue = $shouldMask && !$contact->is_verified ?
                                substr($contact->contact_value, 0, 3) . '****' . substr($contact->contact_value, -2) :
                                $contact->contact_value;
                        ?>

                        <?php if($showReveal && $shouldMask && !$contact->is_verified): ?>
                            <div class="contact-preview" id="contact-preview-<?php echo e($contact->id); ?>">
                                <i class="<?php echo e($contactIcon); ?> me-2"></i>
                                <span class="contact-label"><?php echo e($contact->display_label ?? ucfirst($contact->contact_type)); ?>:</span>
                                <span class="masked-info"><?php echo e($displayValue); ?></span>
                                <?php if($contact->is_primary): ?>
                                    <span class="badge bg-success ms-2">أساسي</span>
                                <?php endif; ?>
                                <button class="btn btn-sm btn-outline-primary reveal-btn ms-2"
                                        onclick="revealContact('<?php echo e($contact->id); ?>', '<?php echo e($ad->id); ?>')"
                                        data-contact-id="<?php echo e($contact->id); ?>">
                                    <i class="fas fa-eye me-1"></i>
                                    إظهار
                                </button>
                            </div>

                            <div class="contact-revealed d-none" id="contact-revealed-<?php echo e($contact->id); ?>">
                                <i class="<?php echo e($contactIcon); ?> me-2"></i>
                                <span class="contact-label"><?php echo e($contact->display_label ?? ucfirst($contact->contact_type)); ?>:</span>
                                <span class="full-info" id="full-contact-<?php echo e($contact->id); ?>"></span>
                                <?php if($contact->is_primary): ?>
                                    <span class="badge bg-success ms-2">أساسي</span>
                                <?php endif; ?>
                                <div class="contact-actions mt-2">
                                    <?php if($contact->contact_type === 'phone'): ?>
                                        <a href="tel:<?php echo e($contact->contact_value); ?>" class="btn btn-sm btn-success me-2">
                                            <i class="fas fa-phone me-1"></i>اتصال
                                        </a>
                                        <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9+]/', '', $contact->contact_value)); ?>"
                                           class="btn btn-sm btn-success" target="_blank">
                                            <i class="fab fa-whatsapp me-1"></i>واتساب
                                        </a>
                                    <?php elseif($contact->contact_type === 'email'): ?>
                                        <a href="mailto:<?php echo e($contact->contact_value); ?>?subject=<?php echo e(urlencode('استفسار حول: ' . $ad->title)); ?>"
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-envelope me-1"></i>إرسال إيميل
                                        </a>
                                    <?php else: ?>
                                        <?php
                                            $actionUrl = match($contact->contact_type) {
                                                'whatsapp' => 'https://wa.me/' . preg_replace('/[^0-9+]/', '', $contact->contact_value),
                                                'telegram' => 'https://t.me/' . ltrim($contact->contact_value, '@'),
                                                'instagram' => 'https://instagram.com/' . ltrim($contact->contact_value, '@'),
                                                'facebook' => 'https://facebook.com/' . ltrim($contact->contact_value, '@'),
                                                'twitter' => 'https://twitter.com/' . ltrim($contact->contact_value, '@'),
                                                'linkedin' => 'https://linkedin.com/in/' . ltrim($contact->contact_value, '@'),
                                                'website' => str_starts_with($contact->contact_value, 'http') ? $contact->contact_value : 'https://' . $contact->contact_value,
                                                default => $contact->contact_value
                                            };
                                        ?>
                                        <a href="<?php echo e($actionUrl); ?>" class="btn btn-sm btn-primary" target="_blank">
                                            <i class="<?php echo e(explode(' ', $contactIcon)[1]); ?> me-1"></i>
                                            فتح <?php echo e($contact->display_label ?? ucfirst($contact->contact_type)); ?>

                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="contact-item-simple">
                                <i class="<?php echo e($contactIcon); ?> me-1"></i>
                                <span class="contact-label"><?php echo e($contact->display_label ?? ucfirst($contact->contact_type)); ?>:</span>
                                <small class="text-muted"><?php echo e($displayValue); ?></small>
                                <?php if($contact->is_primary): ?>
                                    <span class="badge bg-success ms-2">أساسي</span>
                                <?php endif; ?>
                                <?php if($contact->is_verified): ?>
                                    <span class="badge bg-info ms-1">متحقق</span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


            </div>
        <?php endif; ?>

        
        <?php if($hasLegacyContact): ?>
            <div class="contact-info-legacy">
                <?php if(is_array($ad->contact_info)): ?>
                    <?php if(isset($ad->contact_info['phone'])): ?>
                        <div class="contact-item-simple">
                            <i class="fas fa-phone text-success me-1"></i>
                            <small class="text-muted"><?php echo e($ad->contact_info['phone']); ?></small>
                        </div>
                    <?php endif; ?>

                    <?php if(isset($ad->contact_info['email'])): ?>
                        <div class="contact-item-simple">
                            <i class="fas fa-envelope text-primary me-1"></i>
                            <small class="text-muted"><?php echo e($ad->contact_info['email']); ?></small>
                        </div>
                    <?php endif; ?>

                    <?php if(isset($ad->contact_info['whatsapp'])): ?>
                        <div class="contact-item-simple">
                            <i class="fab fa-whatsapp text-success me-1"></i>
                            <small class="text-muted"><?php echo e($ad->contact_info['whatsapp']); ?></small>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="contact-item-simple">
                        <i class="fas fa-info-circle text-info me-1"></i>
                        <small class="text-muted"><?php echo e($ad->contact_info); ?></small>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        
        <?php if($showReveal && $isDetailed): ?>
            <div class="contact-stats mt-3">
                <small class="text-muted">
                    <i class="fas fa-eye me-1"></i>
                    تم الكشف عن معلومات التواصل <?php echo e($ad->contact_reveals_count ?? 0); ?> مرة
                </small>
            </div>
        <?php endif; ?>
    </div>

    

    
    <?php if (! $__env->hasRenderedOnce('bb8fb1f9-9d5c-427d-af18-7c5e6ee9cafa')): $__env->markAsRenderedOnce('bb8fb1f9-9d5c-427d-af18-7c5e6ee9cafa'); ?>
        <?php $__env->startPush('scripts'); ?>
            <script>
                // تتبع النقرات - محدث للنظام الجديد
                function trackContactClick(type, adId, contactId = null) {
                    // تتبع النقرات لأغراض الإحصائيات
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'contact_click', {
                            'contact_type': type,
                            'ad_id': adId,
                            'contact_id': contactId
                        });
                    }

                    // إرسال طلب AJAX لتسجيل النقرة في نظام الأمان الجديد
                    fetch(`/api/contact-access`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            contact_type: type,
                            ad_id: adId,
                            contact_id: contactId,
                            access_type: 'click'
                        })
                    }).catch(error => {
                        console.log('Contact tracking error:', error);
                    });
                }

                // كشف معلومات التواصل (للنظام المتقدم الجديد)
                function revealContact(contactId, adId) {
                    const previewElement = document.getElementById(`contact-preview-${contactId}`);
                    const revealedElement = document.getElementById(`contact-revealed-${contactId}`);

                    if (previewElement && revealedElement) {
                        // طلب AJAX لكشف معلومة التواصل
                        fetch(`/api/contacts/${contactId}/reveal`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                ad_id: adId,
                                access_type: 'reveal'
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                previewElement.classList.add('d-none');
                                revealedElement.classList.remove('d-none');
                                document.getElementById(`full-contact-${contactId}`).textContent = data.contact_value;

                                // تحديث الروابط حسب نوع التواصل
                                updateContactActions(contactId, data.contact_type, data.contact_value, adId);
                            } else {
                                alert(data.message || 'فشل في كشف معلومة التواصل');
                            }
                        })
                        .catch(error => {
                            console.log('Contact reveal error:', error);
                            alert('حدث خطأ في كشف معلومة التواصل');
                        });
                    }
                }

                // تحديث أزرار الإجراءات بعد كشف معلومة التواصل
                function updateContactActions(contactId, contactType, contactValue, adId) {
                    const actionsContainer = document.querySelector(`#contact-revealed-${contactId} .contact-actions`);
                    if (!actionsContainer) return;

                    // مسح الأزرار الموجودة
                    actionsContainer.innerHTML = '';

                    // إضافة الأزرار المناسبة حسب نوع التواصل
                    switch(contactType) {
                        case 'phone':
                            actionsContainer.innerHTML = `
                                <a href="tel:${contactValue}" class="btn btn-sm btn-success me-2">
                                    <i class="fas fa-phone me-1"></i>اتصال
                                </a>
                                <a href="https://wa.me/${contactValue.replace(/[^0-9+]/g, '')}"
                                   class="btn btn-sm btn-success" target="_blank">
                                    <i class="fab fa-whatsapp me-1"></i>واتساب
                                </a>
                            `;
                            break;
                        case 'email':
                            actionsContainer.innerHTML = `
                                <a href="mailto:${contactValue}?subject=استفسار حول الإعلان"
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-envelope me-1"></i>إرسال إيميل
                                </a>
                            `;
                            break;
                        case 'whatsapp':
                            actionsContainer.innerHTML = `
                                <a href="https://wa.me/${contactValue.replace(/[^0-9+]/g, '')}"
                                   class="btn btn-sm btn-success" target="_blank">
                                    <i class="fab fa-whatsapp me-1"></i>فتح واتساب
                                </a>
                            `;
                            break;
                        default:
                            // للأنواع الأخرى، إضافة زر عام
                            actionsContainer.innerHTML = `
                                <button class="btn btn-sm btn-secondary" onclick="copyToClipboard('${contactValue}')">
                                    <i class="fas fa-copy me-1"></i>نسخ
                                </button>
                            `;
                    }
                }

                // نسخ النص للحافظة
                function copyToClipboard(text) {
                    navigator.clipboard.writeText(text).then(() => {
                        // إظهار رسالة نجاح
                        const toast = document.createElement('div');
                        toast.className = 'toast-message';
                        toast.textContent = 'تم نسخ المعلومات بنجاح';
                        toast.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            background: #28a745;
                            color: white;
                            padding: 10px 20px;
                            border-radius: 5px;
                            z-index: 9999;
                        `;
                        document.body.appendChild(toast);
                        setTimeout(() => toast.remove(), 3000);
                    }).catch(err => {
                        console.log('Copy failed:', err);
                    });
                }

                // دعم الوظائف القديمة للتوافق مع النظام السابق
                function revealPhone(adId) {
                    // البحث عن أول معلومة هاتف وكشفها
                    const phonePreview = document.querySelector(`[id^="contact-preview-"][id*="phone"]`);
                    if (phonePreview) {
                        const contactId = phonePreview.id.replace('contact-preview-', '');
                        revealContact(contactId, adId);
                    }
                }

                // كشف البريد الإلكتروني (للتوافق مع النظام السابق)
                function revealEmail(adId) {
                    // البحث عن أول معلومة إيميل وكشفها
                    const emailPreview = document.querySelector(`[id^="contact-preview-"][id*="email"]`);
                    if (emailPreview) {
                        const contactId = emailPreview.id.replace('contact-preview-', '');
                        revealContact(contactId, adId);
                    }
                }
            </script>
        <?php $__env->stopPush(); ?>
    <?php endif; ?>
<?php endif; ?>


<?php if($shouldHideContacts && ($hasPhone || $hasEmail || $hasWhatsApp || $hasLegacyContact)): ?>
    <div class="contact-section contact-hidden">
        <div class="alert alert-info border-0 bg-light">
            <div class="d-flex align-items-center">
                <i class="fas fa-eye-slash text-muted me-2"></i>
                <div>
                    <small class="text-muted mb-1 d-block fw-bold">
                        معلومات التواصل مخفية
                    </small>
                    <small class="text-muted">
                        <?php if($isOwner): ?>
                            <i class="fas fa-cog me-1"></i>
                            يمكنك تغيير هذا في إعدادات الخصوصية
                        <?php else: ?>
                            صاحب الإعلان اختار إخفاء معلومات التواصل
                        <?php endif; ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/ad-card/partials/contact.blade.php ENDPATH**/ ?>