<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج إعدادات الخصوصية للمستخدمين
 * يحتوي على جميع إعدادات الخصوصية والأمان لمعلومات التواصل
 */
class UserPrivacySetting extends Model
{
    /**
     * اسم الجدول
     */
    protected $table = 'user_privacy_settings';

    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',
        'show_contacts_to_guests',
        'show_contacts_to_registered',
        'require_verification_to_view',
        'enable_contact_rate_limiting',
        'max_contact_views_per_hour',
        'max_contact_views_per_day',
        'require_phone_verification',
        'require_email_verification',
        'auto_hide_unverified_contacts',
        'notify_on_contact_view',
        'notify_on_contact_copy',
        'notify_on_suspicious_activity',
        'enable_contact_encryption',
        'enable_watermark',
        'disable_right_click',
        'disable_text_selection',
        'allow_contact_sharing',
        'allow_contact_export',
        'show_contact_qr_code',
        'track_contact_interactions',
        'log_contact_access',
        'enable_analytics',
    ];

    /**
     * تحويل الحقول إلى أنواع محددة
     */
    protected $casts = [
        'show_contacts_to_guests' => 'boolean',
        'show_contacts_to_registered' => 'boolean',
        'require_verification_to_view' => 'boolean',
        'enable_contact_rate_limiting' => 'boolean',
        'max_contact_views_per_hour' => 'integer',
        'max_contact_views_per_day' => 'integer',
        'require_phone_verification' => 'boolean',
        'require_email_verification' => 'boolean',
        'auto_hide_unverified_contacts' => 'boolean',
        'notify_on_contact_view' => 'boolean',
        'notify_on_contact_copy' => 'boolean',
        'notify_on_suspicious_activity' => 'boolean',
        'enable_contact_encryption' => 'boolean',
        'enable_watermark' => 'boolean',
        'disable_right_click' => 'boolean',
        'disable_text_selection' => 'boolean',
        'allow_contact_sharing' => 'boolean',
        'allow_contact_export' => 'boolean',
        'show_contact_qr_code' => 'boolean',
        'track_contact_interactions' => 'boolean',
        'log_contact_access' => 'boolean',
        'enable_analytics' => 'boolean',
    ];

    /**
     * الإعدادات الافتراضية
     */
    public static function getDefaultSettings(): array
    {
        return [
            'show_contacts_to_guests' => true,
            'show_contacts_to_registered' => true,
            'require_verification_to_view' => false,
            'enable_contact_rate_limiting' => true,
            'max_contact_views_per_hour' => 10,
            'max_contact_views_per_day' => 50,
            'require_phone_verification' => false,
            'require_email_verification' => false,
            'auto_hide_unverified_contacts' => false,
            'notify_on_contact_view' => false,
            'notify_on_contact_copy' => false,
            'notify_on_suspicious_activity' => true,
            'enable_contact_encryption' => false,
            'enable_watermark' => false,
            'disable_right_click' => false,
            'disable_text_selection' => false,
            'allow_contact_sharing' => true,
            'allow_contact_export' => false,
            'show_contact_qr_code' => false,
            'track_contact_interactions' => true,
            'log_contact_access' => true,
            'enable_analytics' => false,
        ];
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * التحقق من إمكانية عرض معلومات التواصل للمستخدم المحدد
     */
    public function canViewContacts(?User $viewer = null): bool
    {
        // إذا كان المشاهد هو صاحب المعلومات
        if ($viewer && $viewer->id === $this->user_id) {
            return true;
        }

        // إذا كان المشاهد مدير
        if ($viewer && $viewer->is_admin) {
            return true;
        }

        // إذا كان المشاهد غير مسجل (زائر)
        if (!$viewer) {
            return $this->show_contacts_to_guests;
        }

        // إذا كان المشاهد مسجل
        return $this->show_contacts_to_registered;
    }

    /**
     * التحقق من الحاجة للتحقق قبل عرض المعلومات
     */
    public function requiresVerificationToView(?User $viewer = null): bool
    {
        if (!$this->require_verification_to_view) {
            return false;
        }

        // إذا كان المشاهد هو صاحب المعلومات أو مدير
        if ($viewer && ($viewer->id === $this->user_id || $viewer->is_admin)) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من تجاوز حدود المشاهدة
     */
    public function isRateLimited(string $ipAddress): bool
    {
        if (!$this->enable_contact_rate_limiting) {
            return false;
        }

        // هنا يمكن إضافة منطق فحص Rate Limiting
        // باستخدام Cache أو جدول منفصل لتتبع المشاهدات

        return false; // مؤقتاً
    }

    /**
     * الحصول على إعدادات الأمان النشطة
     */
    public function getActiveSecurityFeatures(): array
    {
        $features = [];

        if ($this->enable_contact_encryption) {
            $features[] = 'encryption';
        }

        if ($this->enable_watermark) {
            $features[] = 'watermark';
        }

        if ($this->disable_right_click) {
            $features[] = 'no_right_click';
        }

        if ($this->disable_text_selection) {
            $features[] = 'no_text_selection';
        }

        if ($this->require_verification_to_view) {
            $features[] = 'verification_required';
        }

        return $features;
    }

    /**
     * إنشاء إعدادات افتراضية للمستخدم
     */
    public static function createDefaultForUser(int $userId): self
    {
        return self::create(array_merge(
            ['user_id' => $userId],
            self::getDefaultSettings()
        ));
    }
}
