@extends('layouts.app')

@section('title', __('Ad Settings'))

@section('content')
<div class="container my-5">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 fw-bold text-primary">
                        <i class="fas fa-bullhorn me-2"></i>
                        {{ __('Ad Settings') }}
                    </h1>
                    <p class="text-muted">{{ __('Manage ads system settings') }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.settings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        {{ __('Back to Settings') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج الإعدادات -->
    <form action="{{ route('admin.settings.ads.update') }}" method="POST" id="settingsForm">
        @csrf
        @method('PUT')

        @foreach($settings as $groupName => $groupSettings)
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        @switch($groupName)
                            @case('ads_general')
                                {{ __('General Settings') }}
                                @break
                            @case('ads_ui')
                                {{ __('UI Settings') }}
                                @break
                            @case('ads_images')
                                {{ __('Image Settings') }}
                                @break
                            @default
                                {{ ucfirst(str_replace('_', ' ', $groupName)) }}
                        @endswitch
                    </h5>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        @foreach($groupSettings as $setting)
                            <div class="col-md-6 mb-4">
                                <div class="setting-item p-3 border rounded">
                                    <label class="form-label fw-bold">
                                        {{ $setting->title }}
                                    </label>
                                    
                                    @if($setting->description)
                                        <p class="text-muted small mb-3">{{ $setting->description }}</p>
                                    @endif

                                    @switch($setting->type)
                                        @case('boolean')
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" 
                                                       type="checkbox" 
                                                       id="setting_{{ $setting->key }}"
                                                       name="settings[{{ $setting->key }}]"
                                                       value="1"
                                                       {{ $setting->value ? 'checked' : '' }}>
                                                <label class="form-check-label" for="setting_{{ $setting->key }}">
                                                    {{ $setting->value ? __('Enabled') : __('Disabled') }}
                                                </label>
                                            </div>
                                            @break

                                        @case('number')
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="setting_{{ $setting->key }}"
                                                   name="settings[{{ $setting->key }}]"
                                                   value="{{ $setting->value }}"
                                                   min="0">
                                            @break

                                        @case('text')
                                        @default
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="setting_{{ $setting->key }}"
                                                   name="settings[{{ $setting->key }}]"
                                                   value="{{ $setting->value }}">
                                            @break
                                    @endswitch
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endforeach

        <!-- أزرار الحفظ -->
        <div class="row">
            <div class="col-12">
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                        <i class="fas fa-undo me-1"></i>
                        {{ __('Reset') }}
                    </button>
                    <button type="submit" class="btn btn-primary arabic-text" id="saveBtn">
                        <i class="fas fa-save me-1"></i>
                        {{ __('Save Settings') }}
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- معلومات إضافية -->
    <div class="alert alert-info mt-4">
        <h6 class="alert-heading">
            <i class="fas fa-info-circle me-2"></i>
            {{ __('Important Notes') }}
        </h6>
        <ul class="mb-0 arabic-text">
            <li>{{ __('UI settings affect the appearance of buttons and elements on the site') }}</li>
            <li>{{ __('General settings control the behavior of the entire ads system') }}</li>
            <li>{{ __('Image settings determine the limits for uploading images for ads') }}</li>
            <li>{{ __('Changes are applied immediately after saving') }}</li>
        </ul>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-bullhorn fa-2x mb-2"></i>
                    <h4>{{ \App\Models\Ad::count() }}</h4>
                    <small class="arabic-text">{{ __('Total Ads') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4>{{ \App\Models\Ad::active()->count() }}</h4>
                    <small class="arabic-text">{{ __('Active Ads') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4>{{ \App\Models\Ad::pending()->count() }}</h4>
                    <small class="arabic-text">{{ __('Pending Review') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4>{{ \App\Models\User::has('ads')->count() }}</h4>
                    <small class="arabic-text">{{ __('Users with Ads') }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.setting-item {
    transition: all 0.3s ease;
    background-color: var(--bs-light);
}

.setting-item:hover {
    background-color: var(--bs-primary-bg-subtle);
    border-color: var(--bs-primary) !important;
}

.form-check-input:checked {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
}

.form-check-label {
    font-weight: 500;
}

.card-header {
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark, #0056b3));
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .setting-item {
        margin-bottom: 1rem;
    }
    
    .col-md-6 {
        margin-bottom: 1rem;
    }
}
</style>

<script>
// تحسين تجربة المستخدم
document.getElementById('settingsForm').addEventListener('submit', function() {
    const saveBtn = document.getElementById('saveBtn');
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>{{ __("Saving...") }}';
    saveBtn.disabled = true;
});

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('{{ __("Are you sure you want to reset all settings?") }}')) {
        document.getElementById('settingsForm').reset();
    }
}

// تحديث نص التبديل للـ switches
document.querySelectorAll('.form-check-input').forEach(function(checkbox) {
    checkbox.addEventListener('change', function() {
        const label = this.nextElementSibling;
        label.textContent = this.checked ? '{{ __("مفعل") }}' : '{{ __("معطل") }}';
    });
});
</script>
@endsection
