<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Category;
use App\Models\Ad;
use App\Models\UserContact;
use App\Models\UserPrivacySetting;
use App\Services\InputSanitizationService;

/**
 * بذر قاعدة البيانات الرئيسية - شامل ومتكامل
 * يحتوي على جميع البيانات المطلوبة للاختبار الكامل
 */
class DatabaseSeeder extends Seeder
{
    /**
     * تشغيل البذر الشامل
     */
    public function run(): void
    {
        $this->command->info('🌱 بدء بذر البيانات الشاملة...');
        $this->command->newLine();

        // 1. إنشاء المستخدم الإداري
        $this->createAdminUser();

        // 2. إنشاء المستخدمين التجريبيين
        $this->createTestUsers();

        // 3. إنشاء الفئات
        $this->createCategories();

        // 4. إنشاء الإعلانات التجريبية
        $this->createTestAds();

        // 5. إنشاء إعلانات الأسعار المتقدمة
        $this->createAdvancedPricingTestAds();

        // 6. إنشاء معلومات التواصل للمستخدمين
        $this->createUserContacts();

        // 7. إنشاء إعدادات الخصوصية للمستخدمين
        $this->createUserPrivacySettings();

        $this->command->newLine();
        $this->command->info('🎉 تم بذر جميع البيانات بنجاح!');
        $this->command->info('👤 يمكنك تسجيل الدخول كمدير: <EMAIL>');
        $this->command->warn('🔑 كلمة المرور الإدارية تم عرضها أعلاه - احفظها في مكان آمن!');
        $this->command->info('🧪 المستخدمين التجريبيين: <EMAIL>, <EMAIL>, <EMAIL>');
        $this->command->info('🔑 كلمة مرور المستخدمين التجريبيين: TestPass123!');
    }

    /**
     *
     * إنشاء المستخدم الإداري
     */
    private function createAdminUser(): void
    {
        $this->command->info('👑 إنشاء المستخدم الإداري...');

        // التحقق من وجود المستخدم الإداري
        $existingAdmin = User::where('email', '<EMAIL>')->first();

        if ($existingAdmin) {
            $this->command->warn('⚠️  المستخدم الإداري موجود بالفعل');
            return;
        }

        // إنشاء كلمة مرور قوية
        $securePassword = $this->generateSecurePassword();

        // إنشاء المستخدم الإداري
        User::create([
            'name' => 'المدير العام',
            'email' => '<EMAIL>',
            'password' => Hash::make($securePassword),
            'is_admin' => true,
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $this->command->info('✅ تم إنشاء المستخدم الإداري بنجاح');
        $this->command->info('📧 البريد الإلكتروني: <EMAIL>');
        $this->command->line('🔑 كلمة المرور: <fg=red>' . $securePassword . '</fg=red>');
        $this->command->warn('⚠️  احفظ كلمة المرور في مكان آمن!');
    }

    /**
     * إنشاء المستخدمين التجريبيين
     */
    private function createTestUsers(): void
    {
        $this->command->info('👥 إنشاء المستخدمين التجريبيين...');

        $testUsers = [
            [
                'name' => 'أحمد محمد السالم',
                'email' => '<EMAIL>',
                'password' => 'TestPass123!',
            ],
            [
                'name' => 'فاطمة علي الزهراني',
                'email' => '<EMAIL>',
                'password' => 'TestPass123!',
            ],
            [
                'name' => 'محمد سالم العمري',
                'email' => '<EMAIL>',
                'password' => 'TestPass123!',
            ],
            [
                'name' => 'عائشة أحمد الحسني',
                'email' => '<EMAIL>',
                'password' => 'TestPass123!',
            ],
            [
                'name' => 'يوسف عبدالله المقطري',
                'email' => '<EMAIL>',
                'password' => 'TestPass123!',
            ],
        ];

        foreach ($testUsers as $userData) {
            User::firstOrCreate(
                ['email' => $userData['email']],
                [
                    'name' => InputSanitizationService::sanitizeText($userData['name']),
                    'email' => InputSanitizationService::sanitizeEmail($userData['email']),
                    'password' => Hash::make($userData['password']),
                    'email_verified_at' => now(),
                    'is_admin' => false,
                    'created_at' => now()->subDays(rand(1, 30)),
                    'updated_at' => now(),
                ]
            );
        }

        $this->command->info('✅ تم إنشاء ' . count($testUsers) . ' مستخدمين تجريبيين');
    }

    /**
     * إنشاء الفئات الأساسية
     */
    private function createCategories(): void
    {
        $this->command->info('📂 إنشاء الفئات الأساسية...');

        $categories = [
            // الفئات التجارية الأساسية
            [
                'name_ar' => 'سيارات ومركبات',
                'name_en' => 'Cars & Vehicles',
                'slug' => 'cars',
                'description_ar' => 'بيع وشراء السيارات والمركبات بجميع أنواعها',
                'description_en' => 'Buy and sell cars and vehicles of all types',
                'icon' => 'fas fa-car',
                'is_active' => true,
            ],
            [
                'name_ar' => 'عقارات',
                'name_en' => 'Real Estate',
                'slug' => 'real-estate',
                'description_ar' => 'بيع وإيجار العقارات والأراضي',
                'description_en' => 'Buy and rent real estate and land',
                'icon' => 'fas fa-home',
                'is_active' => true,
            ],
            [
                'name_ar' => 'إلكترونيات وأجهزة',
                'name_en' => 'Electronics & Devices',
                'slug' => 'electronics',
                'description_ar' => 'الأجهزة الإلكترونية والهواتف والحاسوب',
                'description_en' => 'Electronic devices, phones and computers',
                'icon' => 'fas fa-laptop',
                'is_active' => true,
            ],
            [
                'name_ar' => 'أثاث وديكور',
                'name_en' => 'Furniture & Decor',
                'slug' => 'furniture',
                'description_ar' => 'الأثاث والديكور المنزلي والمكتبي',
                'description_en' => 'Home and office furniture and decor',
                'icon' => 'fas fa-couch',
                'is_active' => true,
            ],
            [
                'name_ar' => 'وظائف وأعمال',
                'name_en' => 'Jobs & Business',
                'slug' => 'jobs',
                'description_ar' => 'فرص العمل والوظائف والأعمال التجارية',
                'description_en' => 'Job opportunities and business ventures',
                'icon' => 'fas fa-briefcase',
                'is_active' => true,
            ],
            [
                'name_ar' => 'خدمات',
                'name_en' => 'Services',
                'slug' => 'services',
                'description_ar' => 'الخدمات المختلفة والمهنية',
                'description_en' => 'Various professional services',
                'icon' => 'fas fa-tools',
                'is_active' => true,
            ],

            // الفئات التعليمية والمؤسسية
            [
                'name_ar' => 'مدارس',
                'name_en' => 'Schools',
                'slug' => 'schools',
                'description_ar' => 'المدارس الحكومية والخاصة والأهلية',
                'description_en' => 'Government, private and community schools',
                'icon' => 'fas fa-school',
                'is_active' => true,
            ],
            [
                'name_ar' => 'جامعات',
                'name_en' => 'Universities',
                'slug' => 'universities',
                'description_ar' => 'الجامعات والكليات الحكومية والخاصة',
                'description_en' => 'Government and private universities and colleges',
                'icon' => 'fas fa-university',
                'is_active' => true,
            ],
            [
                'name_ar' => 'معاهد ومراكز تدريب',
                'name_en' => 'Institutes & Training Centers',
                'slug' => 'institutes',
                'description_ar' => 'المعاهد التقنية ومراكز التدريب المهني',
                'description_en' => 'Technical institutes and vocational training centers',
                'icon' => 'fas fa-chalkboard-teacher',
                'is_active' => true,
            ],
            [
                'name_ar' => 'شركات',
                'name_en' => 'Companies',
                'slug' => 'companies',
                'description_ar' => 'الشركات والمؤسسات التجارية والصناعية',
                'description_en' => 'Commercial and industrial companies and enterprises',
                'icon' => 'fas fa-building',
                'is_active' => true,
            ],
            [
                'name_ar' => 'مستشفيات ومراكز طبية',
                'name_en' => 'Hospitals & Medical Centers',
                'slug' => 'hospitals',
                'description_ar' => 'المستشفيات والمراكز الطبية والعيادات',
                'description_en' => 'Hospitals, medical centers and clinics',
                'icon' => 'fas fa-hospital',
                'is_active' => true,
            ],
            [
                'name_ar' => 'مطاعم ومقاهي',
                'name_en' => 'Restaurants & Cafes',
                'slug' => 'restaurants',
                'description_ar' => 'المطاعم والمقاهي والوجبات السريعة',
                'description_en' => 'Restaurants, cafes and fast food',
                'icon' => 'fas fa-utensils',
                'is_active' => true,
            ],
            [
                'name_ar' => 'فنادق وسياحة',
                'name_en' => 'Hotels & Tourism',
                'slug' => 'hotels',
                'description_ar' => 'الفنادق والمنتجعات والخدمات السياحية',
                'description_en' => 'Hotels, resorts and tourism services',
                'icon' => 'fas fa-bed',
                'is_active' => true,
            ],

            // الفئات الأخرى
            [
                'name_ar' => 'أزياء وموضة',
                'name_en' => 'Fashion & Style',
                'slug' => 'fashion',
                'description_ar' => 'الملابس والأحذية والإكسسوارات',
                'description_en' => 'Clothing, shoes and accessories',
                'icon' => 'fas fa-tshirt',
                'is_active' => true,
            ],
            [
                'name_ar' => 'رياضة وترفيه',
                'name_en' => 'Sports & Entertainment',
                'slug' => 'sports',
                'description_ar' => 'الأدوات الرياضية وألعاب الترفيه',
                'description_en' => 'Sports equipment and entertainment games',
                'icon' => 'fas fa-football-ball',
                'is_active' => true,
            ],
            [
                'name_ar' => 'كتب وتعليم',
                'name_en' => 'Books & Education',
                'slug' => 'books',
                'description_ar' => 'الكتب والمواد التعليمية والدورات',
                'description_en' => 'Books, educational materials and courses',
                'icon' => 'fas fa-book',
                'is_active' => true,
            ],
            [
                'name_ar' => 'صحة وجمال',
                'name_en' => 'Health & Beauty',
                'slug' => 'health',
                'description_ar' => 'منتجات الصحة والجمال والعناية الشخصية',
                'description_en' => 'Health, beauty and personal care products',
                'icon' => 'fas fa-heart',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            $sanitizedData = [
                'name_ar' => InputSanitizationService::sanitizeText($categoryData['name_ar']),
                'name_en' => InputSanitizationService::sanitizeText($categoryData['name_en']),
                'slug' => $categoryData['slug'],
                'description_ar' => InputSanitizationService::sanitizeText($categoryData['description_ar']),
                'description_en' => InputSanitizationService::sanitizeText($categoryData['description_en']),
                'icon' => $categoryData['icon'],
                'is_active' => $categoryData['is_active'],
            ];

            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $sanitizedData
            );
        }

        $this->command->info('✅ تم إنشاء ' . count($categories) . ' فئات أساسية');
    }

    /**
     * إنشاء الإعلانات التجريبية الشاملة
     */
    private function createTestAds(): void
    {
        $this->command->info('📢 إنشاء الإعلانات التجريبية...');

        $users = User::where('is_admin', false)->get();
        $categories = Category::where('is_active', true)->get();

        if ($users->isEmpty() || $categories->isEmpty()) {
            $this->command->warn('⚠️  لا توجد مستخدمين أو فئات لإنشاء الإعلانات');
            return;
        }

        $testAds = [
            // إعلانات السيارات
            [
                'title_ar' => 'سيارة تويوتا كامري 2020 للبيع',
                'title_en' => 'Toyota Camry 2020 for Sale',
                'description_ar' => 'سيارة تويوتا كامري موديل 2020 في حالة ممتازة، قليلة الاستخدام، جميع الأوراق سليمة، صيانة دورية منتظمة، لون أبيض، فحص شامل متاح.',
                'description_en' => 'Toyota Camry 2020 in excellent condition, low usage, all papers valid, regular maintenance, white color, comprehensive inspection available.',
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-1-234567',
                'email' => '<EMAIL>',
                'category_slug' => 'cars',
                'price' => 45000,
            ],
            [
                'title_ar' => 'هيونداي إلنترا 2019 نظيفة جداً',
                'title_en' => 'Hyundai Elantra 2019 Very Clean',
                'description_ar' => 'سيارة هيونداي إلنترا 2019، نظيفة جداً، استعمال شخصي، ماشية 35 ألف كيلو فقط، تكييف بارد، جنوط أصلية.',
                'description_en' => 'Hyundai Elantra 2019, very clean, personal use, only 35k km, cold AC, original rims.',
                'location' => 'عدن، اليمن',
                'phone' => '+967-2-345678',
                'email' => '<EMAIL>',
                'category_slug' => 'cars',
                'price' => 38000,
            ],

            // إعلانات العقارات
            [
                'title_ar' => 'شقة مفروشة للإيجار في المدينة',
                'title_en' => 'Furnished Apartment for Rent in City',
                'description_ar' => 'شقة مفروشة بالكامل للإيجار، 3 غرف نوم، صالة واسعة، مطبخ حديث، حمامين، موقع ممتاز قريب من الخدمات، الطابق الثالث، مصعد متاح.',
                'description_en' => 'Fully furnished apartment for rent, 3 bedrooms, spacious living room, modern kitchen, 2 bathrooms, excellent location near services, 3rd floor, elevator available.',
                'location' => 'تعز، اليمن',
                'phone' => '+967-4-456789',
                'email' => '<EMAIL>',
                'category_slug' => 'real-estate',
                'price' => 800,
            ],
            [
                'title_ar' => 'فيلا للبيع في حي راقي',
                'title_en' => 'Villa for Sale in Upscale Neighborhood',
                'description_ar' => 'فيلا فاخرة للبيع، 5 غرف نوم، 4 حمامات، حديقة واسعة، مسبح، موقف سيارات، في حي راقي وهادئ، تشطيب عالي الجودة.',
                'description_en' => 'Luxury villa for sale, 5 bedrooms, 4 bathrooms, large garden, swimming pool, parking, in upscale quiet neighborhood, high-quality finishing.',
                'location' => 'الحديدة، اليمن',
                'phone' => '+967-3-567890',
                'email' => '<EMAIL>',
                'category_slug' => 'real-estate',
                'price' => 250000,
            ],

            // إعلانات المدارس
            [
                'title_ar' => 'مدرسة الأمل الأهلية - تسجيل مفتوح',
                'title_en' => 'Al-Amal Private School - Open Registration',
                'description_ar' => 'مدرسة الأمل الأهلية تفتح باب التسجيل للعام الدراسي الجديد، جميع المراحل من الروضة حتى الثانوية، كادر تعليمي مؤهل، مناهج حديثة، أنشطة متنوعة.',
                'description_en' => 'Al-Amal Private School opens registration for the new academic year, all levels from kindergarten to high school, qualified teaching staff, modern curricula, diverse activities.',
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-1-555001',
                'email' => '<EMAIL>',
                'category_slug' => 'schools',
                'price' => 1500,
            ],
            [
                'title_ar' => 'مدرسة النور الدولية - برامج متميزة',
                'title_en' => 'Al-Noor International School - Excellence Programs',
                'description_ar' => 'مدرسة النور الدولية تقدم برامج تعليمية متميزة، منهج بريطاني وأمريكي، تعليم اللغات، مختبرات علمية حديثة، ملاعب رياضية، رحلات تعليمية.',
                'description_en' => 'Al-Noor International School offers excellent educational programs, British and American curriculum, language education, modern science labs, sports fields, educational trips.',
                'location' => 'عدن، اليمن',
                'phone' => '+967-2-555002',
                'email' => '<EMAIL>',
                'category_slug' => 'schools',
                'price' => 2500,
            ],

            // إعلانات الجامعات
            [
                'title_ar' => 'جامعة صنعاء - كلية الهندسة',
                'title_en' => 'Sana\'a University - Faculty of Engineering',
                'description_ar' => 'جامعة صنعاء كلية الهندسة تعلن عن فتح باب القبول للعام الجامعي الجديد، تخصصات: هندسة مدنية، كهربائية، ميكانيكية، حاسوب، معمارية.',
                'description_en' => 'Sana\'a University Faculty of Engineering announces admission for the new academic year, specializations: civil, electrical, mechanical, computer, architectural engineering.',
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-1-666001',
                'email' => '<EMAIL>',
                'category_slug' => 'universities',
                'price' => 3000,
            ],
            [
                'title_ar' => 'جامعة عدن - كلية الطب',
                'title_en' => 'Aden University - Faculty of Medicine',
                'description_ar' => 'جامعة عدن كلية الطب تفتح باب القبول لدراسة الطب البشري، مدة الدراسة 6 سنوات، مستشفى جامعي للتدريب العملي، كادر طبي متخصص.',
                'description_en' => 'Aden University Faculty of Medicine opens admission for human medicine studies, 6-year program, university hospital for practical training, specialized medical staff.',
                'location' => 'عدن، اليمن',
                'phone' => '+967-2-666002',
                'email' => '<EMAIL>',
                'category_slug' => 'universities',
                'price' => 4000,
            ],

            // إعلانات المعاهد
            [
                'title_ar' => 'معهد التقنية العالي - دبلوم تقني',
                'title_en' => 'Higher Technical Institute - Technical Diploma',
                'description_ar' => 'معهد التقنية العالي يقدم برامج دبلوم تقني في: تقنية المعلومات، الإلكترونيات، الميكانيكا، التبريد والتكييف، مدة الدراسة سنتان.',
                'description_en' => 'Higher Technical Institute offers technical diploma programs in: IT, electronics, mechanics, refrigeration and air conditioning, 2-year duration.',
                'location' => 'تعز، اليمن',
                'phone' => '+967-4-777001',
                'email' => '<EMAIL>',
                'category_slug' => 'institutes',
                'price' => 1800,
            ],
            [
                'title_ar' => 'معهد اللغات الحديثة - دورات مكثفة',
                'title_en' => 'Modern Languages Institute - Intensive Courses',
                'description_ar' => 'معهد اللغات الحديثة يقدم دورات مكثفة في: الإنجليزية، الفرنسية، الألمانية، التركية، العربية للناطقين بغيرها، شهادات معتمدة.',
                'description_en' => 'Modern Languages Institute offers intensive courses in: English, French, German, Turkish, Arabic for non-native speakers, certified certificates.',
                'location' => 'الحديدة، اليمن',
                'phone' => '+967-3-777002',
                'email' => '<EMAIL>',
                'category_slug' => 'institutes',
                'price' => 500,
            ],

            // إعلانات الشركات
            [
                'title_ar' => 'شركة التقنية المتقدمة - حلول برمجية',
                'title_en' => 'Advanced Technology Company - Software Solutions',
                'description_ar' => 'شركة التقنية المتقدمة متخصصة في تطوير الحلول البرمجية، تطبيقات الويب والموبايل، أنظمة إدارة، تجارة إلكترونية، خبرة 15 عام.',
                'description_en' => 'Advanced Technology Company specializes in software solutions development, web and mobile applications, management systems, e-commerce, 15 years experience.',
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-1-888001',
                'email' => '<EMAIL>',
                'category_slug' => 'companies',
                'price' => null,
            ],
            [
                'title_ar' => 'شركة البناء والتعمير - مقاولات عامة',
                'title_en' => 'Construction & Development Company - General Contracting',
                'description_ar' => 'شركة البناء والتعمير متخصصة في المقاولات العامة، بناء المساكن، المباني التجارية، الطرق، البنية التحتية، فريق هندسي متخصص.',
                'description_en' => 'Construction & Development Company specializes in general contracting, residential buildings, commercial buildings, roads, infrastructure, specialized engineering team.',
                'location' => 'عدن، اليمن',
                'phone' => '+967-2-888002',
                'email' => '<EMAIL>',
                'category_slug' => 'companies',
                'price' => null,
            ],

            // إعلانات المستشفيات والمراكز الطبية
            [
                'title_ar' => 'مستشفى الشفاء العام - خدمات طبية شاملة',
                'title_en' => 'Al-Shifa General Hospital - Comprehensive Medical Services',
                'description_ar' => 'مستشفى الشفاء العام يقدم خدمات طبية شاملة، أقسام: الطوارئ، الجراحة، الباطنية، النساء والولادة، الأطفال، المختبر، الأشعة، على مدار 24 ساعة.',
                'description_en' => 'Al-Shifa General Hospital provides comprehensive medical services, departments: emergency, surgery, internal medicine, obstetrics, pediatrics, laboratory, radiology, 24/7.',
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-1-999001',
                'email' => '<EMAIL>',
                'category_slug' => 'hospitals',
                'price' => null,
            ],
            [
                'title_ar' => 'مركز الأسنان المتخصص - عيادة حديثة',
                'title_en' => 'Specialized Dental Center - Modern Clinic',
                'description_ar' => 'مركز الأسنان المتخصص يقدم جميع خدمات طب الأسنان، تقويم، زراعة، تجميل، علاج الجذور، أحدث الأجهزة، أطباء متخصصون.',
                'description_en' => 'Specialized Dental Center provides all dental services, orthodontics, implants, cosmetics, root canal treatment, latest equipment, specialized doctors.',
                'location' => 'تعز، اليمن',
                'phone' => '+967-4-999002',
                'email' => '<EMAIL>',
                'category_slug' => 'hospitals',
                'price' => 200,
            ],

            // إعلانات المطاعم والمقاهي
            [
                'title_ar' => 'مطعم الأصالة اليمنية - أكلات شعبية',
                'title_en' => 'Al-Asala Yemeni Restaurant - Traditional Food',
                'description_ar' => 'مطعم الأصالة اليمنية متخصص في الأكلات الشعبية اليمنية، مندي، كبسة، فحسة، شفوت، ملوخية، أجواء تراثية أصيلة، خدمة ممتازة.',
                'description_en' => 'Al-Asala Yemeni Restaurant specializes in traditional Yemeni cuisine, mandi, kabsa, fahsa, shafout, molokhia, authentic heritage atmosphere, excellent service.',
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-1-111001',
                'email' => '<EMAIL>',
                'category_slug' => 'restaurants',
                'price' => 25,
            ],
            [
                'title_ar' => 'مقهى الكتاب الثقافي - أجواء هادئة',
                'title_en' => 'Al-Kitab Cultural Cafe - Quiet Atmosphere',
                'description_ar' => 'مقهى الكتاب الثقافي يوفر أجواء هادئة للقراءة والدراسة، مكتبة، واي فاي مجاني، مشروبات ساخنة وباردة، حلويات، فعاليات ثقافية.',
                'description_en' => 'Al-Kitab Cultural Cafe provides quiet atmosphere for reading and studying, library, free WiFi, hot and cold drinks, desserts, cultural events.',
                'location' => 'عدن، اليمن',
                'phone' => '+967-2-111002',
                'email' => '<EMAIL>',
                'category_slug' => 'restaurants',
                'price' => 15,
            ],

            // إعلانات الفنادق والسياحة
            [
                'title_ar' => 'فندق الخليج الذهبي - إقامة فاخرة',
                'title_en' => 'Golden Gulf Hotel - Luxury Stay',
                'description_ar' => 'فندق الخليج الذهبي يوفر إقامة فاخرة، غرف مكيفة، إطلالة على البحر، مطعم، مسبح، صالة رياضة، خدمة الغرف 24 ساعة، موقع متميز.',
                'description_en' => 'Golden Gulf Hotel offers luxury accommodation, air-conditioned rooms, sea view, restaurant, swimming pool, gym, 24-hour room service, prime location.',
                'location' => 'عدن، اليمن',
                'phone' => '+967-2-222001',
                'email' => '<EMAIL>',
                'category_slug' => 'hotels',
                'price' => 150,
            ],
            [
                'title_ar' => 'شركة سياحة الجزيرة العربية - رحلات منظمة',
                'title_en' => 'Arabian Peninsula Tourism - Organized Tours',
                'description_ar' => 'شركة سياحة الجزيرة العربية تنظم رحلات سياحية داخلية وخارجية، زيارة المعالم التاريخية، الشواطئ، الجبال، حجوزات فنادق، تأشيرات.',
                'description_en' => 'Arabian Peninsula Tourism organizes domestic and international tours, historical sites visits, beaches, mountains, hotel bookings, visas.',
                'location' => 'الحديدة، اليمن',
                'phone' => '+967-3-222002',
                'email' => '<EMAIL>',
                'category_slug' => 'hotels',
                'price' => 500,
            ],

            // إعلانات إضافية للفئات الأساسية
            [
                'title_ar' => 'لابتوب ديل جديد بالكرتونة',
                'title_en' => 'New Dell Laptop in Box',
                'description_ar' => 'لابتوب ديل جديد بالكرتونة، معالج Intel Core i7، ذاكرة 16 جيجا، قرص صلب SSD 512 جيجا، كارت شاشة منفصل، ضمان سنتين.',
                'description_en' => 'Brand new Dell laptop in box, Intel Core i7 processor, 16GB RAM, 512GB SSD, dedicated graphics card, 2 years warranty.',
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-1-678901',
                'email' => '<EMAIL>',
                'category_slug' => 'electronics',
                'price' => 2800,
            ],
            [
                'title_ar' => 'طقم أثاث غرفة نوم كامل',
                'title_en' => 'Complete Bedroom Furniture Set',
                'description_ar' => 'طقم أثاث كامل لغرفة النوم، خشب طبيعي عالي الجودة، يشمل: سرير مزدوج، دولاب كبير، تسريحة مع مرآة، كومودينو، تصميم عصري.',
                'description_en' => 'Complete bedroom furniture set, high-quality natural wood, includes: double bed, large wardrobe, dresser with mirror, nightstand, modern design.',
                'location' => 'تعز، اليمن',
                'phone' => '+967-4-890123',
                'email' => '<EMAIL>',
                'category_slug' => 'furniture',
                'price' => 1200,
            ],
            [
                'title_ar' => 'مطلوب مطور ويب محترف',
                'title_en' => 'Professional Web Developer Needed',
                'description_ar' => 'نبحث عن مطور ويب محترف للعمل في شركة تقنية رائدة، خبرة في Laravel وReact وVue.js، راتب مجزي، بيئة عمل ممتازة، تأمين صحي.',
                'description_en' => 'Looking for professional web developer to work in leading tech company, experience in Laravel, React and Vue.js, good salary, excellent work environment, health insurance.',
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-1-901234',
                'email' => '<EMAIL>',
                'category_slug' => 'jobs',
                'price' => 2500,
            ],
            [
                'title_ar' => 'خدمات صيانة وإصلاح الأجهزة',
                'title_en' => 'Device Maintenance and Repair Services',
                'description_ar' => 'نقدم خدمات صيانة وإصلاح جميع أنواع الأجهزة الإلكترونية، هواتف، لابتوب، أجهزة منزلية، خبرة 10 سنوات، ضمان على الإصلاح.',
                'description_en' => 'We provide maintenance and repair services for all types of electronic devices, phones, laptops, home appliances, 10 years experience, repair warranty.',
                'location' => 'الحديدة، اليمن',
                'phone' => '+967-3-012345',
                'email' => '<EMAIL>',
                'category_slug' => 'services',
                'price' => 50,
            ],
            [
                'title_ar' => 'فستان سهرة أنيق للبيع',
                'title_en' => 'Elegant Evening Dress for Sale',
                'description_ar' => 'فستان سهرة أنيق، مقاس متوسط، لون أزرق داكن، مستعمل مرة واحدة فقط، تصميم راقي، مناسب للمناسبات الخاصة.',
                'description_en' => 'Elegant evening dress, medium size, dark blue color, used only once, sophisticated design, suitable for special occasions.',
                'location' => 'عدن، اليمن',
                'phone' => '+967-2-123456',
                'email' => '<EMAIL>',
                'category_slug' => 'fashion',
                'price' => 150,
            ],
            [
                'title_ar' => 'كتاب البرمجة الحديثة - نسخة أصلية',
                'title_en' => 'Modern Programming Book - Original Copy',
                'description_ar' => 'كتاب البرمجة الحديثة باللغة العربية، يشرح أحدث تقنيات البرمجة، PHP، JavaScript، Python، أمثلة عملية، نسخة أصلية جديدة.',
                'description_en' => 'Modern Programming book in Arabic, explains latest programming technologies, PHP, JavaScript, Python, practical examples, original new copy.',
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-1-333001',
                'email' => '<EMAIL>',
                'category_slug' => 'books',
                'price' => 80,
            ],
            [
                'title_ar' => 'كريم العناية بالبشرة الطبيعي',
                'title_en' => 'Natural Skin Care Cream',
                'description_ar' => 'كريم العناية بالبشرة الطبيعي، مكونات طبيعية 100%، مناسب لجميع أنواع البشرة، مرطب ومغذي، خالي من المواد الكيميائية الضارة.',
                'description_en' => 'Natural skin care cream, 100% natural ingredients, suitable for all skin types, moisturizing and nourishing, free from harmful chemicals.',
                'location' => 'تعز، اليمن',
                'phone' => '+967-4-444001',
                'email' => '<EMAIL>',
                'category_slug' => 'health',
                'price' => 45,
            ],
            [
                'title_ar' => 'كرة قدم أديداس أصلية',
                'title_en' => 'Original Adidas Football',
                'description_ar' => 'كرة قدم أديداس أصلية، مقاس 5، جلد طبيعي، مناسبة للملاعب العشبية والترابية، جودة عالية، مستعملة قليلاً.',
                'description_en' => 'Original Adidas football, size 5, genuine leather, suitable for grass and dirt fields, high quality, lightly used.',
                'location' => 'عدن، اليمن',
                'phone' => '+967-2-555001',
                'email' => '<EMAIL>',
                'category_slug' => 'sports',
                'price' => 120,
            ],
        ];

        $createdCount = 0;
        foreach ($testAds as $adData) {
            $category = $categories->where('slug', $adData['category_slug'])->first();
            $user = $users->random();

            if (!$category) {
                continue;
            }

            $sanitizedData = [
                'user_id' => $user->id,
                'category_id' => $category->id,
                'title_ar' => InputSanitizationService::sanitizeText($adData['title_ar']),
                'title_en' => InputSanitizationService::sanitizeText($adData['title_en']),
                'description_ar' => InputSanitizationService::sanitizeRichText($adData['description_ar']),
                'description_en' => InputSanitizationService::sanitizeRichText($adData['description_en']),
                'location' => InputSanitizationService::sanitizeText($adData['location']),
                'phone' => InputSanitizationService::sanitizePhone($adData['phone']),
                'email' => InputSanitizationService::sanitizeEmail($adData['email']),
                'price' => $adData['price'] ?? null,
                'status' => 'active',
                'slug' => Str::slug($adData['title_en']) . '-' . time() . '-' . rand(1000, 9999),
                'expires_at' => now()->addDays(30),
                'views_count' => rand(10, 150),
                'is_featured' => rand(0, 1) == 1,
                'created_at' => now()->subDays(rand(1, 15)),
                'updated_at' => now(),
            ];

            Ad::create($sanitizedData);
            $createdCount++;
        }

        $this->command->info('✅ تم إنشاء ' . $createdCount . ' إعلان تجريبي');
    }

    /**
     * إنشاء كلمة مرور قوية
     */
    private function generateSecurePassword(): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*';

        $password = '';
        $password .= $uppercase[rand(0, strlen($uppercase) - 1)];
        $password .= $lowercase[rand(0, strlen($lowercase) - 1)];
        $password .= $numbers[rand(0, strlen($numbers) - 1)];
        $password .= $symbols[rand(0, strlen($symbols) - 1)];

        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < 12; $i++) {
            $password .= $allChars[rand(0, strlen($allChars) - 1)];
        }

        return str_shuffle($password);
    }

    /**
     * إنشاء إعلانات الأسعار المتقدمة التجريبية
     */
    private function createAdvancedPricingTestAds(): void
    {
        $this->command->info('💰 إنشاء إعلانات الأسعار المتقدمة...');

        $users = User::where('is_admin', false)->get();
        $categories = Category::where('is_active', true)->get();

        if ($users->isEmpty() || $categories->isEmpty()) {
            $this->command->warn('⚠️  لا توجد مستخدمين أو فئات لإنشاء الإعلانات');
            return;
        }

        // 1. إعلانات مجانية
        $freeAds = [
            [
                'title_ar' => 'كتب مجانية للطلاب',
                'title_en' => 'Free Books for Students',
                'description_ar' => 'مجموعة كتب دراسية مجانية للطلاب المحتاجين',
                'description_en' => 'Collection of free study books for needy students',
                'category_slug' => 'books',
                'price_type' => 'free',
                'is_free' => true,
            ],
            [
                'title_ar' => 'دورة تدريبية مجانية في البرمجة',
                'title_en' => 'Free Programming Training Course',
                'description_ar' => 'دورة مجانية لتعلم أساسيات البرمجة',
                'description_en' => 'Free course to learn programming basics',
                'category_slug' => 'services',
                'price_type' => 'free',
                'is_free' => true,
            ],
            [
                'title_ar' => 'استشارة طبية مجانية',
                'title_en' => 'Free Medical Consultation',
                'description_ar' => 'استشارة طبية مجانية عبر الهاتف',
                'description_en' => 'Free medical consultation via phone',
                'category_slug' => 'hospitals',
                'price_type' => 'free',
                'is_free' => true,
            ]
        ];

        // 2. إعلانات بأسعار ثابتة بعملات مختلفة
        $fixedPriceAds = [
            [
                'title_ar' => 'لابتوب ديل مستعمل',
                'title_en' => 'Used Dell Laptop',
                'description_ar' => 'لابتوب ديل بحالة ممتازة، مناسب للطلاب والعمل',
                'description_en' => 'Dell laptop in excellent condition, suitable for students and work',
                'category_slug' => 'electronics',
                'price' => 150000,
                'currency' => 'YER',
                'price_type' => 'fixed',
            ],
            [
                'title_ar' => 'هاتف آيفون 13',
                'title_en' => 'iPhone 13',
                'description_ar' => 'هاتف آيفون 13 جديد بالكرتونة',
                'description_en' => 'New iPhone 13 in box',
                'category_slug' => 'electronics',
                'price' => 800,
                'currency' => 'USD',
                'price_type' => 'fixed',
            ],
            [
                'title_ar' => 'سيارة تويوتا كامري 2020',
                'title_en' => 'Toyota Camry 2020',
                'description_ar' => 'سيارة تويوتا كامري موديل 2020 بحالة ممتازة',
                'description_en' => 'Toyota Camry 2020 model in excellent condition',
                'category_slug' => 'cars',
                'price' => 85000,
                'currency' => 'SAR',
                'price_type' => 'fixed',
            ],
            [
                'title_ar' => 'شقة للإيجار في دبي',
                'title_en' => 'Apartment for Rent in Dubai',
                'description_ar' => 'شقة مفروشة للإيجار في دبي، غرفتين وصالة',
                'description_en' => 'Furnished apartment for rent in Dubai, 2 bedrooms and living room',
                'category_slug' => 'real-estate',
                'price' => 4500,
                'currency' => 'AED',
                'price_type' => 'fixed',
            ]
        ];

        // 3. إعلانات مع خصومات
        $discountedAds = [
            [
                'title_ar' => 'جهاز كمبيوتر مكتبي - عرض خاص',
                'title_en' => 'Desktop Computer - Special Offer',
                'description_ar' => 'جهاز كمبيوتر مكتبي بمواصفات عالية مع خصم 25%',
                'description_en' => 'High-spec desktop computer with 25% discount',
                'category_slug' => 'electronics',
                'original_price' => 200000,
                'price' => 150000,
                'currency' => 'YER',
                'price_type' => 'fixed',
                'discount_expires_at' => now()->addDays(30),
            ],
            [
                'title_ar' => 'ساعة ذكية - خصم كبير',
                'title_en' => 'Smart Watch - Big Discount',
                'description_ar' => 'ساعة ذكية بخصم 40% لفترة محدودة',
                'description_en' => 'Smart watch with 40% discount for limited time',
                'category_slug' => 'electronics',
                'original_price' => 500,
                'price' => 300,
                'currency' => 'USD',
                'price_type' => 'fixed',
                'discount_expires_at' => now()->addDays(15),
            ],
            [
                'title_ar' => 'دراجة هوائية - تخفيض نهاية الموسم',
                'title_en' => 'Bicycle - End of Season Sale',
                'description_ar' => 'دراجة هوائية جبلية بخصم 30%',
                'description_en' => 'Mountain bicycle with 30% discount',
                'category_slug' => 'sports',
                'original_price' => 2000,
                'price' => 1400,
                'currency' => 'SAR',
                'price_type' => 'fixed',
                'discount_expires_at' => now()->addDays(20),
            ]
        ];

        // 4. إعلانات قابلة للتفاوض
        $negotiableAds = [
            [
                'title_ar' => 'أرض للبيع - قابل للتفاوض',
                'title_en' => 'Land for Sale - Negotiable',
                'description_ar' => 'قطعة أرض في موقع ممتاز، السعر قابل للتفاوض',
                'description_en' => 'Land plot in excellent location, price negotiable',
                'category_slug' => 'real-estate',
                'price' => 500000,
                'currency' => 'YER',
                'price_type' => 'negotiable',
                'is_negotiable' => true,
                'price_notes' => 'السعر قابل للتفاوض حسب طريقة الدفع',
            ],
            [
                'title_ar' => 'معدات مطعم - سعر قابل للتفاوض',
                'title_en' => 'Restaurant Equipment - Negotiable Price',
                'description_ar' => 'معدات مطعم كاملة، السعر قابل للتفاوض حسب الكمية',
                'description_en' => 'Complete restaurant equipment, price negotiable based on quantity',
                'category_slug' => 'companies',
                'price' => 15000,
                'currency' => 'USD',
                'price_type' => 'negotiable',
                'is_negotiable' => true,
                'price_notes' => 'خصم خاص للكميات الكبيرة',
            ]
        ];

        // 5. إعلانات "السعر عند الطلب"
        $onRequestAds = [
            [
                'title_ar' => 'خدمات تصميم مواقع',
                'title_en' => 'Website Design Services',
                'description_ar' => 'خدمات تصميم وتطوير المواقع الإلكترونية',
                'description_en' => 'Website design and development services',
                'category_slug' => 'services',
                'price_type' => 'on_request',
                'currency' => 'YER',
                'price_notes' => 'يتم تحديد السعر حسب نوع وحجم المشروع',
            ],
            [
                'title_ar' => 'استشارات قانونية',
                'title_en' => 'Legal Consultations',
                'description_ar' => 'استشارات قانونية في جميع المجالات',
                'description_en' => 'Legal consultations in all fields',
                'category_slug' => 'services',
                'price_type' => 'on_request',
                'currency' => 'YER',
                'price_notes' => 'أسعار تنافسية حسب نوع الاستشارة',
            ]
        ];

        // 6. عروض محدودة
        $limitedOffers = [
            [
                'title_ar' => 'عرض محدود - جهاز تابلت',
                'title_en' => 'Limited Offer - Tablet Device',
                'description_ar' => 'جهاز تابلت بعرض محدود لمدة أسبوع فقط',
                'description_en' => 'Tablet device with limited offer for one week only',
                'category_slug' => 'electronics',
                'original_price' => 300,
                'price' => 200,
                'currency' => 'USD',
                'price_type' => 'fixed',
                'is_limited_offer' => true,
                'discount_expires_at' => now()->addDays(7),
                'price_notes' => 'عرض محدود لمدة أسبوع فقط!',
            ],
            [
                'title_ar' => 'تخفيضات نهاية السنة - ملابس',
                'title_en' => 'End of Year Sale - Clothing',
                'description_ar' => 'تشكيلة ملابس بتخفيضات تصل إلى 50%',
                'description_en' => 'Clothing collection with discounts up to 50%',
                'category_slug' => 'fashion',
                'original_price' => 100000,
                'price' => 50000,
                'currency' => 'YER',
                'price_type' => 'fixed',
                'is_limited_offer' => true,
                'discount_expires_at' => now()->addDays(10),
                'price_notes' => 'تخفيضات نهاية السنة - كميات محدودة',
            ]
        ];

        // دمج جميع الإعلانات
        $allAdvancedAds = array_merge($freeAds, $fixedPriceAds, $discountedAds, $negotiableAds, $onRequestAds, $limitedOffers);

        $createdCount = 0;
        foreach ($allAdvancedAds as $adData) {
            $category = $categories->where('slug', $adData['category_slug'])->first();
            $user = $users->random();

            if (!$category) {
                continue;
            }

            $sanitizedData = [
                'user_id' => $user->id,
                'category_id' => $category->id,
                'title_ar' => InputSanitizationService::sanitizeText($adData['title_ar']),
                'title_en' => InputSanitizationService::sanitizeText($adData['title_en']),
                'description_ar' => InputSanitizationService::sanitizeRichText($adData['description_ar']),
                'description_en' => InputSanitizationService::sanitizeRichText($adData['description_en']),
                'location' => 'صنعاء، اليمن',
                'phone' => '+967-777-123456',
                'email' => $user->email,
                'status' => 'active',
                'slug' => Str::slug($adData['title_en']) . '-' . time() . '-' . rand(1000, 9999),
                'expires_at' => now()->addMonths(3),
                'views_count' => rand(5, 100),
                'is_featured' => rand(0, 1) == 1,
                'created_at' => now()->subDays(rand(1, 10)),
                'updated_at' => now(),
                // حقول الأسعار المتقدمة
                'price' => $adData['price'] ?? null,
                'original_price' => $adData['original_price'] ?? null,
                'currency' => $adData['currency'] ?? 'YER',
                'price_type' => $adData['price_type'] ?? 'fixed',
                'is_free' => $adData['is_free'] ?? false,
                'is_negotiable' => $adData['is_negotiable'] ?? false,
                'is_limited_offer' => $adData['is_limited_offer'] ?? false,
                'discount_expires_at' => $adData['discount_expires_at'] ?? null,
                'price_notes' => $adData['price_notes'] ?? null,
            ];

            Ad::create($sanitizedData);
            $createdCount++;
        }

        $this->command->info('✅ تم إنشاء ' . $createdCount . ' إعلان بأسعار متقدمة');
    }

    /**
     * إنشاء معلومات التواصل للمستخدمين
     */
    private function createUserContacts(): void
    {
        $this->command->info('📞 إنشاء معلومات التواصل للمستخدمين...');

        // الحصول على جميع المستخدمين
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->warn('⚠️  لا توجد مستخدمين في قاعدة البيانات');
            return;
        }

        // بيانات تجريبية متنوعة لمعلومات التواصل
        $contactTemplates = [
            [
                'contact_type' => 'phone',
                'is_primary' => true,
                'is_public' => true,
                'privacy_level' => 'public',
                'display_order' => 1,
                'display_label' => 'هاتف شخصي',
            ],
            [
                'contact_type' => 'whatsapp',
                'is_primary' => false,
                'is_public' => true,
                'privacy_level' => 'public',
                'display_order' => 2,
                'display_label' => 'واتساب',
            ],
            [
                'contact_type' => 'email',
                'is_primary' => true,
                'is_public' => true,
                'privacy_level' => 'registered_users',
                'display_order' => 3,
                'display_label' => 'إيميل العمل',
            ],
            [
                'contact_type' => 'telegram',
                'is_primary' => false,
                'is_public' => true,
                'privacy_level' => 'public',
                'display_order' => 4,
                'display_label' => 'تليجرام',
            ],
            [
                'contact_type' => 'instagram',
                'is_primary' => false,
                'is_public' => true,
                'privacy_level' => 'public',
                'display_order' => 5,
                'display_label' => 'إنستجرام',
            ],
            [
                'contact_type' => 'facebook',
                'is_primary' => false,
                'is_public' => true,
                'privacy_level' => 'public',
                'display_order' => 6,
                'display_label' => 'فيسبوك',
            ],
            [
                'contact_type' => 'twitter',
                'is_primary' => false,
                'is_public' => false,
                'privacy_level' => 'registered_users',
                'display_order' => 7,
                'display_label' => 'تويتر',
            ],
            [
                'contact_type' => 'linkedin',
                'is_primary' => false,
                'is_public' => false,
                'privacy_level' => 'private',
                'display_order' => 8,
                'display_label' => 'لينكد إن',
            ],
            [
                'contact_type' => 'website',
                'is_primary' => false,
                'is_public' => true,
                'privacy_level' => 'public',
                'display_order' => 9,
                'display_label' => 'موقع شخصي',
            ],
        ];

        $createdCount = 0;

        // إضافة معلومات تواصل لكل مستخدم
        foreach ($users as $index => $user) {
            // تحديد عدد معلومات التواصل لكل مستخدم (3-7 معلومات)
            $contactsToCreate = rand(3, 7);
            $selectedTemplates = array_slice($contactTemplates, 0, $contactsToCreate);

            foreach ($selectedTemplates as $template) {
                // تخصيص البيانات لكل مستخدم
                $contactData = $template;
                $contactData['user_id'] = $user->id;

                // تخصيص القيم حسب المستخدم ونوع التواصل
                $contactData['contact_value'] = $this->generateContactValue(
                    $template['contact_type'],
                    $user,
                    $index
                );

                // تحديد التحقق عشوائياً (70% محقق)
                $contactData['is_verified'] = rand(1, 10) <= 7;

                // تنويع إعدادات الخصوصية
                if (rand(1, 10) <= 3) { // 30% خاص
                    $contactData['privacy_level'] = 'private';
                    $contactData['is_public'] = false;
                } elseif (rand(1, 10) <= 6) { // 30% للمسجلين فقط
                    $contactData['privacy_level'] = 'registered_users';
                }

                UserContact::create($contactData);
                $createdCount++;
            }

            $this->command->info("✅ تم إنشاء معلومات التواصل للمستخدم: {$user->name}");
        }

        $this->command->info("🎉 تم إنشاء {$createdCount} معلومة تواصل لـ " . $users->count() . " مستخدم");
    }

    /**
     * توليد قيمة معلومة التواصل حسب النوع
     */
    private function generateContactValue(string $type, User $user, int $index): string
    {
        $userNumber = $index + 1;

        switch ($type) {
            case 'phone':
                return '+96777' . str_pad($userNumber, 7, '0', STR_PAD_LEFT);

            case 'whatsapp':
                return '+96777' . str_pad($userNumber + 1000, 7, '0', STR_PAD_LEFT);

            case 'email':
                $username = strtolower(str_replace(' ', '.', $user->name));
                return $username . $userNumber . '@example.com';

            case 'telegram':
                return '@user' . $userNumber . '_telegram';

            case 'instagram':
                return '@user' . $userNumber . '_insta';

            case 'facebook':
                return 'facebook.com/user' . $userNumber;

            case 'twitter':
                return '@user' . $userNumber . '_twitter';

            case 'linkedin':
                return 'linkedin.com/in/user' . $userNumber;

            case 'website':
                return 'https://user' . $userNumber . '.example.com';

            default:
                return 'contact_' . $userNumber;
        }
    }

    /**
     * إنشاء إعدادات الخصوصية للمستخدمين
     */
    private function createUserPrivacySettings(): void
    {
        $this->command->info('🔐 إنشاء إعدادات الخصوصية الافتراضية...');

        $usersWithoutSettings = User::whereDoesntHave('privacySettings')->get();

        if ($usersWithoutSettings->isEmpty()) {
            $this->command->info('✅ جميع المستخدمين لديهم إعدادات خصوصية بالفعل');
            return;
        }

        $createdCount = 0;

        foreach ($usersWithoutSettings as $user) {
            $settings = $this->getCustomSettingsForUser($user);

            UserPrivacySetting::create(array_merge(
                ['user_id' => $user->id],
                $settings
            ));

            $createdCount++;
            $this->command->info("✅ تم إنشاء إعدادات الخصوصية للمستخدم: {$user->name}");
        }

        $this->command->info("🎉 تم إنشاء إعدادات الخصوصية لـ {$createdCount} مستخدم");
    }

    /**
     * الحصول على إعدادات مخصصة حسب نوع المستخدم
     */
    private function getCustomSettingsForUser(User $user): array
    {
        $baseSettings = UserPrivacySetting::getDefaultSettings();

        if ($user->is_admin) {
            return array_merge($baseSettings, [
                'show_contacts_to_guests' => true,
                'show_contacts_to_registered' => true,
                'require_verification_to_view' => false,
                'enable_contact_rate_limiting' => false,
                'max_contact_views_per_hour' => 100,
                'max_contact_views_per_day' => 1000,
                'notify_on_contact_view' => true,
                'notify_on_suspicious_activity' => true,
                'track_contact_interactions' => true,
                'log_contact_access' => true,
                'enable_analytics' => true,
            ]);
        }

        $variations = [
            [
                'show_contacts_to_guests' => false,
                'show_contacts_to_registered' => true,
                'require_verification_to_view' => true,
                'enable_contact_rate_limiting' => true,
                'max_contact_views_per_hour' => 5,
                'max_contact_views_per_day' => 20,
                'require_phone_verification' => true,
                'require_email_verification' => true,
                'auto_hide_unverified_contacts' => true,
                'notify_on_contact_view' => true,
                'notify_on_suspicious_activity' => true,
                'enable_contact_encryption' => true,
            ],
            [
                'show_contacts_to_guests' => true,
                'show_contacts_to_registered' => true,
                'require_verification_to_view' => false,
                'enable_contact_rate_limiting' => true,
                'max_contact_views_per_hour' => 10,
                'max_contact_views_per_day' => 50,
                'require_phone_verification' => false,
                'require_email_verification' => false,
                'auto_hide_unverified_contacts' => false,
                'notify_on_contact_view' => false,
                'notify_on_suspicious_activity' => true,
                'enable_contact_encryption' => false,
            ],
            [
                'show_contacts_to_guests' => true,
                'show_contacts_to_registered' => true,
                'require_verification_to_view' => false,
                'enable_contact_rate_limiting' => true,
                'max_contact_views_per_hour' => 20,
                'max_contact_views_per_day' => 100,
                'require_phone_verification' => false,
                'require_email_verification' => false,
                'auto_hide_unverified_contacts' => false,
                'notify_on_contact_view' => false,
                'notify_on_suspicious_activity' => true,
                'allow_contact_sharing' => true,
                'allow_contact_export' => true,
                'show_contact_qr_code' => true,
                'track_contact_interactions' => true,
                'enable_analytics' => true,
            ]
        ];

        $selectedVariation = $variations[array_rand($variations)];
        return array_merge($baseSettings, $selectedVariation);
    }
}
