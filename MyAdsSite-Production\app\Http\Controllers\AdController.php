<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Ad;
use App\Models\Category;
use App\Http\Controllers\UserDashboardController;
use App\Services\FileSecurityService;

/**
 * كونترولر الإعلانات
 * يدير عرض الإعلانات وتفاصيلها والبحث فيها
 */
class AdController extends Controller
{
    /**
     * عرض جميع الإعلانات
     */
    public function index(Request $request)
    {
        // بناء الاستعلام الأساسي
        $query = Ad::active()->notExpired()->with('category');

        // تطبيق التصفية حسب التصنيف
        if ($request->has('category') && !empty($request->category)) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // تطبيق التصفية حسب الموقع
        if ($request->has('location') && !empty($request->location)) {
            $query->where('location', 'LIKE', "%{$request->location}%");
        }

        // تطبيق البحث النصي
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('title_en', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_en', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('phone', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('location', 'LIKE', "%{$searchTerm}%");
            });
        }

        // ترتيب النتائج
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'popular':
                $query->orderBy('views_count', 'desc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            default: // latest
                $query->orderBy('created_at', 'desc');
                break;
        }

        // جلب النتائج مع التصفح
        $ads = $query->paginate(12)->withQueryString();

        // جلب التصنيفات للفلتر مع عدد الإعلانات
        $categories = Category::active()
            ->withCount(['ads' => function ($query) {
                $query->active()->notExpired();
            }])
            ->orderBy('name_ar')
            ->get();

        return view('ads.index', compact('ads', 'categories'));
    }

    /**
     * عرض إعلان محدد
     */
    public function show($categorySlug, $adSlug)
    {
        // البحث عن الإعلان مع التصنيف
        $ad = Ad::active()
            ->notExpired()
            ->where('slug', $adSlug)
            ->whereHas('category', function ($query) use ($categorySlug) {
                $query->where('slug', $categorySlug);
            })
            ->with('category')
            ->firstOrFail();

        // زيادة عدد المشاهدات
        $ad->incrementViews();

        // جلب إعلانات مشابهة من نفس التصنيف
        $relatedAds = Ad::active()
            ->notExpired()
            ->where('category_id', $ad->category_id)
            ->where('id', '!=', $ad->id)
            ->orderBy('created_at', 'desc')
            ->limit(4)
            ->get();

        // جلب الإعلان السابق والتالي للتنقل
        $previousAd = Ad::active()
            ->notExpired()
            ->where('category_id', $ad->category_id)
            ->where('id', '<', $ad->id)
            ->orderBy('id', 'desc')
            ->first();

        $nextAd = Ad::active()
            ->notExpired()
            ->where('category_id', $ad->category_id)
            ->where('id', '>', $ad->id)
            ->orderBy('id', 'asc')
            ->first();

        return view('ads.show', compact('ad', 'relatedAds', 'previousAd', 'nextAd'));
    }

    /**
     * البحث في الإعلانات
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (empty($query)) {
            return redirect()->route('ads.index');
        }

        // البحث في الإعلانات
        $ads = Ad::active()
            ->notExpired()
            ->where(function ($q) use ($query) {
                $q->where('title_ar', 'LIKE', "%{$query}%")
                  ->orWhere('title_en', 'LIKE', "%{$query}%")
                  ->orWhere('description_ar', 'LIKE', "%{$query}%")
                  ->orWhere('description_en', 'LIKE', "%{$query}%")
                  ->orWhere('phone', 'LIKE', "%{$query}%")
                  ->orWhere('location', 'LIKE', "%{$query}%");
            })
            ->with('category')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('ads.search', compact('ads', 'query'));
    }

    /**
     * عرض الإعلانات حسب التصنيف
     */
    public function byCategory($categorySlug, Request $request)
    {
        // البحث عن التصنيف
        $category = Category::active()
            ->where('slug', $categorySlug)
            ->firstOrFail();

        // جلب إعلانات التصنيف
        $query = $category->ads()->active()->notExpired();

        // تطبيق البحث إذا وجد
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('title_en', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_en', 'LIKE', "%{$searchTerm}%");
            });
        }

        // تطبيق التصفية حسب الموقع
        if ($request->has('location') && !empty($request->location)) {
            $query->where('location', 'LIKE', "%{$request->location}%");
        }

        $ads = $query->orderBy('created_at', 'desc')->paginate(12)->withQueryString();

        return view('ads.category', compact('category', 'ads'));
    }

    /**
     * عرض نموذج إنشاء إعلان جديد
     * تم نقل هذه الوظيفة إلى UserDashboardController::createAd()
     * هذا Method محتفظ به للتوافق مع الروابط القديمة
     */
    public function create()
    {
        // إعادة توجيه إلى dashboard
        return redirect()->route('dashboard.create-ad');
    }

    /**
     * حفظ إعلان جديد
     * تم نقل هذه الوظيفة إلى UserDashboardController::storeAd()
     * هذا Method محتفظ به للتوافق مع النماذج القديمة
     */
    public function store(Request $request)
    {
        // إعادة توجيه إلى dashboard مع البيانات
        return app(UserDashboardController::class)->storeAd($request);
    }

    /**
     * عرض نموذج تعديل الإعلان
     * تم نقل هذه الوظيفة إلى UserDashboardController::editAd()
     */
    public function edit(Ad $ad)
    {
        // إعادة توجيه إلى dashboard
        return redirect()->route('dashboard.edit-ad', $ad);
    }

    /**
     * تحديث الإعلان
     * تم نقل هذه الوظيفة إلى UserDashboardController::updateAd()
     */
    public function update(Request $request, Ad $ad)
    {
        // إعادة توجيه إلى dashboard
        return app(UserDashboardController::class)->updateAd($request, $ad);
    }

    /**
     * حذف الإعلان
     * تم نقل هذه الوظيفة إلى UserDashboardController::deleteAd()
     */
    public function destroy(Ad $ad)
    {
        // إعادة توجيه إلى dashboard
        return app(UserDashboardController::class)->deleteAd($ad);
    }

    /**
     * رفع الصورة وحفظها بشكل آمن
     */
    private function uploadImage($image)
    {
        // فحص أمان الملف
        $validation = FileSecurityService::validateUploadedFile($image);

        if (!$validation['valid']) {
            throw new \Exception(__('File is not secure: :errors', ['errors' => implode(', ', $validation['errors'])]));
        }

        // حفظ الملف بشكل آمن
        $result = FileSecurityService::secureStore($image, 'ads');

        if (!$result['success']) {
            throw new \Exception(__('Failed to save file: :errors', ['errors' => implode(', ', $result['errors'])]));
        }

        return $result['path'];
    }

    /**
     * عرض إعلانات المستخدم
     */
    public function myAds()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $ads = Auth::user()->ads()
            ->with('category')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('ads.my-ads', compact('ads'));
    }
}
