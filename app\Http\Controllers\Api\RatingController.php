<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\Rating;
use App\Models\Ad;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * كونترولر API للتقييمات
 * يدير إضافة وعرض وتحديث تقييمات الإعلانات
 */
class RatingController extends Controller
{
    /**
     * إضافة أو تحديث تقييم
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // التحقق من تسجيل الدخول
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You must be logged in to rate ads')
                ], 401);
            }

            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'ad_id' => 'required|exists:ads,id',
                'rating' => 'required|integer|min:1|max:5',
                'comment' => 'nullable|string|max:1000',
                'quality_rating' => 'nullable|in:excellent,good,average,poor',
                'price_rating' => 'nullable|in:excellent,good,average,poor',
                'service_rating' => 'nullable|in:excellent,good,average,poor',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => __('Invalid data provided'),
                    'errors' => $validator->errors()
                ], 422);
            }

            $userId = Auth::id();
            $adId = $request->ad_id;

            // التحقق من وجود الإعلان وأنه نشط
            $ad = Ad::find($adId);
            if (!$ad || $ad->status !== 'active') {
                return response()->json([
                    'success' => false,
                    'message' => __('Ad not found or not active')
                ], 404);
            }

            // منع المستخدم من تقييم إعلانه الخاص
            if ($ad->user_id === $userId) {
                return response()->json([
                    'success' => false,
                    'message' => __('You cannot rate your own ad')
                ], 403);
            }

            // إضافة أو تحديث التقييم
            $ratingData = [
                'rating' => $request->rating,
                'comment' => $request->comment,
                'quality_rating' => $request->quality_rating,
                'price_rating' => $request->price_rating,
                'service_rating' => $request->service_rating,
                'interaction_data' => [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'timestamp' => now()->toISOString()
                ]
            ];

            $rating = Rating::addOrUpdateRating($userId, $adId, $ratingData);

            // تسجيل الحدث
            Log::info('Rating added/updated', [
                'user_id' => $userId,
                'ad_id' => $adId,
                'rating' => $request->rating,
                'is_update' => $rating->wasRecentlyCreated ? false : true
            ]);

            return response()->json([
                'success' => true,
                'message' => $rating->wasRecentlyCreated ?
                    __('Rating added successfully') :
                    __('Rating updated successfully'),
                'rating' => [
                    'id' => $rating->id,
                    'rating' => $rating->rating,
                    'comment' => $rating->comment,
                    'created_at' => $rating->created_at->diffForHumans()
                ],
                'ad_stats' => Rating::getAdRatingStats($adId)
            ]);

        } catch (\Exception $e) {
            Log::error('Error adding/updating rating: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * الحصول على تقييمات إعلان معين
     */
    public function show(Request $request, int $adId): JsonResponse
    {
        try {
            // التحقق من وجود الإعلان
            $ad = Ad::find($adId);
            if (!$ad) {
                return response()->json([
                    'success' => false,
                    'message' => __('Ad not found')
                ], 404);
            }

            $perPage = $request->get('per_page', 10);
            $sortBy = $request->get('sort_by', 'latest'); // latest, oldest, highest, lowest, helpful

            // بناء الاستعلام
            $query = Rating::byAd($adId)->with('user:id,name');

            // ترتيب النتائج
            switch ($sortBy) {
                case 'oldest':
                    $query->oldest();
                    break;
                case 'highest':
                    $query->highestRated();
                    break;
                case 'lowest':
                    $query->lowestRated();
                    break;
                case 'helpful':
                    $query->mostHelpful();
                    break;
                default:
                    $query->latest();
            }

            $ratings = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'ratings' => $ratings,
                'stats' => Rating::getAdRatingStats($adId)
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching ratings: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * الحصول على تقييم المستخدم لإعلان معين
     */
    public function getUserRating(int $adId): JsonResponse
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You must be logged in')
                ], 401);
            }

            $userId = Auth::id();
            $rating = Rating::getUserRating($userId, $adId);

            return response()->json([
                'success' => true,
                'has_rated' => $rating !== null,
                'rating' => $rating ? [
                    'id' => $rating->id,
                    'rating' => $rating->rating,
                    'comment' => $rating->comment,
                    'quality_rating' => $rating->quality_rating,
                    'price_rating' => $rating->price_rating,
                    'service_rating' => $rating->service_rating,
                    'created_at' => $rating->created_at->diffForHumans(),
                    'updated_at' => $rating->updated_at->diffForHumans()
                ] : null
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching user rating: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * حذف تقييم المستخدم
     */
    public function destroy(int $adId): JsonResponse
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You must be logged in')
                ], 401);
            }

            $userId = Auth::id();
            $rating = Rating::getUserRating($userId, $adId);

            if (!$rating) {
                return response()->json([
                    'success' => false,
                    'message' => __('Rating not found')
                ], 404);
            }

            $rating->delete();

            Log::info('Rating deleted', [
                'user_id' => $userId,
                'ad_id' => $adId,
                'rating_id' => $rating->id
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Rating deleted successfully'),
                'ad_stats' => Rating::getAdRatingStats($adId)
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting rating: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * تحديث مفيدية التقييم
     */
    public function toggleHelpful(Request $request, int $ratingId): JsonResponse
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You must be logged in')
                ], 401);
            }

            $rating = Rating::find($ratingId);
            if (!$rating) {
                return response()->json([
                    'success' => false,
                    'message' => __('Rating not found')
                ], 404);
            }

            // منع المستخدم من تقييم تقييمه الخاص
            if ($rating->user_id === Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You cannot rate your own review')
                ], 403);
            }

            // تبديل حالة المفيدية (يمكن تطوير نظام أكثر تعقيداً لاحقاً)
            $rating->helpful_count += $request->helpful ? 1 : -1;
            $rating->helpful_count = max(0, $rating->helpful_count); // منع القيم السالبة
            $rating->save();

            return response()->json([
                'success' => true,
                'message' => __('Thank you for your feedback'),
                'helpful_count' => $rating->helpful_count
            ]);

        } catch (\Exception $e) {
            Log::error('Error toggling rating helpfulness: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }
}
