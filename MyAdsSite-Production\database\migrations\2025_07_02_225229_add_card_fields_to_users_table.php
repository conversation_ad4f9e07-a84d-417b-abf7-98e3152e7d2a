<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // إضافة حقول معلومات البطاقة لنظام الدفع
            $table->string('card_number')->nullable()->after('email'); // رقم البطاقة
            $table->string('card_type')->nullable()->after('card_number'); // نوع البطاقة (كاك بنك، الكريمي، إلخ)
            $table->enum('currency', ['YER', 'USD', 'SAR'])->default('YER')->after('card_type'); // نوع العملة (ريال يمني، دولار، ريال سعودي)

            // إضافة حقل صلاحيات المدير
            $table->boolean('is_admin')->default(false)->after('currency'); // هل المستخدم مدير
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // حذف حقول البطاقة المضافة وحقل المدير
            $table->dropColumn(['card_number', 'card_type', 'currency', 'is_admin']);
        });
    }
};
