<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Request;
use Carbon\Carbon;

/**
 * نموذج سجلات الوصول لمعلومات التواصل
 * يتتبع جميع محاولات الوصول لمعلومات التواصل للأمان والتحليلات
 */
class ContactAccessLog extends Model
{
    /**
     * اسم الجدول
     */
    protected $table = 'contact_access_logs';

    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'contact_owner_id',
        'contact_id',
        'accessor_user_id',
        'access_type',
        'ip_address',
        'user_agent',
        'session_id',
        'referrer_url',
        'country_code',
        'city',
        'metadata',
        'is_suspicious',
        'suspicious_reason',
    ];

    /**
     * تحويل الحقول إلى أنواع محددة
     */
    protected $casts = [
        'metadata' => 'array',
        'is_suspicious' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * أنواع الوصول المتاحة
     */
    public const ACCESS_TYPES = [
        'view' => 'مشاهدة',
        'click' => 'نقر',
        'copy' => 'نسخ',
        'export' => 'تصدير',
        'share' => 'مشاركة',
        'reveal' => 'كشف',
        'download_qr' => 'تحميل QR',
    ];

    /**
     * العلاقة مع المستخدم صاحب معلومات التواصل
     */
    public function contactOwner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'contact_owner_id');
    }

    /**
     * العلاقة مع معلومة التواصل
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(UserContact::class, 'contact_id');
    }

    /**
     * العلاقة مع المستخدم الذي وصل للمعلومات
     */
    public function accessorUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'accessor_user_id');
    }

    /**
     * تسجيل وصول جديد لمعلومات التواصل
     */
    public static function logAccess(
        int $contactOwnerId,
        string $accessType,
        ?int $contactId = null,
        ?int $accessorUserId = null,
        array $additionalData = []
    ): self {
        $request = request();

        $logData = [
            'contact_owner_id' => $contactOwnerId,
            'contact_id' => $contactId,
            'accessor_user_id' => $accessorUserId,
            'access_type' => $accessType,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'session_id' => $request->session()->getId(),
            'referrer_url' => $request->header('referer'),
            'metadata' => $additionalData,
        ];

        // فحص النشاط المشبوه
        $suspiciousCheck = self::checkSuspiciousActivity($logData);
        if ($suspiciousCheck['is_suspicious']) {
            $logData['is_suspicious'] = true;
            $logData['suspicious_reason'] = $suspiciousCheck['reason'];
        }

        return self::create($logData);
    }

    /**
     * فحص النشاط المشبوه
     */
    protected static function checkSuspiciousActivity(array $logData): array
    {
        $ipAddress = $logData['ip_address'];
        $accessorUserId = $logData['accessor_user_id'];

        // فحص عدد الطلبات من نفس IP في آخر ساعة
        $recentAccessCount = self::where('ip_address', $ipAddress)
                               ->where('created_at', '>=', Carbon::now()->subHour())
                               ->count();

        if ($recentAccessCount > 50) {
            return [
                'is_suspicious' => true,
                'reason' => 'عدد طلبات مرتفع من نفس IP في آخر ساعة: ' . $recentAccessCount
            ];
        }

        // فحص الوصول السريع المتتالي
        $lastAccess = self::where('ip_address', $ipAddress)
                         ->latest()
                         ->first();

        if ($lastAccess && $lastAccess->created_at->diffInSeconds() < 2) {
            return [
                'is_suspicious' => true,
                'reason' => 'وصول سريع متتالي (أقل من ثانيتين)'
            ];
        }

        // فحص User Agent مشبوه
        $userAgent = $logData['user_agent'] ?? '';
        $suspiciousAgents = ['bot', 'crawler', 'spider', 'scraper'];

        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                return [
                    'is_suspicious' => true,
                    'reason' => 'User Agent مشبوه: ' . $agent
                ];
            }
        }

        return ['is_suspicious' => false, 'reason' => null];
    }

    /**
     * الحصول على إحصائيات الوصول للمستخدم
     */
    public static function getAccessStatsForUser(int $userId, int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);

        $logs = self::where('contact_owner_id', $userId)
                   ->where('created_at', '>=', $startDate)
                   ->get();

        return [
            'total_accesses' => $logs->count(),
            'unique_visitors' => $logs->pluck('ip_address')->unique()->count(),
            'registered_users' => $logs->whereNotNull('accessor_user_id')->pluck('accessor_user_id')->unique()->count(),
            'access_types' => $logs->groupBy('access_type')->map->count()->toArray(),
            'suspicious_activities' => $logs->where('is_suspicious', true)->count(),
            'daily_stats' => $logs->groupBy(function ($log) {
                return $log->created_at->format('Y-m-d');
            })->map->count()->toArray(),
        ];
    }

    /**
     * الحصول على النشاطات المشبوهة الأخيرة
     */
    public static function getRecentSuspiciousActivities(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('is_suspicious', true)
                  ->with(['contactOwner', 'contact', 'accessorUser'])
                  ->latest()
                  ->limit($limit)
                  ->get();
    }

    /**
     * تنظيف السجلات القديمة
     */
    public static function cleanOldLogs(int $daysToKeep = 90): int
    {
        $cutoffDate = Carbon::now()->subDays($daysToKeep);

        return self::where('created_at', '<', $cutoffDate)->delete();
    }

    /**
     * الحصول على اسم نوع الوصول بالعربية
     */
    public function getAccessTypeNameAttribute(): string
    {
        return self::ACCESS_TYPES[$this->access_type] ?? 'غير محدد';
    }

    /**
     * التحقق من كون النشاط من مستخدم مسجل
     */
    public function isFromRegisteredUser(): bool
    {
        return !is_null($this->accessor_user_id);
    }

    /**
     * الحصول على معلومات المتصفح المبسطة
     */
    public function getBrowserInfoAttribute(): array
    {
        $userAgent = $this->user_agent ?? '';

        // تحليل بسيط لـ User Agent
        $browser = 'غير معروف';
        $os = 'غير معروف';

        if (preg_match('/Chrome\/[\d.]+/', $userAgent)) {
            $browser = 'Chrome';
        } elseif (preg_match('/Firefox\/[\d.]+/', $userAgent)) {
            $browser = 'Firefox';
        } elseif (preg_match('/Safari\/[\d.]+/', $userAgent)) {
            $browser = 'Safari';
        } elseif (preg_match('/Edge\/[\d.]+/', $userAgent)) {
            $browser = 'Edge';
        }

        if (preg_match('/Windows/', $userAgent)) {
            $os = 'Windows';
        } elseif (preg_match('/Mac OS X/', $userAgent)) {
            $os = 'macOS';
        } elseif (preg_match('/Linux/', $userAgent)) {
            $os = 'Linux';
        } elseif (preg_match('/Android/', $userAgent)) {
            $os = 'Android';
        } elseif (preg_match('/iPhone|iPad/', $userAgent)) {
            $os = 'iOS';
        }

        return [
            'browser' => $browser,
            'os' => $os,
            'full_agent' => $userAgent
        ];
    }
}
