<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comments', function (Blueprint $table) {
            $table->id();

            // العلاقات الأساسية
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('ad_id')->constrained()->onDelete('cascade');
            $table->foreignId('parent_id')->nullable()->constrained('comments')->onDelete('cascade');

            // محتوى التعليق
            $table->text('content')->comment('محتوى التعليق');
            $table->text('content_html')->nullable()->comment('محتوى التعليق بصيغة HTML');

            // معلومات الحالة
            $table->enum('status', ['pending', 'approved', 'rejected', 'hidden'])->default('pending');
            $table->boolean('is_pinned')->default(false)->comment('هل التعليق مثبت');
            $table->boolean('is_edited')->default(false)->comment('هل التعليق معدل');
            $table->timestamp('edited_at')->nullable()->comment('تاريخ آخر تعديل');

            // معلومات التفاعل
            $table->integer('likes_count')->default(0)->comment('عدد الإعجابات');
            $table->integer('dislikes_count')->default(0)->comment('عدد عدم الإعجاب');
            $table->integer('replies_count')->default(0)->comment('عدد الردود');
            $table->boolean('is_helpful')->default(false)->comment('هل التعليق مفيد');

            // معلومات إضافية
            $table->json('metadata')->nullable()->comment('بيانات إضافية (IP، User Agent، إلخ)');
            $table->string('language', 5)->default('ar')->comment('لغة التعليق');
            $table->integer('depth')->default(0)->comment('عمق التعليق في التداخل');

            // معلومات الإشراف
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('rejection_reason')->nullable();

            $table->timestamps();

            // فهارس لتحسين الأداء
            $table->index(['ad_id', 'status', 'created_at']);
            $table->index(['parent_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['status', 'is_pinned', 'created_at']);
            $table->index(['depth', 'parent_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comments');
    }
};
