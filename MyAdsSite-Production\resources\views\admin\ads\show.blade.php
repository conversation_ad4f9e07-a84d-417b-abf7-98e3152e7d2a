@extends('layouts.app')

@section('title', __('Ad Details') . ' - ' . __('Admin Panel'))

@section('content')
<div class="container-fluid my-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 fw-bold text-primary arabic-text">
                        <i class="fas fa-eye me-2"></i>
                        {{ __('Ad Details') }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('admin.ads.index') }}" class="arabic-text">{{ __('Manage Ads') }}</a></li>
                            <li class="breadcrumb-item active arabic-text">{{ __('Ad Details') }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('admin.ads.index') }}" class="btn btn-outline-secondary arabic-text">
                        <i class="fas fa-arrow-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} me-1"></i>
                        {{ __('Back to List') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- تفاصيل الإعلان -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 arabic-text">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('Ad Information') }}
                    </h5>
                    @php
                        $statusClasses = [
                            'pending' => 'bg-warning text-dark',
                            'active' => 'bg-success',
                            'rejected' => 'bg-danger',
                            'inactive' => 'bg-secondary'
                        ];
                        $statusTexts = [
                            'pending' => __('Pending Review'),
                            'active' => __('Active'),
                            'rejected' => __('Rejected'),
                            'inactive' => __('Inactive')
                        ];
                    @endphp
                    <span class="badge {{ $statusClasses[$ad->status] ?? 'bg-secondary' }} fs-6 arabic-text">
                        {{ $statusTexts[$ad->status] ?? $ad->status }}
                    </span>
                </div>
                
                <div class="card-body">
                    <!-- صورة الإعلان -->
                    @if($ad->image)
                        <div class="text-center mb-4">
                            <img src="{{ asset('storage/' . $ad->image) }}" 
                                 alt="{{ $ad->title }}" 
                                 class="img-fluid rounded shadow"
                                 style="max-height: 400px; object-fit: cover;">
                        </div>
                    @endif

                    <!-- العنوان -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold arabic-text">{{ __('Arabic Title') }}</label>
                            <p class="form-control-plaintext border rounded p-2 bg-light arabic-text">{{ $ad->title_ar }}</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold arabic-text">{{ __('English Title') }}</label>
                            <p class="form-control-plaintext border rounded p-2 bg-light">{{ $ad->title_en }}</p>
                        </div>
                    </div>

                    <!-- الوصف -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold arabic-text">{{ __('Arabic Description') }}</label>
                            <div class="border rounded p-3 bg-light arabic-text" style="min-height: 120px;">
                                {{ $ad->description_ar }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold arabic-text">{{ __('English Description') }}</label>
                            <div class="border rounded p-3 bg-light" style="min-height: 120px;">
                                {{ $ad->description_en }}
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label fw-bold arabic-text">{{ __('Category') }}</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-primary arabic-text">
                                    <i class="{{ $ad->category->icon }} me-1"></i>
                                    {{ $ad->category->name }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label fw-bold arabic-text">{{ __('Location') }}</label>
                            <p class="form-control-plaintext border rounded p-2 bg-light arabic-text">{{ $ad->location }}</p>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label fw-bold arabic-text">{{ __('Views Count') }}</label>
                            <p class="form-control-plaintext border rounded p-2 bg-light">
                                <i class="fas fa-eye me-1"></i>
                                {{ number_format($ad->views_count) }}
                            </p>
                        </div>
                    </div>

                    <!-- معلومات التواصل -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold arabic-text">{{ __('Phone Number') }}</label>
                            <p class="form-control-plaintext border rounded p-2 bg-light">
                                <i class="fas fa-phone me-1"></i>
                                {{ $ad->phone }}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold arabic-text">{{ __('Email') }}</label>
                            <p class="form-control-plaintext border rounded p-2 bg-light">
                                <i class="fas fa-envelope me-1"></i>
                                {{ $ad->email ?? __('Not specified') }}
                            </p>
                        </div>
                    </div>

                    <!-- تواريخ -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label fw-bold arabic-text">{{ __('Created Date') }}</label>
                            <p class="form-control-plaintext border rounded p-2 bg-light">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $ad->created_at->format('Y-m-d H:i') }}
                            </p>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label fw-bold arabic-text">{{ __('Last Updated') }}</label>
                            <p class="form-control-plaintext border rounded p-2 bg-light">
                                <i class="fas fa-clock me-1"></i>
                                {{ $ad->updated_at->format('Y-m-d H:i') }}
                            </p>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label fw-bold arabic-text">{{ __('Expiry Date') }}</label>
                            <p class="form-control-plaintext border rounded p-2 bg-light">
                                <i class="fas fa-calendar-times me-1"></i>
                                {{ $ad->expires_at ? $ad->expires_at->format('Y-m-d') : __('Not specified') }}
                            </p>
                        </div>
                    </div>

                    <!-- الرابط المختصر -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label fw-bold arabic-text">{{ __('Short Link') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">{{ url('/') }}/ads/</span>
                                <input type="text" class="form-control" value="{{ $ad->slug }}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    @if($ad->rejection_reason)
                        <!-- سبب الرفض -->
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>{{ __('Rejection Reason') }}</h6>
                            <p class="mb-0 arabic-text">{{ $ad->rejection_reason }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- معلومات المستخدم والإجراءات -->
        <div class="col-lg-4">
            <!-- معلومات المستخدم -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0 arabic-text">
                        <i class="fas fa-user me-2"></i>
                        {{ __('User Information') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center"
                             style="width: 60px; height: 60px;">
                            <i class="fas fa-user fa-2x text-white"></i>
                        </div>
                    </div>
                    <div class="text-center">
                        <h6 class="fw-bold arabic-text">{{ $ad->user->name }}</h6>
                        <p class="text-muted mb-2">{{ $ad->user->email }}</p>
                        @if($ad->user->is_admin)
                            <span class="badge bg-warning text-dark arabic-text">
                                <i class="fas fa-crown me-1"></i>
                                {{ __('Admin') }}
                            </span>
                        @else
                            <span class="badge bg-info arabic-text">
                                <i class="fas fa-user me-1"></i>
                                {{ __('User') }}
                            </span>
                        @endif
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted arabic-text">{{ __('Total Ads') }}</small>
                            <div class="fw-bold">{{ $ad->user->ads()->count() }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted arabic-text">{{ __('Active Ads') }}</small>
                            <div class="fw-bold">{{ $ad->user->ads()->active()->count() }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات الإدارة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0 arabic-text">
                        <i class="fas fa-tools me-2"></i>
                        {{ __('Admin Actions') }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($ad->status === 'pending')
                        <!-- أزرار الموافقة والرفض -->
                        <div class="d-grid gap-2 mb-3">
                            <form method="POST" action="{{ route('admin.ads.approve', $ad) }}">
                                @csrf
                                <button type="submit" class="btn btn-success w-100 arabic-text"
                                        onclick="return confirm('{{ __('Do you want to approve this ad?') }}')">
                                    <i class="fas fa-check me-2"></i>
                                    {{ __('Approve Ad') }}
                                </button>
                            </form>
                            <button type="button" class="btn btn-danger w-100 arabic-text" onclick="showRejectModal()">
                                <i class="fas fa-times me-2"></i>
                                {{ __('Reject Ad') }}
                            </button>
                        </div>
                        <hr>
                    @endif

                    <!-- تبديل الإعلان المميز -->
                    <div class="d-grid gap-2 mb-3">
                        <form method="POST" action="{{ route('admin.ads.toggle-featured', $ad) }}">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn {{ $ad->is_featured ? 'btn-warning' : 'btn-outline-warning' }} w-100 arabic-text">
                                <i class="fas fa-star me-2"></i>
                                {{ $ad->is_featured ? __('Remove from Featured') : __('Add to Featured') }}
                            </button>
                        </form>
                    </div>

                    <!-- تغيير الحالة -->
                    <div class="mb-3">
                        <label class="form-label arabic-text">{{ __('Change Ad Status') }}</label>
                        <form method="POST" action="{{ route('admin.ads.update-status', $ad) }}">
                            @csrf
                            @method('PATCH')
                            <div class="input-group">
                                <select name="status" class="form-select arabic-text">
                                    <option value="pending" {{ $ad->status === 'pending' ? 'selected' : '' }}>{{ __('Pending Review') }}</option>
                                    <option value="active" {{ $ad->status === 'active' ? 'selected' : '' }}>{{ __('Active') }}</option>
                                    <option value="inactive" {{ $ad->status === 'inactive' ? 'selected' : '' }}>{{ __('Inactive') }}</option>
                                    <option value="rejected" {{ $ad->status === 'rejected' ? 'selected' : '' }}>{{ __('Rejected') }}</option>
                                </select>
                                <button type="submit" class="btn btn-primary arabic-text">{{ __('Update') }}</button>
                            </div>
                        </form>
                    </div>

                    <hr>

                    <!-- حذف الإعلان -->
                    <div class="d-grid">
                        <form method="POST" action="{{ route('admin.ads.destroy', $ad) }}">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100 arabic-text"
                                    onclick="return confirm('{{ __('Are you sure you want to permanently delete this ad? This action cannot be undone.') }}')">
                                <i class="fas fa-trash me-2"></i>
                                {{ __('Delete Ad Permanently') }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal رفض الإعلان -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title arabic-text">{{ __('Reject Ad') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('admin.ads.reject', $ad) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reason" class="form-label arabic-text">{{ __('Rejection Reason (Optional)') }}</label>
                        <textarea class="form-control arabic-text" id="reason" name="reason" rows="4"
                                  placeholder="{{ __('Write the reason for rejecting the ad...') }}"></textarea>
                        <div class="form-text arabic-text">{{ __('This reason will be sent to the user') }}</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary arabic-text" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-danger arabic-text">{{ __('Reject Ad') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRejectModal() {
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // يمكن إضافة رسالة تأكيد هنا
        alert('{{ __("Link copied to clipboard") }}');
    });
}
</script>
@endsection
