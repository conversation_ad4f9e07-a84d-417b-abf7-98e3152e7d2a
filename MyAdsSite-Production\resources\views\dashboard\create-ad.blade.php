@extends('layouts.app')

@section('title', __('Create New Ad'))

@push('styles')
<style>
.arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
}

.preview-container {
    position: relative;
    display: inline-block;
}

.remove-preview {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 12px;
    cursor: pointer;
}

@if(app()->getLocale() == 'ar')
.btn-group .btn {
    border-radius: 0;
}
.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}
@endif
</style>
@endpush

@section('content')
<div class="container py-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1 arabic-text text-primary" data-animation="fadeInLeft">
                        <i class="fas fa-plus-circle me-2"></i>
                        {{ __('Create New Ad') }}
                    </h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('Add your ad now and reach thousands of users') }}</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.ads.index') }}" class="btn btn-outline-secondary arabic-text">
                        <i class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'right' : 'left' }} me-2"></i>
                        {{ __('Back to My Ads') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إنشاء الإعلان -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0 arabic-text">
                        <i class="fas fa-edit me-2"></i>
                        {{ __('Ad Details') }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('dashboard.ads.store') }}" enctype="multipart/form-data" id="createAdForm">
                        @csrf

                        <!-- العنوان بالعربية -->
                        <div class="mb-3">
                            <label for="title_ar" class="form-label required arabic-text">
                                <i class="fas fa-heading me-1"></i>
                                {{ __('Title (Arabic)') }}
                            </label>
                            <input type="text"
                                   class="form-control @error('title_ar') is-invalid @enderror"
                                   id="title_ar"
                                   name="title_ar"
                                   value="{{ old('title_ar') }}"
                                   required
                                   maxlength="255"
                                   placeholder="{{ __('Enter ad title in Arabic') }}"
                                   dir="rtl">
                            @error('title_ar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- العنوان بالإنجليزية -->
                        <div class="mb-3">
                            <label for="title_en" class="form-label arabic-text">
                                <i class="fas fa-heading me-1"></i>
                                {{ __('Title (English)') }}
                            </label>
                            <input type="text"
                                   class="form-control @error('title_en') is-invalid @enderror"
                                   id="title_en"
                                   name="title_en"
                                   value="{{ old('title_en') }}"
                                   maxlength="255"
                                   placeholder="{{ __('Enter ad title in English (optional)') }}"
                                   dir="ltr">
                            @error('title_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- التصنيف -->
                        <div class="mb-3">
                            <label for="category_id" class="form-label required arabic-text">
                                <i class="fas fa-tags me-1"></i>
                                {{ __('Category') }}
                            </label>
                            <select class="form-select @error('category_id') is-invalid @enderror"
                                    id="category_id"
                                    name="category_id"
                                    required>
                                <option value="">{{ __('Select Category') }}</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}"
                                            {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الوصف بالعربية -->
                        <div class="mb-3">
                            <label for="description_ar" class="form-label required arabic-text">
                                <i class="fas fa-align-left me-1"></i>
                                {{ __('Description (Arabic)') }}
                            </label>
                            <textarea class="form-control @error('description_ar') is-invalid @enderror"
                                      id="description_ar"
                                      name="description_ar"
                                      rows="4"
                                      required
                                      maxlength="2000"
                                      placeholder="{{ __('Describe your ad in detail in Arabic') }}"
                                      dir="rtl">{{ old('description_ar') }}</textarea>
                            @error('description_ar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الوصف بالإنجليزية -->
                        <div class="mb-3">
                            <label for="description_en" class="form-label arabic-text">
                                <i class="fas fa-align-left me-1"></i>
                                {{ __('Description (English)') }}
                            </label>
                            <textarea class="form-control @error('description_en') is-invalid @enderror"
                                      id="description_en"
                                      name="description_en"
                                      rows="4"
                                      maxlength="2000"
                                      placeholder="{{ __('Describe your ad in detail in English (optional)') }}"
                                      dir="ltr">{{ old('description_en') }}</textarea>
                            @error('description_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الصورة -->
                        <div class="mb-3">
                            <label for="image" class="form-label arabic-text">
                                <i class="fas fa-image me-1"></i>
                                {{ __('Ad Image') }}
                            </label>
                            <input type="file"
                                   class="form-control @error('image') is-invalid @enderror"
                                   id="image"
                                   name="image"
                                   accept="image/*"
                                   onchange="previewImage(this)">
                            <div class="form-text arabic-text">
                                {{ __('Maximum: 5 MB. Supported types: JPG, PNG, WEBP') }}
                            </div>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror

                            <!-- معاينة الصورة -->
                            <div id="imagePreview" style="display: none;" class="mt-2">
                                <div class="preview-container">
                                    <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                    <button type="button" class="remove-preview" onclick="removeImage()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label arabic-text">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ __('Phone Number') }}
                                </label>
                                <input type="tel"
                                       class="form-control @error('phone') is-invalid @enderror"
                                       id="phone"
                                       name="phone"
                                       value="{{ old('phone') }}"
                                       maxlength="20"
                                       placeholder="{{ __('Phone number') }}"
                                       dir="ltr">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label arabic-text">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ __('Email') }}
                                </label>
                                <input type="email"
                                       class="form-control @error('email') is-invalid @enderror"
                                       id="email"
                                       name="email"
                                       value="{{ old('email') }}"
                                       maxlength="255"
                                       placeholder="{{ __('Email address') }}"
                                       dir="ltr">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- الموقع -->
                        <div class="mb-3">
                            <label for="location" class="form-label arabic-text">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ __('Ad Location') }}
                            </label>
                            <input type="text"
                                   class="form-control @error('location') is-invalid @enderror"
                                   id="location"
                                   name="location"
                                   value="{{ old('location') }}"
                                   maxlength="255"
                                   placeholder="{{ __('City or area') }}"
                                   dir="rtl">
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- تاريخ انتهاء الصلاحية -->
                        <div class="mb-4">
                            <label for="expires_at" class="form-label arabic-text">
                                <i class="fas fa-calendar-alt me-1"></i>
                                {{ __('Ad Expiry Date') }}
                            </label>
                            <input type="date"
                                   class="form-control @error('expires_at') is-invalid @enderror"
                                   id="expires_at"
                                   name="expires_at"
                                   value="{{ old('expires_at') }}"
                                   min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                            <div class="form-text arabic-text">
                                {{ __('If no date is specified, the ad will remain active indefinitely') }}
                            </div>
                            @error('expires_at')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('dashboard.ads.index') }}" class="btn btn-outline-secondary arabic-text">
                                <i class="fas fa-times me-2"></i>
                                {{ __('Cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary arabic-text" id="submitBtn">
                                <i class="fas fa-save me-2"></i>
                                {{ __('Publish Ad') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- ملاحظات مهمة -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0 arabic-text">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('Important Notes') }}
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0 arabic-text">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Your ad will be reviewed before publishing to ensure it complies with site terms') }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Make sure your contact information is correct to ensure interested parties can reach you') }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('Use clear title and description to increase your ad\'s chances of appearing in search results') }}
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ __('You can edit or delete your ad anytime from My Ads page') }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        }

        reader.readAsDataURL(input.files[0]);
    }
}

function removeImage() {
    const input = document.getElementById('image');
    const preview = document.getElementById('imagePreview');

    input.value = '';
    preview.style.display = 'none';
}

// تحسين تجربة الإرسال
document.getElementById('createAdForm').addEventListener('submit', function() {
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{{ __("Publishing...") }}';
    submitBtn.disabled = true;
});
</script>
@endsection
