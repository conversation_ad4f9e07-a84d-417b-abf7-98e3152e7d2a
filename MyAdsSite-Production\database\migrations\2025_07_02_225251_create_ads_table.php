<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ads', function (Blueprint $table) {
            $table->id();

            // عناوين الإعلانات باللغتين العربية والإنجليزية
            $table->string('title_ar'); // العنوان بالعربية
            $table->string('title_en'); // العنوان بالإنجليزية

            // وصف الإعلانات باللغتين العربية والإنجليزية
            $table->text('description_ar'); // الوصف بالعربية
            $table->text('description_en'); // الوصف بالإنجليزية

            // مسار الصورة الخاصة بالإعلان
            $table->string('image')->nullable();

            // مفتاح خارجي يربط بجدول التصنيفات
            $table->foreignId('category_id')->constrained()->onDelete('cascade');

            // مفتاح خارجي يربط بجدول المستخدمين
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // حالة الإعلان (نشط، غير نشط، في انتظار الموافقة، مرفوض، منتهي الصلاحية)
            $table->enum('status', ['pending', 'active', 'inactive', 'rejected', 'expired'])->default('pending');

            // هل الإعلان مميز
            $table->boolean('is_featured')->default(false);

            // حقول إضافية لتنظيم الإعلانات
            $table->string('phone')->nullable(); // رقم الهاتف
            $table->string('email')->nullable(); // البريد الإلكتروني
            $table->string('location')->nullable(); // الموقع أو المدينة
            $table->decimal('price', 10, 2)->nullable(); // السعر (اختياري)
            $table->date('expires_at')->nullable(); // تاريخ انتهاء صلاحية الإعلان
            $table->text('rejection_reason')->nullable(); // سبب الرفض (في حالة رفض الإعلان)

            // رابط مختصر وودود للإعلان (لتحسين محركات البحث)
            $table->string('slug')->unique();

            // عداد المشاهدات
            $table->unsignedInteger('views_count')->default(0);

            $table->timestamps();

            // إضافة فهارس لتحسين الأداء والبحث السريع
            $table->index(['user_id', 'status'], 'ads_user_status_index');
            $table->index(['status', 'expires_at'], 'ads_status_expires_index');
            $table->index(['category_id', 'status'], 'ads_category_status_index');
            $table->index(['created_at'], 'ads_created_at_index');
            $table->index(['is_featured', 'status'], 'ads_featured_status_index');

            // فهارس إضافية للبحث المتقدم
            $table->index(['title_ar'], 'ads_title_ar_index');
            $table->index(['title_en'], 'ads_title_en_index');
            $table->index(['location'], 'ads_location_index');
            $table->index(['views_count'], 'ads_views_count_index');
            $table->index(['slug'], 'ads_slug_index');

            // فهارس مركبة للاستعلامات المعقدة
            $table->index(['category_id', 'is_featured', 'status'], 'ads_category_featured_status_index');
            $table->index(['location', 'category_id', 'status'], 'ads_location_category_status_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ads');
    }
};
