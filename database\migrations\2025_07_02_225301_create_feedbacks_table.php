<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('feedbacks', function (Blueprint $table) {
            $table->id();

            // معلومات العميل
            $table->string('name'); // اسم العميل
            $table->string('email')->nullable(); // البريد الإلكتروني (اختياري)

            // محتوى التقييم
            $table->text('message'); // رسالة التقييم أو الرأي

            // نظام التقييم بالنجوم (من 1 إلى 5)
            $table->tinyInteger('rating')->unsigned()->default(5);

            // حالة التقييم للمراجعة والموافقة
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');

            // حقول إضافية اختيارية
            $table->string('phone')->nullable(); // رقم الهاتف للتواصل
            $table->string('company')->nullable(); // اسم الشركة أو المؤسسة (إن وجد)

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feedbacks');
    }
};
