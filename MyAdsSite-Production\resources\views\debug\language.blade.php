@extends('layouts.app')

@section('title', 'Language Debug Info')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-bug me-2"></i>
                        {{ __('Language Debug Information') }}
                    </h4>
                    <small class="text-light">{{ __('Development Mode Only') }}</small>
                </div>

                <div class="card-body">
                    <!-- معلومات اللغة الحالية -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-globe me-2"></i>{{ __('Current Language') }}</h5>
                                <p class="mb-1"><strong>{{ __('Locale') }}:</strong>
                                    <span class="badge bg-primary fs-6">{{ $debugInfo['current_locale'] }}</span>
                                </p>
                                <p class="mb-1"><strong>{{ __('Direction') }}:</strong>
                                    <span class="badge bg-secondary fs-6">{{ $debugInfo['direction'] }}</span>
                                </p>
                                <p class="mb-0"><strong>{{ __('Source') }}:</strong>
                                    <span class="badge bg-success fs-6">{{ $debugInfo['locale_source'] }}</span>
                                </p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="alert alert-secondary">
                                <h5><i class="fas fa-list me-2"></i>{{ __('Available Languages') }}</h5>
                                @foreach($debugInfo['available_locales'] as $code => $name)
                                    <span class="badge bg-outline-primary me-2 mb-1">{{ $code }}: {{ $name }}</span>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التخزين -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-database me-2"></i>{{ __('Storage Information') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>{{ __('Session Locale') }}:</strong></td>
                                            <td>
                                                @if($debugInfo['storage_info']['session_locale'])
                                                    <span class="badge bg-success">{{ $debugInfo['storage_info']['session_locale'] }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ __('Not Set') }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Cookie Locale') }}:</strong></td>
                                            <td>
                                                @if($debugInfo['storage_info']['cookie_locale'])
                                                    <span class="badge bg-success">{{ $debugInfo['storage_info']['cookie_locale'] }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ __('Not Set') }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>{{ __('Config Locale') }}:</strong></td>
                                            <td><span class="badge bg-info">{{ $debugInfo['storage_info']['config_locale'] }}</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('Fallback Locale') }}:</strong></td>
                                            <td><span class="badge bg-warning">{{ $debugInfo['storage_info']['fallback_locale'] }}</span></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات البيئة -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-server me-2"></i>{{ __('Environment Information') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <p><strong>{{ __('Debug Mode') }}:</strong>
                                        @if($debugInfo['environment']['app_debug'])
                                            <span class="badge bg-warning">{{ __('Enabled') }}</span>
                                        @else
                                            <span class="badge bg-success">{{ __('Disabled') }}</span>
                                        @endif
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>{{ __('Environment') }}:</strong>
                                        <span class="badge bg-info">{{ $debugInfo['environment']['app_env'] }}</span>
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>{{ __('Timezone') }}:</strong>
                                        <span class="badge bg-secondary">{{ $debugInfo['environment']['timezone'] }}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الطلب -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-network-wired me-2"></i>{{ __('Request Information') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>{{ __('Accept Language') }}:</strong>
                                        <code>{{ $debugInfo['request_info']['accept_language'] ?? 'Not Set' }}</code>
                                    </p>
                                    <p><strong>{{ __('User Agent') }}:</strong>
                                        <small class="text-muted">{{ Str::limit($debugInfo['request_info']['user_agent'], 100) }}</small>
                                    </p>
                                    <p><strong>{{ __('Current URL') }}:</strong>
                                        <code>{{ $debugInfo['request_info']['url'] }}</code>
                                    </p>
                                    <p><strong>{{ __('Method') }}:</strong>
                                        <span class="badge bg-primary">{{ $debugInfo['request_info']['method'] }}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- JSON Export -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-code me-2"></i>{{ __('JSON Export') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex gap-2 mb-3">
                                <a href="{{ route('language.debug', ['json' => 1]) }}"
                                   class="btn btn-outline-primary btn-sm" target="_blank">
                                    <i class="fas fa-download me-1"></i>{{ __('View as JSON') }}
                                </a>
                                <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                                    <i class="fas fa-copy me-1"></i>{{ __('Copy JSON') }}
                                </button>
                            </div>
                            <pre id="jsonData" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"><code>{{ json_encode($debugInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                        </div>
                    </div>

                    <!-- معلومات الوقت -->
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            {{ __('Generated at') }}: {{ $debugInfo['timestamp'] }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard() {
    const jsonData = document.getElementById('jsonData').textContent;
    navigator.clipboard.writeText(jsonData).then(function() {
        // إظهار رسالة نجاح
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>{{ __("Copied!") }}';
        btn.classList.remove('btn-outline-secondary');
        btn.classList.add('btn-success');

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}
</script>
@endsection
