

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'ad',                           // الإعلان المطلوب عرضه (مطلوب)
    'favorite' => null,             // بيانات المفضلة
    'config' => []                  // إعدادات البطاقة
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'ad',                           // الإعلان المطلوب عرضه (مطلوب)
    'favorite' => null,             // بيانات المفضلة
    'config' => []                  // إعدادات البطاقة
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // دمج الإعدادات الموجودة مع الإعدادات الخاصة ببطاقة المفضلة
    $config = array_merge($config, [
        'variant' => 'favorites',
        'descriptionLength' => $config['descriptionLength'] ?? 80,
        'imageHeight' => '200px',
        'cardHeight' => '480px',
        'padding' => 'p-3',
        'marginBottom' => 'mb-4',
        'titleSize' => 'h6',
        'showFullMeta' => true,
        'showFavorites' => false,  // لا نظهر أيقونة المفضلة العادية
        'showRating' => true,
        'favoriteId' => $favorite->id ?? null,
        'favoriteDate' => $favorite->created_at ?? null,
    ]);
?>




<div class="card ad-card ad-card-favorites h-100 shadow-sm border-0"
     data-ad-id="<?php echo e($ad->id); ?>"
     data-animation="fadeInUp"
     style="min-height: <?php echo e($config['cardHeight']); ?>;">

    
    <div class="card-header-custom">
        <!-- صورة الإعلان -->
        <div class="ad-image" style="height: <?php echo e($config['imageHeight']); ?>;">
            <?php if($ad->image_url): ?>
                <img src="<?php echo e($ad->image_url); ?>" alt="<?php echo e($ad->title); ?>" class="card-img-top">
            <?php else: ?>
                <div class="placeholder-image">
                    <i class="<?php echo e($ad->category->icon); ?> fa-4x text-muted"></i>
                </div>
            <?php endif; ?>

            <!-- أيقونة إزالة من المفضلة - محسنة للنظام الجديد -->
            <div class="favorite-icon favorite-remove-icon"
                 data-ad-id="<?php echo e($ad->id); ?>"
                 data-favorite-id="<?php echo e($config['favoriteId'] ?? ''); ?>">
                <button class="btn-favorite-remove btn-favorite"
                        data-ad-id="<?php echo e($ad->id); ?>"
                        data-favorite-id="<?php echo e($config['favoriteId'] ?? ''); ?>"
                        title="<?php echo e(__('Remove from favorites')); ?>">
                    <i class="fas fa-heart-broken"></i>
                </button>
            </div>

            
            <?php echo $__env->make('components.ad-card.partials.badges', [
                'ad' => $ad,
                'config' => $config
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>

    
    <div class="card-body <?php echo e($config['padding']); ?>" style="overflow: visible; position: relative;">
        
        <?php echo $__env->make('components.ad-card.partials.category-badge', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        
        <?php echo $__env->make('components.ad-card.partials.title', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        
        <?php echo $__env->make('components.ad-card.partials.description', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        
        <?php if($config['showRating'] ?? true): ?>
            <?php echo $__env->make('components.ad-card.partials.rating', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>

        
        <?php if($config['showMeta']): ?>
            <?php echo $__env->make('components.ad-card.partials.meta', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>

        
        <?php echo $__env->make('components.ad-card.partials.contact', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        
        <?php if($config['showPricing']): ?>
            <?php echo $__env->make('components.ad-card.partials.pricing', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>
    </div>

    
    <?php if($config['showActions'] || $config['showExpiry']): ?>
        <?php echo $__env->make('components.ad-card.partials.footer', ['ad' => $ad, 'config' => $config], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>

    
    <div class="card-overlay"></div>
</div>


<?php if (! $__env->hasRenderedOnce('6b74ea48-0b87-4a91-9499-14e784badbf6')): $__env->markAsRenderedOnce('6b74ea48-0b87-4a91-9499-14e784badbf6'); ?>
    <?php $__env->startPush('styles'); ?>
        <link rel="stylesheet" href="<?php echo e(asset('css/ad-cards.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('css/badge-fix.css')); ?>">
        <style>
        /* أنماط إضافية خاصة ببطاقات المفضلة */
        .favorite-remove-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }

        .btn-favorite-remove {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(220, 53, 69, 0.9);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-favorite-remove:hover {
            background: #dc3545;
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .ad-card-favorites:hover {
            border-color: #dc3545 !important;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .btn-favorite-remove {
                width: 30px;
                height: 30px;
            }
        }
        </style>
    <?php $__env->stopPush(); ?>
<?php endif; ?>


<?php if (! $__env->hasRenderedOnce('5863a022-baa9-4b28-825d-7cfc479aa7cf')): $__env->markAsRenderedOnce('5863a022-baa9-4b28-825d-7cfc479aa7cf'); ?>
    <?php $__env->startPush('scripts'); ?>
        <script src="<?php echo e(asset('js/favorites.js')); ?>"></script>
        <script>
            // دالة مشاركة الإعلان
            function shareAd(url, title) {
                if (navigator.share) {
                    navigator.share({
                        title: title,
                        url: url
                    }).catch(console.error);
                } else {
                    // نسخ الرابط للحافظة
                    navigator.clipboard.writeText(url).then(function() {
                        showToast('<?php echo e(__("Link copied to clipboard")); ?>', 'success');
                    }).catch(function() {
                        // فتح نافذة مشاركة تقليدية
                        window.open('https://wa.me/?text=' + encodeURIComponent(title + ' - ' + url), '_blank');
                    });
                }
            }

            // تم نقل معالج الأحداث إلى النظام الموحد في public/js/favorites.js
            // يتم التعامل مع الأحداث تلقائياً عبر النظام الموحد
        </script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/components/ad-card/variants/favorites.blade.php ENDPATH**/ ?>