@extends('layouts.app')

@section('title', __('About Us') . ' - ' . __('site_name'))
@section('description', __('Learn more about our platform and our mission to connect businesses and customers'))

@section('content')
<!-- القسم الرئيسي -->
<section class="about-hero py-5" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); min-height: 50vh;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center text-white">
                <div class="about-header">
                    <div class="about-icon mb-4">
                        <i class="fas fa-info-circle fa-5x"></i>
                    </div>
                    <h1 class="display-4 fw-bold arabic-text mb-3">
                        {{ __('About Us') }}
                    </h1>
                    <p class="lead arabic-text mb-4">
                        {{ __('Learn more about our platform and our mission') }}
                    </p>
                    <div class="hero-stats">
                        <div class="row text-center">
                            <div class="col-md-4 mb-3">
                                <div class="stat-item">
                                    <h3 class="fw-bold">{{ \App\Models\Category::active()->count() }}+</h3>
                                    <p class="arabic-text">{{ __('Categories') }}</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="stat-item">
                                    <h3 class="fw-bold">{{ \App\Models\Ad::active()->notExpired()->count() }}+</h3>
                                    <p class="arabic-text">{{ __('Active Ads') }}</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="stat-item">
                                    <h3 class="fw-bold">{{ \App\Models\User::count() }}+</h3>
                                    <p class="arabic-text">{{ __('Users') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="about-content py-5">
    <div class="container">
        <!-- قصتنا -->
        <div class="row mb-5">
            <div class="col-lg-6 mb-4">
                <div class="content-card h-100">
                    <div class="card-icon mb-3">
                        <i class="fas fa-rocket fa-3x text-success"></i>
                    </div>
                    <h3 class="arabic-text fw-bold mb-3">{{ __('Our Story') }}</h3>
                    <p class="arabic-text text-muted">
                        {{ __('We started with a simple vision: to create a comprehensive platform that connects businesses with their customers in Yemen and the Middle East. Our platform specializes in educational, healthcare, and commercial institutions.') }}
                    </p>
                    <p class="arabic-text text-muted">
                        {{ __('Since our launch, we have been committed to providing an easy-to-use platform that helps businesses reach their target audience effectively.') }}
                    </p>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="content-card h-100">
                    <div class="card-icon mb-3">
                        <i class="fas fa-bullseye fa-3x text-primary"></i>
                    </div>
                    <h3 class="arabic-text fw-bold mb-3">{{ __('Our Mission') }}</h3>
                    <p class="arabic-text text-muted">
                        {{ __('Our mission is to provide a reliable and comprehensive advertising platform that serves educational, healthcare, and commercial institutions in Yemen and the Middle East.') }}
                    </p>
                    <p class="arabic-text text-muted">
                        {{ __('We strive to make it easy for businesses to showcase their services and for customers to find what they need quickly and efficiently.') }}
                    </p>
                </div>
            </div>
        </div>

        <!-- قيمنا -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h2 class="arabic-text fw-bold">{{ __('Our Values') }}</h2>
                <p class="text-muted arabic-text">{{ __('The principles that guide our work') }}</p>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="value-card text-center">
                    <div class="value-icon mb-3">
                        <i class="fas fa-shield-alt fa-3x text-info"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Trust') }}</h5>
                    <p class="arabic-text text-muted small">
                        {{ __('We build trust through transparency and reliability') }}
                    </p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="value-card text-center">
                    <div class="value-icon mb-3">
                        <i class="fas fa-users fa-3x text-warning"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Community') }}</h5>
                    <p class="arabic-text text-muted small">
                        {{ __('We serve our community and support local businesses') }}
                    </p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="value-card text-center">
                    <div class="value-icon mb-3">
                        <i class="fas fa-lightbulb fa-3x text-danger"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Innovation') }}</h5>
                    <p class="arabic-text text-muted small">
                        {{ __('We continuously improve our platform with new features') }}
                    </p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="value-card text-center">
                    <div class="value-icon mb-3">
                        <i class="fas fa-star fa-3x text-success"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Excellence') }}</h5>
                    <p class="arabic-text text-muted small">
                        {{ __('We strive for excellence in everything we do') }}
                    </p>
                </div>
            </div>
        </div>

        <!-- خدماتنا -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h2 class="arabic-text fw-bold">{{ __('Our Services') }}</h2>
                <p class="text-muted arabic-text">{{ __('What we offer to our users') }}</p>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="service-card">
                    <div class="service-icon mb-3">
                        <i class="fas fa-graduation-cap fa-2x text-primary"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Educational Institutions') }}</h5>
                    <p class="arabic-text text-muted">
                        {{ __('Schools, universities, and training centers can showcase their programs and services') }}
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="service-card">
                    <div class="service-icon mb-3">
                        <i class="fas fa-heartbeat fa-2x text-danger"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Healthcare Services') }}</h5>
                    <p class="arabic-text text-muted">
                        {{ __('Hospitals, clinics, and medical centers can reach patients effectively') }}
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="service-card">
                    <div class="service-icon mb-3">
                        <i class="fas fa-store fa-2x text-success"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Commercial Businesses') }}</h5>
                    <p class="arabic-text text-muted">
                        {{ __('Shops, restaurants, and service providers can attract more customers') }}
                    </p>
                </div>
            </div>
        </div>

        <!-- فريق العمل -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h2 class="arabic-text fw-bold">{{ __('Our Team') }}</h2>
                <p class="text-muted arabic-text">{{ __('The people behind our success') }}</p>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="team-card text-center">
                    <div class="team-avatar mb-3">
                        <i class="fas fa-user-circle fa-5x text-primary"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Development Team') }}</h5>
                    <p class="text-muted arabic-text">{{ __('Technical Development') }}</p>
                    <p class="arabic-text small">
                        {{ __('Our skilled developers work continuously to improve the platform') }}
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="team-card text-center">
                    <div class="team-avatar mb-3">
                        <i class="fas fa-user-tie fa-5x text-success"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Business Team') }}</h5>
                    <p class="text-muted arabic-text">{{ __('Business Development') }}</p>
                    <p class="arabic-text small">
                        {{ __('Our business team helps connect with new partners and clients') }}
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="team-card text-center">
                    <div class="team-avatar mb-3">
                        <i class="fas fa-headset fa-5x text-info"></i>
                    </div>
                    <h5 class="arabic-text fw-bold">{{ __('Support Team') }}</h5>
                    <p class="text-muted arabic-text">{{ __('Customer Support') }}</p>
                    <p class="arabic-text small">
                        {{ __('Our support team is always ready to help our users') }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- دعوة للعمل -->
<section class="about-cta py-5 bg-light">
    <div class="container text-center">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h2 class="arabic-text fw-bold mb-3">{{ __('Ready to Join Us?') }}</h2>
                <p class="lead arabic-text text-muted mb-4">
                    {{ __('Start showcasing your business today and reach thousands of potential customers') }}
                </p>
                <div class="cta-buttons">
                    <a href="{{ route('auth.register') }}" class="btn btn-success btn-lg me-3 mb-2">
                        <i class="fas fa-user-plus me-2"></i>
                        {{ __('Create Account') }}
                    </a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-primary btn-lg mb-2">
                        <i class="fas fa-envelope me-2"></i>
                        {{ __('Contact Us') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* تنسيق القسم الرئيسي */
.about-hero {
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.about-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.hero-stats .stat-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

/* بطاقات المحتوى */
.content-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.content-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    text-align: center;
}

/* بطاقات القيم */
.value-card {
    background: white;
    padding: 2rem 1rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* بطاقات الخدمات */
.service-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    border-left: 4px solid transparent;
}

.service-card:hover {
    transform: translateY(-5px);
    border-left-color: #007bff;
}

/* بطاقات الفريق */
.team-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.team-card:hover {
    transform: translateY(-5px);
}

.team-avatar {
    margin-bottom: 1rem;
}

/* دعوة للعمل */
.about-cta {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.cta-buttons .btn {
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: bold;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .about-hero {
        min-height: 40vh;
        padding: 3rem 0;
    }
    
    .content-card,
    .value-card,
    .service-card,
    .team-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-stats .stat-item {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .about-hero h1 {
        font-size: 2rem;
    }
    
    .content-card,
    .value-card,
    .service-card,
    .team-card {
        padding: 1rem;
    }
}
</style>
@endpush
