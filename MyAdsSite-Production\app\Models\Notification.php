<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج الإشعارات
 * يدير إشعارات المستخدمين في النظام
 */
class Notification extends Model
{
    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',          // معرف المستخدم
        'title',            // عنوان الإشعار
        'message',          // نص الإشعار
        'type',             // نوع الإشعار
        'data',             // بيانات إضافية (JSON)
        'read_at',          // تاريخ القراءة
        'action_url',       // رابط الإجراء
        'priority',         // أولوية الإشعار
        'expires_at',       // تاريخ انتهاء الصلاحية
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * أنواع الإشعارات
     */
    const TYPE_INFO = 'info';
    const TYPE_SUCCESS = 'success';
    const TYPE_WARNING = 'warning';
    const TYPE_ERROR = 'error';
    const TYPE_AD_APPROVED = 'ad_approved';
    const TYPE_AD_REJECTED = 'ad_rejected';
    const TYPE_AD_EXPIRED = 'ad_expired';
    const TYPE_SYSTEM = 'system';

    /**
     * مستويات الأولوية
     */
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * علاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * البحث في الإشعارات غير المقروءة
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * البحث في الإشعارات المقروءة
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * البحث حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * البحث حسب الأولوية
     */
    public function scopeOfPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * البحث في الإشعارات النشطة (غير منتهية الصلاحية)
     */
    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * البحث في الإشعارات الحديثة
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * تحديد ما إذا كان الإشعار مقروءاً
     */
    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    /**
     * تحديد ما إذا كان الإشعار منتهي الصلاحية
     */
    public function isExpired(): bool
    {
        return $this->expires_at !== null && $this->expires_at < now();
    }

    /**
     * تحديد ما إذا كان الإشعار عالي الأولوية
     */
    public function isHighPriority(): bool
    {
        return in_array($this->priority, [self::PRIORITY_HIGH, self::PRIORITY_URGENT]);
    }

    /**
     * وضع علامة مقروء على الإشعار
     */
    public function markAsRead(): bool
    {
        if ($this->isRead()) {
            return true;
        }

        return $this->update(['read_at' => now()]);
    }

    /**
     * وضع علامة غير مقروء على الإشعار
     */
    public function markAsUnread(): bool
    {
        return $this->update(['read_at' => null]);
    }

    /**
     * إنشاء إشعار جديد
     */
    public static function createNotification(
        int $userId,
        string $title,
        string $message,
        string $type = self::TYPE_INFO,
        array $data = [],
        ?string $actionUrl = null,
        string $priority = self::PRIORITY_NORMAL,
        ?\DateTime $expiresAt = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'data' => $data,
            'action_url' => $actionUrl,
            'priority' => $priority,
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * إشعار موافقة على الإعلان
     */
    public static function adApproved(int $userId, Ad $ad): self
    {
        return self::createNotification(
            $userId,
            'تم قبول إعلانك',
            "تم قبول إعلان \"{$ad->title}\" ونشره بنجاح",
            self::TYPE_AD_APPROVED,
            ['ad_id' => $ad->id],
            route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]),
            self::PRIORITY_HIGH
        );
    }

    /**
     * إشعار رفض الإعلان
     */
    public static function adRejected(int $userId, Ad $ad, ?string $reason = null): self
    {
        $message = "تم رفض إعلان \"{$ad->title}\"";
        if ($reason) {
            $message .= "\nالسبب: {$reason}";
        }

        return self::createNotification(
            $userId,
            'تم رفض إعلانك',
            $message,
            self::TYPE_AD_REJECTED,
            ['ad_id' => $ad->id, 'reason' => $reason],
            route('my-ads.index'),
            self::PRIORITY_HIGH
        );
    }

    /**
     * إشعار انتهاء صلاحية الإعلان
     */
    public static function adExpired(int $userId, Ad $ad): self
    {
        return self::createNotification(
            $userId,
            'انتهت صلاحية إعلانك',
            "انتهت صلاحية إعلان \"{$ad->title}\". يمكنك تجديده من لوحة التحكم",
            self::TYPE_AD_EXPIRED,
            ['ad_id' => $ad->id],
            route('my-ads.index'),
            self::PRIORITY_NORMAL,
            now()->addDays(30) // ينتهي الإشعار بعد 30 يوم
        );
    }

    /**
     * إشعار النظام العام
     */
    public static function systemNotification(
        int $userId,
        string $title,
        string $message,
        string $priority = self::PRIORITY_NORMAL
    ): self {
        return self::createNotification(
            $userId,
            $title,
            $message,
            self::TYPE_SYSTEM,
            [],
            null,
            $priority
        );
    }

    /**
     * إرسال إشعار لجميع المستخدمين
     */
    public static function broadcastToAll(
        string $title,
        string $message,
        string $type = self::TYPE_SYSTEM,
        string $priority = self::PRIORITY_NORMAL
    ): int {
        $userIds = User::pluck('id');
        $count = 0;

        foreach ($userIds as $userId) {
            self::createNotification($userId, $title, $message, $type, [], null, $priority);
            $count++;
        }

        return $count;
    }

    /**
     * تنظيف الإشعارات القديمة
     */
    public static function cleanOldNotifications(int $daysToKeep = 90): int
    {
        return self::where('created_at', '<', now()->subDays($daysToKeep))
            ->where('priority', '!=', self::PRIORITY_URGENT)
            ->delete();
    }

    /**
     * الحصول على عدد الإشعارات غير المقروءة للمستخدم
     */
    public static function getUnreadCount(int $userId): int
    {
        return self::where('user_id', $userId)
            ->unread()
            ->active()
            ->count();
    }

    /**
     * وضع علامة مقروء على جميع إشعارات المستخدم
     */
    public static function markAllAsReadForUser(int $userId): int
    {
        return self::where('user_id', $userId)
            ->unread()
            ->update(['read_at' => now()]);
    }

    /**
     * الحصول على الإشعارات الحديثة للمستخدم
     */
    public static function getRecentForUser(int $userId, int $limit = 10)
    {
        return self::where('user_id', $userId)
            ->active()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * الحصول على لون النوع للعرض
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            self::TYPE_SUCCESS, self::TYPE_AD_APPROVED => 'success',
            self::TYPE_WARNING => 'warning',
            self::TYPE_ERROR, self::TYPE_AD_REJECTED => 'danger',
            self::TYPE_AD_EXPIRED => 'info',
            self::TYPE_SYSTEM => 'primary',
            default => 'secondary'
        };
    }

    /**
     * الحصول على أيقونة النوع للعرض
     */
    public function getTypeIconAttribute(): string
    {
        return match($this->type) {
            self::TYPE_SUCCESS, self::TYPE_AD_APPROVED => 'fas fa-check-circle',
            self::TYPE_WARNING => 'fas fa-exclamation-triangle',
            self::TYPE_ERROR, self::TYPE_AD_REJECTED => 'fas fa-times-circle',
            self::TYPE_AD_EXPIRED => 'fas fa-clock',
            self::TYPE_SYSTEM => 'fas fa-cog',
            default => 'fas fa-info-circle'
        };
    }
}
