<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('announcements', function (Blueprint $table) {
            $table->id();
            $table->string('title_ar')->comment('العنوان بالعربية');
            $table->string('title_en')->comment('العنوان بالإنجليزية');
            $table->text('content_ar')->comment('المحتوى بالعربية');
            $table->text('content_en')->comment('المحتوى بالإنجليزية');
            $table->string('icon', 50)->default('fas fa-bullhorn')->comment('أيقونة الرسالة');
            $table->string('color', 20)->default('text-warning')->comment('لون الأيقونة');
            $table->boolean('is_active')->default(true)->comment('حالة التفعيل');
            $table->integer('sort_order')->default(0)->comment('ترتيب الظهور');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('announcements');
    }
};
