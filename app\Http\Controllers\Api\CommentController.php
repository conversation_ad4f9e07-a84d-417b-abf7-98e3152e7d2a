<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\Comment;
use App\Models\Ad;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * كونترولر API للتعليقات
 * يدير إضافة وعرض وتحديث التعليقات والردود
 */
class CommentController extends Controller
{
    /**
     * عرض تعليقات إعلان معين
     */
    public function index(Request $request, int $adId): JsonResponse
    {
        try {
            // التحقق من وجود الإعلان
            $ad = Ad::find($adId);
            if (!$ad) {
                return response()->json([
                    'success' => false,
                    'message' => __('Ad not found')
                ], 404);
            }

            $perPage = $request->get('per_page', 10);
            $sortBy = $request->get('sort_by', 'latest'); // latest, oldest, most_liked, most_replied

            // بناء الاستعلام
            $query = Comment::byAd($adId)
                           ->mainComments()
                           ->approved()
                           ->with(['user:id,name,avatar', 'replies' => function ($q) use ($sortBy) {
                               $q->approved()->with('user:id,name,avatar');

                               switch ($sortBy) {
                                   case 'oldest':
                                       $q->oldest();
                                       break;
                                   case 'most_liked':
                                       $q->mostLiked();
                                       break;
                                   default:
                                       $q->oldest(); // الردود دائماً مرتبة من الأقدم للأحدث
                               }
                           }]);

            // ترتيب التعليقات الرئيسية
            switch ($sortBy) {
                case 'oldest':
                    $query->oldest();
                    break;
                case 'most_liked':
                    $query->mostLiked();
                    break;
                case 'most_replied':
                    $query->mostReplied();
                    break;
                default:
                    $query->pinned()->latest(); // المثبتة أولاً ثم الأحدث
            }

            $comments = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'comments' => $comments,
                'total_count' => Comment::getCommentsCount($adId)
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching comments: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * إضافة تعليق جديد
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // التحقق من تسجيل الدخول
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You must be logged in to comment')
                ], 401);
            }

            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'ad_id' => 'required|exists:ads,id',
                'content' => 'required|string|min:3|max:1000',
                'parent_id' => 'nullable|exists:comments,id',
                'language' => 'nullable|string|in:ar,en'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => __('Invalid data provided'),
                    'errors' => $validator->errors()
                ], 422);
            }

            $userId = Auth::id();
            $adId = $request->ad_id;

            // التحقق من وجود الإعلان وأنه نشط
            $ad = Ad::find($adId);
            if (!$ad || $ad->status !== 'active') {
                return response()->json([
                    'success' => false,
                    'message' => __('Ad not found or not active')
                ], 404);
            }

            // التحقق من التعليق الأب إذا كان موجوداً
            if ($request->parent_id) {
                $parentComment = Comment::find($request->parent_id);
                if (!$parentComment || !$parentComment->canReply()) {
                    return response()->json([
                        'success' => false,
                        'message' => __('Cannot reply to this comment')
                    ], 403);
                }
            }

            // إضافة التعليق
            $commentData = [
                'user_id' => $userId,
                'ad_id' => $adId,
                'content' => $request->content,
                'parent_id' => $request->parent_id,
                'language' => $request->language ?? 'ar'
            ];

            $comment = Comment::addComment($commentData);

            // تحميل بيانات المستخدم
            $comment->load('user:id,name,avatar');

            // تسجيل الحدث
            Log::info('Comment added', [
                'user_id' => $userId,
                'ad_id' => $adId,
                'comment_id' => $comment->id,
                'is_reply' => !empty($request->parent_id)
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Comment added successfully. It will be reviewed before appearing.'),
                'comment' => [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'status' => $comment->status,
                    'user' => $comment->user,
                    'created_at' => $comment->created_at->diffForHumans(),
                    'can_edit' => $comment->canEdit(),
                    'can_reply' => $comment->canReply()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error adding comment: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * تحديث تعليق
     */
    public function update(Request $request, int $commentId): JsonResponse
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You must be logged in')
                ], 401);
            }

            $comment = Comment::find($commentId);
            if (!$comment) {
                return response()->json([
                    'success' => false,
                    'message' => __('Comment not found')
                ], 404);
            }

            // التحقق من الصلاحية
            if (!$comment->canEdit()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You cannot edit this comment')
                ], 403);
            }

            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'content' => 'required|string|min:3|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => __('Invalid data provided'),
                    'errors' => $validator->errors()
                ], 422);
            }

            // تحديث التعليق
            $comment->update([
                'content' => strip_tags($request->content),
                'content_html' => \Illuminate\Support\Str::markdown($request->content),
                'is_edited' => true,
                'edited_at' => now(),
                'status' => 'pending' // إعادة المراجعة بعد التعديل
            ]);

            Log::info('Comment updated', [
                'user_id' => Auth::id(),
                'comment_id' => $comment->id
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Comment updated successfully. It will be reviewed again.'),
                'comment' => [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'status' => $comment->status,
                    'is_edited' => $comment->is_edited,
                    'edited_at' => $comment->edited_at->diffForHumans()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating comment: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * حذف تعليق
     */
    public function destroy(int $commentId): JsonResponse
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You must be logged in')
                ], 401);
            }

            $comment = Comment::find($commentId);
            if (!$comment) {
                return response()->json([
                    'success' => false,
                    'message' => __('Comment not found')
                ], 404);
            }

            $userId = Auth::id();

            // التحقق من الصلاحية (المالك أو الأدمن)
            if ($comment->user_id !== $userId && !auth()->user()->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => __('You cannot delete this comment')
                ], 403);
            }

            $adId = $comment->ad_id;
            $comment->delete();

            Log::info('Comment deleted', [
                'user_id' => $userId,
                'comment_id' => $commentId,
                'ad_id' => $adId
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Comment deleted successfully'),
                'total_count' => Comment::getCommentsCount($adId)
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting comment: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * إضافة/إزالة إعجاب
     */
    public function toggleLike(int $commentId): JsonResponse
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You must be logged in')
                ], 401);
            }

            $comment = Comment::find($commentId);
            if (!$comment) {
                return response()->json([
                    'success' => false,
                    'message' => __('Comment not found')
                ], 404);
            }

            // منع الإعجاب بالتعليق الخاص
            if ($comment->user_id === Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You cannot like your own comment')
                ], 403);
            }

            // هنا يمكن إضافة نظام أكثر تعقيداً لتتبع الإعجابات
            // للبساطة، سنزيد العداد فقط
            $comment->addLike();

            return response()->json([
                'success' => true,
                'message' => __('Thank you for your feedback'),
                'likes_count' => $comment->likes_count
            ]);

        } catch (\Exception $e) {
            Log::error('Error toggling comment like: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * الإبلاغ عن تعليق
     */
    public function report(Request $request, int $commentId): JsonResponse
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You must be logged in')
                ], 401);
            }

            $comment = Comment::find($commentId);
            if (!$comment) {
                return response()->json([
                    'success' => false,
                    'message' => __('Comment not found')
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'reason' => 'required|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => __('Invalid data provided'),
                    'errors' => $validator->errors()
                ], 422);
            }

            // تسجيل البلاغ (يمكن إنشاء جدول منفصل للبلاغات)
            Log::warning('Comment reported', [
                'reporter_id' => Auth::id(),
                'comment_id' => $commentId,
                'reason' => $request->reason,
                'comment_content' => $comment->content
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Thank you for reporting. We will review this comment.')
            ]);

        } catch (\Exception $e) {
            Log::error('Error reporting comment: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }
}
