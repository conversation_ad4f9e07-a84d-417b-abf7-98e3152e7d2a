<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration لإنشاء جدول إعدادات الخصوصية للمستخدمين
 * يحتوي على إعدادات الخصوصية المتقدمة لمعلومات التواصل
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_privacy_settings', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id();

            // معرف المستخدم - مفتاح خارجي
            $table->unsignedBigInteger('user_id')->unique();

            // إعدادات عرض معلومات التواصل
            $table->boolean('show_contacts_to_guests')->default(true); // عرض للزوار
            $table->boolean('show_contacts_to_registered')->default(true); // عرض للمسجلين
            $table->boolean('require_verification_to_view')->default(false); // يتطلب تحقق لعرض المعلومات

            // إعدادات الحماية من البريد المزعج
            $table->boolean('enable_contact_rate_limiting')->default(true); // تحديد معدل الوصول
            $table->integer('max_contact_views_per_hour')->default(10); // حد أقصى للمشاهدات في الساعة
            $table->integer('max_contact_views_per_day')->default(50); // حد أقصى للمشاهدات في اليوم

            // إعدادات التحقق
            $table->boolean('require_phone_verification')->default(false); // يتطلب تحقق من الهاتف
            $table->boolean('require_email_verification')->default(false); // يتطلب تحقق من الإيميل
            $table->boolean('auto_hide_unverified_contacts')->default(false); // إخفاء المعلومات غير المتحقق منها

            // إعدادات الإشعارات
            $table->boolean('notify_on_contact_view')->default(false); // إشعار عند مشاهدة معلومات التواصل
            $table->boolean('notify_on_contact_copy')->default(false); // إشعار عند نسخ معلومات التواصل
            $table->boolean('notify_on_suspicious_activity')->default(true); // إشعار عند نشاط مشبوه

            // إعدادات الأمان المتقدمة
            $table->boolean('enable_contact_encryption')->default(false); // تشفير معلومات التواصل
            $table->boolean('enable_watermark')->default(false); // إضافة علامة مائية
            $table->boolean('disable_right_click')->default(false); // تعطيل النقر الأيمن
            $table->boolean('disable_text_selection')->default(false); // تعطيل تحديد النص

            // إعدادات المشاركة
            $table->boolean('allow_contact_sharing')->default(true); // السماح بمشاركة معلومات التواصل
            $table->boolean('allow_contact_export')->default(false); // السماح بتصدير معلومات التواصل
            $table->boolean('show_contact_qr_code')->default(false); // عرض QR code لمعلومات التواصل

            // إعدادات التتبع
            $table->boolean('track_contact_interactions')->default(true); // تتبع التفاعلات
            $table->boolean('log_contact_access')->default(true); // تسجيل الوصول لمعلومات التواصل
            $table->boolean('enable_analytics')->default(false); // تفعيل التحليلات

            // تواريخ الإنشاء والتحديث
            $table->timestamps();

            // المفاتيح الخارجية
            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');

            // الفهارس
            $table->index('user_id', 'idx_privacy_settings_user_id');
            $table->index('show_contacts_to_guests', 'idx_privacy_show_guests');
            $table->index('require_verification_to_view', 'idx_privacy_require_verification');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_privacy_settings');
    }
};
