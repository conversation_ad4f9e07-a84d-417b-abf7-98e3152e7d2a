<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Cache;

/**
 * مزود خدمة الترجمات المبسط
 * يوفر cache بسيط للترجمات دون تعقيد
 */
class TranslationServiceProvider extends ServiceProvider
{
    /**
     * تسجيل الخدمات
     */
    public function register()
    {
        // لا حاجة لتسجيل خدمات معقدة
    }

    /**
     * تشغيل الخدمات
     */
    public function boot()
    {
        // تحسين بسيط للترجمات (فقط إذا كانت قاعدة البيانات متاحة)
        if ($this->app->runningInConsole() === false) {
            try {
                $this->cacheCommonTranslations();
            } catch (\Exception $e) {
                // تجاهل الأخطاء في حالة عدم توفر قاعدة البيانات
            }
        }
    }

    /**
     * تخزين الترجمات الشائعة في Cache
     */
    protected function cacheCommonTranslations()
    {
        // تخزين بسيط للترجمات الشائعة فقط
        $commonKeys = [
            'Welcome', 'Home', 'About', 'Contact', 'Login', 'Register',
            'Search', 'Categories', 'Ads', 'Profile', 'Settings'
        ];

        $locales = ['ar', 'en'];

        foreach ($locales as $locale) {
            $cacheKey = "common_translations.{$locale}";

            if (!Cache::has($cacheKey)) {
                $translations = [];
                foreach ($commonKeys as $key) {
                    $translations[$key] = __($key, [], $locale);
                }

                // تخزين لمدة ساعة واحدة
                Cache::put($cacheKey, $translations, 60);
            }
        }
    }
}


