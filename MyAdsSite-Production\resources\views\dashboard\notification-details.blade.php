@extends('layouts.app')

@section('title', __('Notification Details'))

@section('content')
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 arabic-text">
                        <i class="fas fa-bell me-2"></i>
                        {{ __('Notification Details') }}
                    </h1>
                    <p class="text-muted mb-0">{{ __('View notification details') }}</p>
                </div>

                <!-- Back Button -->
                <x-back-button :url="route('dashboard.notifications.index')" text="{{ __('Back to Notifications') }}" />
            </div>

            <!-- Notification Details Card -->
            <div class="card shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex align-items-center">
                        <!-- Icon -->
                        <div class="notification-icon me-3">
                            <i class="{{ $notification->type_icon }} text-{{ $notification->type_color }} fs-3"></i>
                        </div>

                        <!-- Title and Status -->
                        <div class="flex-grow-1">
                            <h4 class="mb-1">{{ $notification->title }}</h4>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-{{ $notification->type_color }} me-2">
                                    {{ ucfirst($notification->type) }}
                                </span>
                                @if($notification->isRead())
                                    <span class="badge bg-success">{{ __('Read') }}</span>
                                @else
                                    <span class="badge bg-warning">{{ __('Unread') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Priority -->
                        <div class="text-end">
                            <span class="badge bg-{{ $notification->priority === 'high' ? 'danger' : ($notification->priority === 'normal' ? 'primary' : 'secondary') }}">
                                {{ ucfirst($notification->priority) }} {{ __('Priority') }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Message -->
                    <div class="mb-4">
                        <h5 class="text-muted mb-2">{{ __('Message') }}:</h5>
                        <div class="alert alert-light border">
                            <p class="mb-0 fs-6">{{ $notification->message }}</p>
                        </div>
                    </div>

                    <!-- Additional Data -->
                    @if($notification->data && count($notification->data) > 0)
                        <div class="mb-4">
                            <h5 class="text-muted mb-2">{{ __('Additional Information') }}:</h5>
                            <div class="alert alert-info border">
                                @foreach($notification->data as $key => $value)
                                    <div class="row mb-2">
                                        <div class="col-sm-4">
                                            <strong>{{ ucfirst(str_replace('_', ' ', $key)) }}:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            {{ is_array($value) ? json_encode($value) : $value }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Timestamps -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">{{ __('Created At') }}:</h6>
                            <p class="mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>
                                {{ \Carbon\Carbon::parse($notification->created_at)->format('Y-m-d H:i:s') }}
                                <small class="text-muted">({{ \Carbon\Carbon::parse($notification->created_at)->diffForHumans() }})</small>
                            </p>
                        </div>

                        @if($notification->read_at)
                            <div class="col-md-6">
                                <h6 class="text-muted">{{ __('Read At') }}:</h6>
                                <p class="mb-0">
                                    <i class="fas fa-eye me-2"></i>
                                    {{ \Carbon\Carbon::parse($notification->read_at)->format('Y-m-d H:i:s') }}
                                    <small class="text-muted">({{ \Carbon\Carbon::parse($notification->read_at)->diffForHumans() }})</small>
                                </p>
                            </div>
                        @endif
                    </div>

                    <!-- Expiry Date -->
                    @if($notification->expires_at)
                        <div class="mb-4">
                            <h6 class="text-muted">{{ __('Expires At') }}:</h6>
                            <p class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                {{ \Carbon\Carbon::parse($notification->expires_at)->format('Y-m-d H:i:s') }}
                                <small class="text-muted">({{ \Carbon\Carbon::parse($notification->expires_at)->diffForHumans() }})</small>
                                @if(\Carbon\Carbon::parse($notification->expires_at)->isPast())
                                    <span class="badge bg-danger ms-2">{{ __('Expired') }}</span>
                                @endif
                            </p>
                        </div>
                    @endif
                </div>

                <!-- Actions -->
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            @if($notification->action_url)
                                @php
                                    $isExternalLink = str_starts_with($notification->action_url, 'http');
                                    $isAdminLink = str_contains($notification->action_url, '/admin/');
                                    $canAccessAdmin = auth()->user()->hasRole('admin') ?? false;
                                @endphp

                                @if(!$isAdminLink || $canAccessAdmin)
                                    <a href="{{ $notification->action_url }}"
                                       class="btn btn-primary"
                                       @if($isExternalLink) target="_blank" @endif>
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        {{ __('Go to Related Page') }}
                                    </a>
                                @else
                                    <button class="btn btn-secondary" disabled>
                                        <i class="fas fa-lock me-2"></i>
                                        {{ __('Admin Access Required') }}
                                    </button>
                                @endif
                            @endif
                        </div>

                        <div>
                            @if(!$notification->isRead())
                                <button type="button" class="btn btn-success me-2" id="markAsRead">
                                    <i class="fas fa-check me-2"></i>
                                    {{ __('Mark as Read') }}
                                </button>
                            @endif

                            <button type="button" class="btn btn-danger" id="deleteNotification">
                                <i class="fas fa-trash me-2"></i>
                                {{ __('Delete') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.notification-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.alert {
    border-radius: 8px;
}

.card {
    border-radius: 12px;
    border: none;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
}

.card-footer {
    border-radius: 0 0 12px 12px !important;
}

.badge {
    font-size: 0.75rem;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mark as read
    document.getElementById('markAsRead')?.addEventListener('click', function() {
        fetch(`/dashboard/notifications/{{ $notification->id }}/read`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{{ __("Failed to mark notification as read") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ __("An error occurred") }}');
        });
    });

    // Delete notification
    document.getElementById('deleteNotification').addEventListener('click', function() {
        if (confirm('{{ __("Are you sure you want to delete this notification?") }}')) {
            fetch(`/dashboard/notifications/{{ $notification->id }}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '{{ route("dashboard.notifications.index") }}';
                } else {
                    alert(data.message || '{{ __("Failed to delete notification") }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ __("An error occurred") }}');
            });
        }
    });
});
</script>
@endpush
