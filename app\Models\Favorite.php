<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use App\Services\CacheService;

/**
 * نموذج المفضلة (Favorites/Wishlist)
 * يحتوي على الإعلانات المحفوظة في المفضلة لكل مستخدم
 */
class Favorite extends Model
{
    /**
     * اسم الجدول
     */
    protected $table = 'favorites';

    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',    // معرف المستخدم
        'ad_id',      // معرف الإعلان
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'user_id' => 'integer',
        'ad_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * الأحداث التي تحدث عند إنشاء أو حذف مفضلة
     */
    protected static function boot()
    {
        parent::boot();

        // مسح الكاش عند إضافة أو حذف مفضلة
        static::created(function ($favorite) {
            CacheService::clearUserCache($favorite->user_id);
        });

        static::deleted(function ($favorite) {
            CacheService::clearUserCache($favorite->user_id);
        });
    }

    /**
     * علاقة متعدد إلى واحد مع المستخدمين
     * مفضلة واحدة تنتمي إلى مستخدم واحد
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * علاقة متعدد إلى واحد مع الإعلانات
     * مفضلة واحدة تنتمي إلى إعلان واحد
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * البحث في مفضلة مستخدم معين
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * البحث في مفضلة إعلان معين
     */
    public function scopeByAd(Builder $query, int $adId): Builder
    {
        return $query->where('ad_id', $adId);
    }

    /**
     * البحث في المفضلة مع الإعلانات النشطة فقط
     */
    public function scopeWithActiveAds(Builder $query): Builder
    {
        return $query->whereHas('ad', function ($q) {
            $q->active()->notExpired();
        });
    }

    /**
     * ترتيب حسب الأحدث
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * ترتيب حسب الأقدم
     */
    public function scopeOldest(Builder $query): Builder
    {
        return $query->orderBy('created_at', 'asc');
    }

    /**
     * التحقق من وجود إعلان في مفضلة المستخدم
     */
    public static function isFavorited(int $userId, int $adId): bool
    {
        return static::where('user_id', $userId)
                    ->where('ad_id', $adId)
                    ->exists();
    }

    /**
     * إضافة إعلان إلى المفضلة
     */
    public static function addToFavorites(int $userId, int $adId): bool
    {
        try {
            // التحقق من عدم وجود الإعلان في المفضلة مسبقاً
            if (static::isFavorited($userId, $adId)) {
                return false; // الإعلان موجود مسبقاً
            }

            // إضافة الإعلان إلى المفضلة
            static::create([
                'user_id' => $userId,
                'ad_id' => $adId,
            ]);

            return true;
        } catch (\Exception $e) {
            \Log::error('خطأ في إضافة إعلان إلى المفضلة: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إزالة إعلان من المفضلة
     */
    public static function removeFromFavorites(int $userId, int $adId): bool
    {
        try {
            $deleted = static::where('user_id', $userId)
                           ->where('ad_id', $adId)
                           ->delete();

            return $deleted > 0;
        } catch (\Exception $e) {
            \Log::error('خطأ في إزالة إعلان من المفضلة: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تبديل حالة الإعلان في المفضلة (إضافة/إزالة)
     */
    public static function toggleFavorite(int $userId, int $adId): array
    {
        try {
            if (static::isFavorited($userId, $adId)) {
                // إزالة من المفضلة
                $success = static::removeFromFavorites($userId, $adId);
                return [
                    'success' => $success,
                    'action' => 'removed',
                    'is_favorited' => false,
                    'message' => $success ? 'تم إزالة الإعلان من المفضلة' : 'فشل في إزالة الإعلان من المفضلة'
                ];
            } else {
                // إضافة إلى المفضلة
                $success = static::addToFavorites($userId, $adId);
                return [
                    'success' => $success,
                    'action' => 'added',
                    'is_favorited' => true,
                    'message' => $success ? 'تم إضافة الإعلان إلى المفضلة' : 'فشل في إضافة الإعلان إلى المفضلة'
                ];
            }
        } catch (\Exception $e) {
            \Log::error('خطأ في تبديل حالة المفضلة: ' . $e->getMessage());
            return [
                'success' => false,
                'action' => 'error',
                'is_favorited' => static::isFavorited($userId, $adId),
                'message' => 'حدث خطأ غير متوقع'
            ];
        }
    }

    /**
     * الحصول على عدد الإعلانات في مفضلة المستخدم
     */
    public static function getUserFavoritesCount(int $userId): int
    {
        return static::byUser($userId)->withActiveAds()->count();
    }

    /**
     * الحصول على مفضلة المستخدم مع الإعلانات
     */
    public static function getUserFavoritesWithAds(int $userId, int $perPage = 12)
    {
        return static::byUser($userId)
                    ->withActiveAds()
                    ->with(['ad.category', 'ad.user'])
                    ->latest()
                    ->paginate($perPage);
    }

    /**
     * الحصول على عدد المرات التي تم إضافة الإعلان للمفضلة
     */
    public static function getAdFavoritesCount(int $adId): int
    {
        return static::byAd($adId)->count();
    }

    /**
     * تنظيف المفضلة من الإعلانات المحذوفة أو المنتهية الصلاحية
     */
    public static function cleanup(): int
    {
        // حذف المفضلة للإعلانات المحذوفة أو غير النشطة
        return static::whereDoesntHave('ad', function ($query) {
            $query->active()->notExpired();
        })->delete();
    }
}
