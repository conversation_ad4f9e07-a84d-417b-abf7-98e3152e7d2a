<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Meta tags للوضع المظلم وإمكانية الوصول -->
    <meta name="theme-color" content="#ffffff">
    <meta name="color-scheme" content="light dark">
    <meta name="supported-color-schemes" content="light dark">
    @auth
        <meta name="user-authenticated" content="true">
    @endauth

    <!-- عنوان الصفحة -->
    <title>@yield('title', __('site_name') . ' - ' . __('Home Page'))</title>

    <!-- وصف الصفحة لمحركات البحث -->
    <meta name="description" content="@yield('description', __('Comprehensive advertising platform for educational, healthcare and commercial institutions'))">
    <meta name="keywords" content="{{ __('ads, schools, universities, hospitals, companies, Yemen, Middle East') }}">
    <meta name="author" content="MyAdsSite">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- أيقونة الموقع -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- خطوط جوجل العربية -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Bootstrap 5 RTL -->
    @if(app()->getLocale() === 'ar')
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet" id="bootstrap-css">
    @else
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
    @endif

    <!-- إضافة meta tag للغة الحالية -->
    <meta name="current-locale" content="{{ app()->getLocale() }}">
    <meta name="text-direction" content="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">

    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Font Awesome Fallback -->
    <script>
        // التحقق من تحميل Font Awesome وإضافة fallback إذا لزم الأمر
        document.addEventListener('DOMContentLoaded', function() {
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-home';
            testIcon.style.position = 'absolute';
            testIcon.style.left = '-9999px';
            document.body.appendChild(testIcon);

            const computedStyle = window.getComputedStyle(testIcon, ':before');
            const content = computedStyle.getPropertyValue('content');

            // إذا لم يتم تحميل Font Awesome، استخدم fallback
            if (!content || content === 'none' || content === '') {
                console.warn('Font Awesome failed to load, using fallback');
                const fallbackLink = document.createElement('link');
                fallbackLink.rel = 'stylesheet';
                fallbackLink.href = 'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css';
                fallbackLink.crossOrigin = 'anonymous';
                document.head.appendChild(fallbackLink);
            }

            document.body.removeChild(testIcon);
        });
    </script>

    <!-- الأنماط المخصصة -->
    <style>
        :root {
            /* ألوان النظام */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --dark-bg: #1f2937;
            --dark-surface: #374151;
            --light-bg: #f8fafc;
            --light-surface: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        /* الخطوط المحسنة */
        body {
            font-family: 'Cairo', 'Roboto', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px; /* زيادة الحجم الأساسي */
        }

        /* تحسينات للنص العربي */
        .arabic-text {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-weight: 500;
            letter-spacing: 0.3px;
            line-height: 1.8;
            font-size: 1.1rem; /* زيادة حجم النص العربي */
        }

        /* تحسين أحجام العناوين */
        h1, .h1 { font-size: 2.8rem; font-weight: 700; margin-bottom: 1.5rem; }
        h2, .h2 { font-size: 2.4rem; font-weight: 600; margin-bottom: 1.3rem; }
        h3, .h3 { font-size: 2rem; font-weight: 600; margin-bottom: 1.2rem; }
        h4, .h4 { font-size: 1.7rem; font-weight: 500; margin-bottom: 1rem; }
        h5, .h5 { font-size: 1.4rem; font-weight: 500; margin-bottom: 0.8rem; }
        h6, .h6 { font-size: 1.2rem; font-weight: 500; margin-bottom: 0.6rem; }

        /* تحسين أحجام النصوص */
        p { font-size: 1.1rem; line-height: 1.8; margin-bottom: 1.2rem; }
        .lead { font-size: 1.3rem; font-weight: 400; line-height: 1.8; }
        .text-muted { font-size: 1rem; }
        small, .small { font-size: 0.95rem; }

        /* تحسين شريط التنقل */
        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar-custom .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }

        .navbar-custom .navbar-nav .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .navbar-custom .navbar-brand {
            color: white;
            font-weight: 700;
            font-size: 1.5rem;
        }

        /* تحسين النصوص العربية */
        .arabic-text {
            font-family: 'Cairo', sans-serif;
            font-weight: 400;
        }

        .english-text {
            font-family: 'Roboto', sans-serif;
        }

        /* شريط التنقل */
        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            box-shadow: var(--shadow);
            backdrop-filter: blur(10px);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem; /* زيادة حجم شعار الموقع */
            color: white !important;
            padding: 0.8rem 0;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            font-size: 1.1rem; /* زيادة حجم روابط التنقل */
            transition: all 0.3s ease;
            padding: 0.8rem 1.3rem !important; /* زيادة المسافات الداخلية */
            border-radius: 0.6rem;
            margin: 0.2rem 0.1rem;
        }

        .nav-link:hover {
            color: white !important;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        /* الأزرار المحسنة */
        .btn {
            font-size: 1.1rem; /* زيادة حجم نص الأزرار */
            padding: 0.8rem 1.8rem; /* زيادة المسافات الداخلية */
            border-radius: 0.6rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border-width: 2px;
        }

        .btn-sm {
            font-size: 1rem;
            padding: 0.6rem 1.4rem;
        }

        .btn-lg {
            font-size: 1.3rem;
            padding: 1rem 2.5rem;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 0.75rem;
            padding: 1rem 2.5rem; /* زيادة المسافات */
            font-weight: 600;
            font-size: 1.2rem; /* زيادة حجم النص */
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary-custom {
            background: linear-gradient(135deg, var(--secondary-color), #059669);
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        /* البطاقات */
        .card-custom {
            border: none;
            border-radius: 1rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            background: var(--light-surface);
        }

        .card-custom:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        /* التذييل */
        .footer-custom {
            background: linear-gradient(135deg, var(--dark-bg), var(--dark-surface));
            color: white;
            margin-top: auto;
        }

        /* الوضع الليلي */
        [data-theme="dark"] {
            --light-bg: #111827;
            --light-surface: #1f2937;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --border-color: #374151;
        }

        [data-theme="dark"] body {
            background-color: var(--light-bg);
            color: var(--text-primary);
        }

        [data-theme="dark"] .card-custom {
            background: var(--light-surface);
            color: var(--text-primary);
        }

        [data-theme="dark"] .text-muted {
            color: var(--text-secondary) !important;
        }

        [data-theme="dark"] .category-card small {
            color: var(--text-secondary) !important;
        }

        [data-theme="dark"] .bg-light {
            background-color: var(--light-bg) !important;
        }

        /* النصوص الثانوية المخصصة */
        .text-secondary-custom {
            color: var(--text-secondary);
        }

        [data-theme="dark"] .text-secondary-custom {
            color: var(--text-secondary) !important;
        }

        /* تحسينات الاستجابة المحسنة */
        @media (max-width: 768px) {
            body {
                font-size: 15px; /* تقليل قليل للجوال */
            }

            .navbar-brand {
                font-size: 1.5rem; /* حجم أكبر للجوال */
            }

            .nav-link {
                font-size: 1rem;
                padding: 0.7rem 1rem !important;
            }

            h1, .h1 { font-size: 2.2rem; }
            h2, .h2 { font-size: 1.9rem; }
            h3, .h3 { font-size: 1.6rem; }
            h4, .h4 { font-size: 1.4rem; }
            h5, .h5 { font-size: 1.2rem; }

            .btn {
                font-size: 1rem;
                padding: 0.7rem 1.5rem;
            }

            .btn-primary-custom {
                padding: 0.8rem 2rem;
                font-size: 1.1rem;
            }

            .card-body {
                padding: 1.5rem; /* تقليل قليل للجوال */
            }

            .card-header {
                padding: 1.2rem 1.5rem;
                font-size: 1.1rem;
            }
        }

        @media (max-width: 576px) {
            body {
                font-size: 14px;
            }

            .navbar-brand {
                font-size: 1.3rem;
            }

            .card-body {
                padding: 1.2rem;
            }

            .btn {
                font-size: 0.95rem;
                padding: 0.6rem 1.3rem;
            }
        }

        /* تأثيرات الانتقال */
        * {
            transition: color 0.3s ease, background-color 0.3s ease;
        }

        /* تحسين المسافات للعربية */
        .content-wrapper {
            min-height: calc(100vh - 200px);
        }

        /* رسائل التنبيه */
        .alert-custom {
            border: none;
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        /* تحسين البطاقات */
        .card {
            border-radius: 1rem;
            border: none;
            box-shadow: var(--shadow);
            margin-bottom: 2rem; /* زيادة المسافة بين البطاقات */
        }

        .card-body {
            padding: 2rem; /* زيادة المسافات الداخلية */
        }

        .card-header {
            padding: 1.5rem 2rem;
            font-size: 1.2rem;
            font-weight: 600;
            border-bottom: 2px solid var(--border-color);
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .card-text {
            font-size: 1.1rem;
            line-height: 1.7;
        }

        /* تحسين الأيقونات */
        .fas, .far, .fab, .fa {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands", "FontAwesome" !important;
            font-weight: 900;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
            font-size: 1.2em; /* زيادة حجم الأيقونات العامة */
        }

        .far {
            font-weight: 400 !important;
        }

        .fab {
            font-weight: 400 !important;
            font-family: "Font Awesome 6 Brands" !important;
        }

        .fa-2x { font-size: 2.5em !important; }
        .fa-3x { font-size: 3.5em !important; }
        .fa-4x { font-size: 4.5em !important; }
        .fa-5x { font-size: 5.5em !important; }

        /* Fallback للأيقونات إذا لم يتم تحميل Font Awesome */
        .icon-fallback {
            display: inline-block;
            width: 1em;
            height: 1em;
            text-align: center;
            line-height: 1;
        }

        .fa-home::before { content: "🏠"; }
        .fa-bullhorn::before { content: "📢"; }
        .fa-list::before { content: "📋"; }
        .fa-info-circle::before { content: "ℹ️"; }
        .fa-comments::before { content: "💬"; }
        .fa-envelope::before { content: "✉️"; }
        .fa-user::before { content: "👤"; }
        .fa-cog::before { content: "⚙️"; }
        .fa-moon::before { content: "🌙"; }
        .fa-sun::before { content: "☀️"; }
        .fa-globe::before { content: "🌐"; }
        .fa-bars::before { content: "☰"; }

        /* تحسين النماذج */
        .form-control {
            border-radius: 0.6rem;
            border: 2px solid var(--border-color);
            padding: 1rem 1.3rem; /* زيادة المسافات الداخلية */
            font-size: 1.1rem; /* زيادة حجم النص */
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.3rem rgba(37, 99, 235, 0.25);
        }

        .form-label {
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 0.8rem;
        }

        /* تحسين تصميم Pagination */
        .pagination {
            --bs-pagination-padding-x: 0.5rem;
            --bs-pagination-padding-y: 0.25rem;
            --bs-pagination-font-size: 0.875rem;
            --bs-pagination-border-radius: 0.375rem;
            gap: 0.25rem;
        }

        .pagination .page-link {
            border: 1px solid #dee2e6;
            color: #6c757d;
            background-color: #fff;
            border-radius: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.25;
            transition: all 0.15s ease-in-out;
            min-width: 2.5rem;
            text-align: center;
        }

        .pagination .page-link:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
            color: #495057;
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: #fff;
        }

        .pagination .page-item.disabled .page-link {
            color: #adb5bd;
            background-color: #fff;
            border-color: #dee2e6;
            cursor: not-allowed;
        }

        .pagination .page-link i {
            font-size: 0.75rem;
        }

        /* تحسين عرض معلومات الصفحة */
        .pagination-info {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
    </style>

    <!-- أنماط إضافية للصفحة -->
    @stack('styles')
</head>

<body>


    <div id="app" class="d-flex flex-column min-vh-100">

        <!-- شريط التنقل -->
        @include('components.navbar')

        <!-- شريط الإعلانات -->
        @include('components.announcement-bar')

        <!-- المحتوى الرئيسي -->
        <main id="main-content" class="flex-grow-1" role="main">


            @if($errors->any())
                <div class="container mt-3">
                    <div class="alert alert-danger alert-custom alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>{{ __('Please correct the following errors') }}:</strong>
                        <ul class="mb-0 mt-2">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            @endif

            <!-- محتوى الصفحة -->
            <div class="content-wrapper">
                @yield('content')
            </div>
        </main>

        <!-- التذييل -->
        @include('components.footer')
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript محسن ومبسط - تم إزالة التكرار مع theme-switcher.js -->
    <script>
        // تطبيق الوضع المحفوظ فقط (باقي الوظائف في theme-switcher.js)
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            // تحديث أيقونة الوضع
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        });

        // دالة مبسطة لتبديل الوضع (تستخدم ThemeSwitcher إذا كان متاح)
        function toggleTheme() {
            if (window.themeSwitcher) {
                window.themeSwitcher.toggleTheme();
            } else {
                // fallback بسيط
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                const themeIcon = document.getElementById('theme-icon');
                if (themeIcon) {
                    themeIcon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
                }
            }
        }

        // مراقبة تغيير اللغة وتحديث الصفحة
        function checkLanguageChange() {
            const currentLocale = document.querySelector('meta[name="current-locale"]')?.content;
            const storedLocale = localStorage.getItem('expected-locale');

            if (storedLocale && storedLocale !== currentLocale) {
                // اللغة تغيرت، قم بتحديث الصفحة
                localStorage.removeItem('expected-locale');
                window.location.reload(true);
            }
        }

        // تشغيل فحص اللغة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkLanguageChange();

            // مراقبة تغييرات URL للغة
            if (window.location.search.includes('refresh=1')) {
                // إزالة معاملات التحديث من URL
                const url = new URL(window.location);
                url.searchParams.delete('refresh');
                url.searchParams.delete('_t');
                window.history.replaceState({}, document.title, url.toString());
            }

            // مراقبة تغيير اللغة عبر headers
            const currentLocale = document.querySelector('meta[name="current-locale"]')?.content;
            if (currentLocale) {
                document.documentElement.setAttribute('lang', currentLocale);
                document.documentElement.setAttribute('dir', currentLocale === 'ar' ? 'rtl' : 'ltr');

                // تحديث Bootstrap CSS إذا لزم الأمر
                updateBootstrapCSS(currentLocale);
            }
        });

        // دالة تحديث Bootstrap CSS
        function updateBootstrapCSS(locale) {
            const bootstrapLink = document.getElementById('bootstrap-css');
            if (bootstrapLink) {
                const isRTL = locale === 'ar';
                const newHref = isRTL
                    ? 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css'
                    : 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css';

                if (bootstrapLink.href !== newHref) {
                    bootstrapLink.href = newHref;
                }
            }
        });
    </script>

    <!-- JavaScript إضافي للصفحة -->
    @stack('scripts')

    <!-- مكون Toast للرسائل المنبثقة -->
    @include('components.toast')
</body>
</html>
