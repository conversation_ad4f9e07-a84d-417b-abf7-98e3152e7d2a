<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware للتحكم في معدل الطلبات (Rate Limiting)
 * يحمي التطبيق من الهجمات والاستخدام المفرط
 */
class RateLimiting
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $type = 'general'): Response
    {
        $key = $this->resolveRequestSignature($request, $type);
        $maxAttempts = $this->getMaxAttempts($type);
        $decayMinutes = $this->getDecayMinutes($type);

        // التحقق من تجاوز الحد المسموح
        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $this->logRateLimitExceeded($request, $type, $key);
            return $this->buildRateLimitResponse($key, $maxAttempts);
        }

        // زيادة عداد الطلبات
        RateLimiter::hit($key, $decayMinutes * 60);

        $response = $next($request);

        // إضافة headers معلومات Rate Limiting
        return $this->addRateLimitHeaders($response, $key, $maxAttempts);
    }

    /**
     * إنشاء مفتاح فريد للطلب
     */
    private function resolveRequestSignature(Request $request, string $type): string
    {
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        $path = $request->path();
        
        // إضافة معرف المستخدم إذا كان مسجل دخول
        $userId = auth()->id() ?? 'guest';
        
        return "rate_limit:{$type}:{$ip}:{$userId}:" . md5($userAgent . $path);
    }

    /**
     * الحصول على الحد الأقصى للطلبات حسب النوع
     */
    private function getMaxAttempts(string $type): int
    {
        return match($type) {
            'login' => 5,           // 5 محاولات تسجيل دخول
            'register' => 3,        // 3 محاولات تسجيل
            'contact' => 2,         // رسالتان اتصال
            'api' => 60,           // 60 طلب API
            'search' => 30,        // 30 عملية بحث
            'upload' => 10,        // 10 عمليات رفع ملفات
            'admin' => 100,        // 100 طلب للإدارة
            'general' => 120,      // 120 طلب عام
            default => 60
        };
    }

    /**
     * الحصول على مدة انتهاء الصلاحية بالدقائق
     */
    private function getDecayMinutes(string $type): int
    {
        return match($type) {
            'login' => 15,         // 15 دقيقة لتسجيل الدخول
            'register' => 60,      // ساعة للتسجيل
            'contact' => 60,       // ساعة للاتصال
            'api' => 1,           // دقيقة واحدة للـ API
            'search' => 1,        // دقيقة واحدة للبحث
            'upload' => 60,       // ساعة لرفع الملفات
            'admin' => 1,         // دقيقة واحدة للإدارة
            'general' => 1,       // دقيقة واحدة عام
            default => 1
        };
    }

    /**
     * تسجيل تجاوز الحد المسموح
     */
    private function logRateLimitExceeded(Request $request, string $type, string $key): void
    {
        Log::warning('Rate limit exceeded', [
            'type' => $type,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'path' => $request->path(),
            'method' => $request->method(),
            'user_id' => auth()->id(),
            'key' => $key,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * إنشاء استجابة تجاوز الحد
     */
    private function buildRateLimitResponse(string $key, int $maxAttempts): Response
    {
        $retryAfter = RateLimiter::availableIn($key);
        
        $message = __('تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة مرة أخرى بعد :seconds ثانية.', [
            'seconds' => $retryAfter
        ]);

        return response()->json([
            'error' => 'Rate limit exceeded',
            'message' => $message,
            'retry_after' => $retryAfter,
            'max_attempts' => $maxAttempts
        ], 429)->header('Retry-After', $retryAfter);
    }

    /**
     * إضافة headers معلومات Rate Limiting
     */
    private function addRateLimitHeaders(Response $response, string $key, int $maxAttempts): Response
    {
        $remaining = RateLimiter::remaining($key, $maxAttempts);
        $retryAfter = RateLimiter::availableIn($key);

        $response->headers->set('X-RateLimit-Limit', $maxAttempts);
        $response->headers->set('X-RateLimit-Remaining', max(0, $remaining));
        
        if ($remaining === 0) {
            $response->headers->set('X-RateLimit-Reset', now()->addSeconds($retryAfter)->timestamp);
        }

        return $response;
    }
}
