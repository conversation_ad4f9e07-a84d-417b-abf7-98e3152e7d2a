@extends('layouts.app')

@section('title', __('Search Results') . ' - ' . __('site_name'))
@section('description', __('Search in schools, universities, hospitals ads...'))

@section('content')
<div class="container py-4">
    <!-- شريط البحث الرئيسي -->
    <div class="row justify-content-center mb-4">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0" data-animation="fadeInDown">
                <div class="card-body p-4">
                    <form method="GET" action="{{ route('search.index') }}" class="search-form" id="unifiedSearchForm">
                        <div class="row align-items-end">
                            <!-- حقل البحث الرئيسي -->
                            <div class="col-lg-6 col-md-8 mb-3">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-search me-2 text-primary"></i>
                                    {{ __('Search') }}
                                </label>
                                <input type="text"
                                       name="q"
                                       class="form-control form-control-lg"
                                       placeholder="{{ __('Search in schools, universities, hospitals ads...') }}"
                                       value="{{ $query }}"
                                       autocomplete="off"
                                       id="search-input">
                            </div>

                            <!-- التصنيف السريع -->
                            <div class="col-lg-3 col-md-4 mb-3">
                                <label class="form-label fw-bold">{{ __('Category') }}</label>
                                <select name="category_id" class="form-select form-select-lg">
                                    <option value="">{{ __('All Categories') }}</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}"
                                                {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- أزرار البحث -->
                            <div class="col-lg-3 mb-3">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search me-2"></i>
                                        {{ __('Search') }}
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="toggleAdvancedFilters">
                                        <i class="fas fa-sliders-h me-2"></i>
                                        {{ __('Advanced Filters') }}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- الفلاتر المتقدمة (مخفية افتراضياً) -->
                        <div id="advancedFilters" class="advanced-filters mt-4" style="display: none;">
                            <hr class="my-4">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-filter me-2 text-primary"></i>
                                {{ __('Advanced Filters') }}
                            </h6>

                            <div class="row">
                                <!-- الموقع -->
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label class="form-label">{{ __('Location') }}</label>
                                    <input type="text"
                                           class="form-control"
                                           name="location"
                                           value="{{ request('location') }}"
                                           placeholder="{{ __('Enter city or area') }}">
                                </div>

                                <!-- تاريخ النشر -->
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label class="form-label">{{ __('Publication Date') }}</label>
                                    <select name="date_range" class="form-select">
                                        <option value="">{{ __('Any time') }}</option>
                                        <option value="today" {{ request('date_range') == 'today' ? 'selected' : '' }}>{{ __('Today') }}</option>
                                        <option value="week" {{ request('date_range') == 'week' ? 'selected' : '' }}>{{ __('This week') }}</option>
                                        <option value="month" {{ request('date_range') == 'month' ? 'selected' : '' }}>{{ __('This month') }}</option>
                                        <option value="year" {{ request('date_range') == 'year' ? 'selected' : '' }}>{{ __('This year') }}</option>
                                    </select>
                                </div>

                                <!-- الترتيب -->
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label class="form-label">{{ __('Sort Results') }}</label>
                                    <select name="sort_by" class="form-select">
                                        <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>{{ __('Latest') }}</option>
                                        <option value="views" {{ request('sort_by') == 'views' ? 'selected' : '' }}>{{ __('Most Viewed') }}</option>
                                        <option value="title" {{ request('sort_by') == 'title' ? 'selected' : '' }}>{{ __('Name') }}</option>
                                        <option value="featured" {{ request('sort_by') == 'featured' ? 'selected' : '' }}>{{ __('Featured First') }}</option>
                                    </select>
                                </div>

                                <!-- خيارات إضافية -->
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label class="form-label">{{ __('Additional Options') }}</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               name="featured_only" value="1"
                                               id="featuredOnly"
                                               {{ request('featured_only') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="featuredOnly">
                                            {{ __('Featured ads only') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- اقتراحات البحث -->
                        <div id="search-suggestions" class="search-suggestions d-none mt-3">
                            <div class="list-group"></div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- إشعار التصحيح الإملائي -->
    @if(session('search_correction'))
        <div class="row justify-content-center mb-3">
            <div class="col-lg-8">
                <div class="alert alert-info alert-dismissible fade show">
                    <i class="fas fa-spell-check me-2"></i>
                    {{ __('Spell check correction', [
                        'corrected' => session('search_correction.corrected'),
                        'original' => session('search_correction.original')
                    ]) }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    @endif

    <div class="row">
        <!-- الشريط الجانبي للاقتراحات -->
        <div class="col-lg-3 mb-4">
            <!-- إحصائيات البحث -->
            @if(!empty($query))
                <div class="card border-0 shadow-sm mb-4" data-animation="fadeInLeft">
                    <div class="card-header bg-light border-0">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            {{ __('Search Statistics') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="arabic-text">{{ __('Total Results') }}:</span>
                            <strong class="text-primary">{{ number_format($totalResults) }}</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="arabic-text">{{ __('Search Term') }}:</span>
                            <span class="text-muted">"{{ $query }}"</span>
                        </div>
                        @if(request('category_id'))
                            <div class="d-flex justify-content-between">
                                <span class="arabic-text">{{ __('Category') }}:</span>
                                <span class="text-info">{{ $categories->find(request('category_id'))->name ?? '' }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- التصنيفات السريعة -->
            <div class="card border-0 shadow-sm mb-4" data-animation="fadeInLeft">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-th-large me-2"></i>
                        {{ __('Quick Categories') }}
                    </h6>
                </div>
                <div class="card-body">
                    @foreach($categories->take(8) as $category)
                        <a href="{{ route('search.index', ['category_id' => $category->id]) }}"
                           class="btn btn-outline-primary btn-sm me-1 mb-2">
                            <i class="{{ $category->icon }} me-1"></i>
                            {{ $category->name }}
                        </a>
                    @endforeach
                </div>
            </div>

            <!-- البحثات الشائعة -->
            @if(!empty($popularSearches))
                <div class="card border-0 shadow-sm mt-4" data-animation="fadeInLeft">
                    <div class="card-header bg-light border-0">
                        <h6 class="mb-0">
                            <i class="fas fa-fire text-danger me-2"></i>
                            البحثات الشائعة
                        </h6>
                    </div>
                    <div class="card-body">
                        @foreach($popularSearches as $search)
                            <a href="{{ route('search.index', ['q' => $search]) }}"
                               class="badge bg-light text-dark text-decoration-none me-1 mb-2">
                                {{ $search }}
                            </a>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- البحثات الحديثة -->
            @if(!empty($recentSearches))
                <div class="card border-0 shadow-sm mt-4" data-animation="fadeInLeft">
                    <div class="card-header bg-light border-0">
                        <h6 class="mb-0">
                            <i class="fas fa-history text-info me-2"></i>
                            بحثاتك الحديثة
                        </h6>
                    </div>
                    <div class="card-body">
                        @foreach($recentSearches as $search)
                            <a href="{{ route('search.index', ['q' => $search]) }}"
                               class="d-block text-decoration-none mb-2 text-muted">
                                <i class="fas fa-search me-2"></i>
                                {{ $search }}
                            </a>
                        @endforeach
                        <button type="button" class="btn btn-sm btn-outline-danger w-100 mt-2" onclick="clearSearchHistory()">
                            <i class="fas fa-trash me-2"></i>
                            مسح السجل
                        </button>
                    </div>
                </div>
            @endif
        </div>

        <!-- النتائج -->
        <div class="col-lg-9">
            @if(!empty($query))
                <!-- معلومات النتائج -->
                <div class="d-flex justify-content-between align-items-center mb-4" data-animation="fadeInRight">
                    <h4 class="mb-0">
                        نتائج البحث عن: "<span class="text-primary">{{ $query }}</span>"
                    </h4>
                    <span class="text-muted">
                        {{ number_format($totalResults) }} نتيجة
                    </span>
                </div>

                @if($totalResults > 0)
                    <!-- عرض النتائج -->
                    <div class="row">
                        @foreach($results as $ad)
                            <div class="col-md-6 col-xl-4 mb-4" data-animation="fadeInUp">
                                @include('ads.card', ['ad' => $ad, 'noWrapper' => true])
                            </div>
                        @endforeach
                    </div>

                    <!-- التصفح -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $results->links('pagination.custom') }}
                    </div>
                @else
                    <!-- لا توجد نتائج -->
                    <div class="text-center py-5" data-animation="fadeIn">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted arabic-text">{{ __('No results found') }}</h4>
                        <p class="text-muted arabic-text">{{ __('Try using different words or reducing filters') }}</p>

                        <div class="mt-4">
                            <h6 class="arabic-text">{{ __('Suggestions:') }}</h6>
                            <ul class="list-unstyled">
                                <li class="arabic-text">• {{ __('Check spelling') }}</li>
                                <li class="arabic-text">• {{ __('Use fewer or more general words') }}</li>
                                <li class="arabic-text">• {{ __('Try searching in a different category') }}</li>
                            </ul>
                        </div>
                    </div>
                @endif
            @else
                <!-- صفحة البحث الفارغة -->
                <div class="text-center py-5" data-animation="fadeIn">
                    <i class="fas fa-search fa-4x text-primary mb-4"></i>
                    <h3 class="arabic-text">{{ __('Search thousands of ads') }}</h3>
                    <p class="text-muted lead arabic-text">{{ __('Discover ads from schools, universities, hospitals and companies') }}</p>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
/* الفلاتر المتقدمة */
.advanced-filters {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.advanced-filters.show {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين أزرار التصنيفات السريعة */
.btn-outline-primary.btn-sm {
    border-radius: 20px;
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
    transition: all 0.3s ease;
}

.btn-outline-primary.btn-sm:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* تحسين البحثات الشائعة */
.badge {
    font-size: 0.85rem;
    padding: 0.5rem 0.8rem;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسين شريط البحث الرئيسي */
.form-control-lg {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control-lg:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

.form-select-lg {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-select-lg:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين الأزرار */
.btn-lg {
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 123, 255, 0.3);
}

.btn-outline-secondary:hover {
    transform: translateY(-1px);
}

/* تحسين البطاقات */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* تحسين responsive */
@media (max-width: 768px) {
    .advanced-filters {
        padding: 1rem;
    }

    .btn-outline-primary.btn-sm {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
        margin-bottom: 0.5rem;
    }
}
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-suggestions .list-group-item {
    border: none;
    cursor: pointer;
    padding: 0.75rem 1rem;
}

.search-suggestions .list-group-item:hover {
    background-color: #f8f9fa;
}

.search-form {
    position: relative;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const suggestionsContainer = document.getElementById('search-suggestions');
    let searchTimeout;

    // اقتراحات البحث
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        clearTimeout(searchTimeout);

        if (query.length < 2) {
            suggestionsContainer.classList.add('d-none');
            return;
        }

        searchTimeout = setTimeout(() => {
            fetch(`{{ route('search.suggestions') }}?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.suggestions && data.suggestions.length > 0) {
                        showSuggestions(data.suggestions);
                    } else {
                        suggestionsContainer.classList.add('d-none');
                    }
                })
                .catch(() => {
                    suggestionsContainer.classList.add('d-none');
                });
        }, 300);
    });

    function showSuggestions(suggestions) {
        const listGroup = suggestionsContainer.querySelector('.list-group');
        listGroup.innerHTML = '';

        suggestions.forEach(suggestion => {
            const item = document.createElement('div');
            item.className = 'list-group-item list-group-item-action';
            item.textContent = suggestion;
            item.addEventListener('click', () => {
                searchInput.value = suggestion;
                suggestionsContainer.classList.add('d-none');
                document.querySelector('.search-form').submit();
            });
            listGroup.appendChild(item);
        });

        suggestionsContainer.classList.remove('d-none');
    }

    // إخفاء الاقتراحات عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
            suggestionsContainer.classList.add('d-none');
        }
    });
});

// مسح سجل البحث
function clearSearchHistory() {
    if (confirm('{{ __("Are you sure you want to clear search history?") }}')) {
        fetch('{{ route('search.clear-history') }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{{ __("An error occurred") }}');
            }
        })
        .catch(() => {
            alert('{{ __("Connection error occurred") }}');
        });
    }
}

// تبديل الفلاتر المتقدمة
document.getElementById('toggleAdvancedFilters').addEventListener('click', function() {
    const advancedFilters = document.getElementById('advancedFilters');
    const button = this;

    if (advancedFilters.style.display === 'none') {
        advancedFilters.style.display = 'block';
        button.innerHTML = '<i class="fas fa-chevron-up me-2"></i>{{ __("Hide Advanced Filters") }}';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-secondary');
    } else {
        advancedFilters.style.display = 'none';
        button.innerHTML = '<i class="fas fa-sliders-h me-2"></i>{{ __("Advanced Filters") }}';
        button.classList.remove('btn-secondary');
        button.classList.add('btn-outline-secondary');
    }
});

// إظهار الفلاتر المتقدمة إذا كانت هناك فلاتر مطبقة
document.addEventListener('DOMContentLoaded', function() {
    const hasAdvancedFilters = {{ request()->hasAny(['location', 'date_range', 'sort_by', 'featured_only']) ? 'true' : 'false' }};
    if (hasAdvancedFilters) {
        document.getElementById('toggleAdvancedFilters').click();
    }
});
</script>
@endpush
@endsection
