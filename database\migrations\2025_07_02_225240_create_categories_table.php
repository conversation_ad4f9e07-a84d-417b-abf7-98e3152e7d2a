<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();

            // أسماء التصنيفات باللغتين العربية والإنجليزية
            $table->string('name_ar'); // الاسم بالعربية
            $table->string('name_en'); // الاسم بالإنجليزية

            // رابط مختصر وودود للتصنيف (للاستخدام في الروابط)
            $table->string('slug')->unique();

            // فئة الأيقونة للعرض (Font Awesome أو Bootstrap Icons)
            $table->string('icon')->default('fas fa-folder');

            // وصف اختياري للتصنيف
            $table->text('description_ar')->nullable(); // الوصف بالعربية
            $table->text('description_en')->nullable(); // الوصف بالإنجليزية

            // حالة التصنيف (فعال أو غير فعال)
            $table->boolean('is_active')->default(true);

            $table->timestamps();

            // فهارس لتحسين الأداء
            $table->index(['name_ar'], 'categories_name_ar_index');
            $table->index(['name_en'], 'categories_name_en_index');
            $table->index(['is_active'], 'categories_is_active_index');
            $table->index(['slug'], 'categories_slug_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
