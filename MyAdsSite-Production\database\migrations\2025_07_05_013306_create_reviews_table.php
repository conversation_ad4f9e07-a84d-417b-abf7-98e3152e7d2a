<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();

            // معرف المستخدم الذي كتب التقييم
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // العنصر المُقيم (polymorphic)
            $table->unsignedBigInteger('reviewable_id');
            $table->string('reviewable_type');

            // محتوى التقييم
            $table->tinyInteger('rating')->unsigned(); // 1-5
            $table->string('title')->nullable();
            $table->text('comment')->nullable();

            // حالة التقييم
            $table->boolean('is_approved')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->integer('helpful_count')->default(0);
            $table->integer('reported_count')->default(0);

            // معلومات الموافقة
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');

            $table->timestamps();

            // فهارس لتحسين الأداء
            $table->index(['reviewable_id', 'reviewable_type'], 'reviews_reviewable_index');
            $table->index(['user_id'], 'reviews_user_index');
            $table->index(['rating'], 'reviews_rating_index');
            $table->index(['is_approved'], 'reviews_approved_index');
            $table->index(['is_verified'], 'reviews_verified_index');
            $table->index(['created_at'], 'reviews_created_at_index');
            $table->index(['helpful_count'], 'reviews_helpful_index');

            // فهرس مركب للبحث السريع
            $table->index(['reviewable_id', 'reviewable_type', 'is_approved'], 'reviews_reviewable_approved_index');

            // منع التقييم المكرر من نفس المستخدم لنفس العنصر
            $table->unique(['user_id', 'reviewable_id', 'reviewable_type'], 'reviews_user_reviewable_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
