<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Services\EncryptionService;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * الحقول القابلة للتعبئة الجماعية
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',                    // اسم المستخدم
        'email',                   // البريد الإلكتروني
        'password',                // كلمة المرور
        'card_number',             // رقم البطاقة
        'card_type',               // نوع البطاقة (كاك بنك، الكريمي، إلخ)
        'currency',                // نوع العملة (ريال يمني، دولار، ريال سعودي)
        'is_admin',                // هل المستخدم مدير
        'password_changed_at',     // تاريخ آخر تغيير لكلمة المرور
        'last_login_at',           // تاريخ آخر تسجيل دخول
        'last_login_ip',           // آخر IP لتسجيل الدخول
        'registration_ip',         // IP التسجيل
        'failed_login_attempts',   // عدد محاولات تسجيل الدخول الفاشلة
        'locked_until',            // مؤقت حتى
        'force_password_change',   // إجبار تغيير كلمة المرور
        'password_history',        // تاريخ كلمات المرور
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'password_history',
        'last_login_ip',
        'registration_ip',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_admin' => 'boolean',
            'password_changed_at' => 'datetime',
            'last_login_at' => 'datetime',
            'locked_until' => 'datetime',
            'force_password_change' => 'boolean',
            'password_history' => 'array',
        ];
    }

    /**
     * علاقة واحد إلى متعدد مع الإعلانات
     * مستخدم واحد يمكن أن يملك عدة إعلانات
     */
    public function ads(): HasMany
    {
        return $this->hasMany(Ad::class);
    }

    /**
     * الحصول على الإعلانات النشطة للمستخدم
     */
    public function activeAds()
    {
        return $this->hasMany(Ad::class)->where('status', 'active')->notExpired();
    }

    /**
     * الحصول على الإعلانات المعلقة للمستخدم
     */
    public function pendingAds(): HasMany
    {
        return $this->hasMany(Ad::class)->where('status', 'pending');
    }

    /**
     * تشفير رقم البطاقة عند الحفظ
     */
    public function setCardNumberAttribute($value)
    {
        $this->attributes['card_number'] = EncryptionService::encryptText($value);
    }

    /**
     * فك تشفير رقم البطاقة عند الاستدعاء
     */
    public function getCardNumberAttribute($value)
    {
        return EncryptionService::decryptText($value);
    }

    /**
     * الحصول على رقم البطاقة مخفي للعرض
     */
    public function getMaskedCardNumberAttribute()
    {
        $cardNumber = $this->card_number;
        if (empty($cardNumber)) {
            return null;
        }

        if (strlen($cardNumber) <= 4) {
            return str_repeat('*', strlen($cardNumber));
        }

        return str_repeat('*', strlen($cardNumber) - 4) . substr($cardNumber, -4);
    }

    /**
     * الحصول على البريد الإلكتروني مخفي للعرض
     */
    public function getMaskedEmailAttribute()
    {
        return EncryptionService::maskEmail($this->email);
    }

    /**
     * التقييمات التي كتبها المستخدم
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * التقييمات المكتوبة للمستخدم (polymorphic)
     */
    public function receivedReviews()
    {
        return $this->morphMany(Review::class, 'reviewable');
    }

    /**
     * التقييمات المعتمدة المكتوبة للمستخدم
     */
    public function approvedReceivedReviews()
    {
        return $this->receivedReviews()->approved();
    }

    /**
     * الإشعارات
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * الحصول على متوسط تقييم المستخدم
     */
    public function getAverageRatingAttribute(): float
    {
        return Review::getAverageRating($this);
    }

    /**
     * الحصول على عدد التقييمات للمستخدم
     */
    public function getReceivedReviewsCountAttribute(): int
    {
        return Review::getReviewsCount($this);
    }

    /**
     * التحقق من انتهاء صلاحية كلمة المرور
     */
    public function isPasswordExpired(): bool
    {
        if (!$this->password_changed_at) {
            return true; // إذا لم يتم تسجيل تاريخ تغيير كلمة المرور
        }

        $maxAge = $this->is_admin ? 90 : 180; // 90 يوم للمديرين، 180 للمستخدمين العاديين

        /** @var \Carbon\Carbon $passwordChangedAt */
        $passwordChangedAt = $this->password_changed_at;
        return $passwordChangedAt?->addDays($maxAge)->isPast() ?? false;
    }

    /**
     * التحقق من قفل الحساب
     */
    public function isLocked(): bool
    {
        if (!$this->locked_until) {
            return false;
        }

        /** @var \Carbon\Carbon $lockedUntil */
        $lockedUntil = $this->locked_until;
        return $lockedUntil->isFuture();
    }

    /**
     * قفل الحساب لفترة محددة
     */
    public function lockAccount(int $minutes = 15): void
    {
        $this->update([
            'locked_until' => now()->addMinutes($minutes),
            'failed_login_attempts' => $this->failed_login_attempts + 1,
        ]);
    }

    /**
     * إلغاء قفل الحساب
     */
    public function unlockAccount(): void
    {
        $this->update([
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);
    }

    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    public function recordFailedLogin(): void
    {
        $attempts = $this->failed_login_attempts + 1;

        $this->update([
            'failed_login_attempts' => $attempts,
        ]);

        // قفل الحساب بعد 5 محاولات فاشلة
        if ($attempts >= 5) {
            $this->lockAccount(15); // قفل لمدة 15 دقيقة
        }
    }

    /**
     * تسجيل تسجيل دخول ناجح
     */
    public function recordSuccessfulLogin(string $ip): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip,
            'failed_login_attempts' => 0,
            'locked_until' => null,
        ]);
    }

    /**
     * إجبار تغيير كلمة المرور
     */
    public function forcePasswordChange(): void
    {
        $this->update([
            'force_password_change' => true,
        ]);
    }

    /**
     * التحقق من ضرورة تغيير كلمة المرور
     */
    public function mustChangePassword(): bool
    {
        return $this->force_password_change || $this->isPasswordExpired();
    }

    /**
     * تحديث كلمة المرور مع حفظ التاريخ
     */
    public function updatePassword(string $newPassword): void
    {
        // حفظ كلمة المرور القديمة في التاريخ (اختياري)
        $history = $this->password_history ?? [];
        if (count($history) >= 5) {
            array_shift($history); // إزالة أقدم كلمة مرور
        }
        $history[] = [
            'hash' => $this->password,
            'changed_at' => now()->toISOString(),
        ];

        $this->update([
            'password' => $newPassword,
            'password_changed_at' => now(),
            'force_password_change' => false,
            'password_history' => $history,
        ]);
    }

    /**
     * التحقق من إعادة استخدام كلمة المرور
     */
    public function isPasswordReused(string $newPassword): bool
    {
        $history = $this->password_history ?? [];

        foreach ($history as $entry) {
            if (password_verify($newPassword, $entry['hash'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * التحقق من دور المستخدم
     *
     * @param string $role
     * @return bool
     */
    public function hasRole(string $role): bool
    {
        // في هذا النظام، لدينا فقط دور admin
        if ($role === 'admin') {
            return $this->is_admin;
        }

        // يمكن إضافة أدوار أخرى في المستقبل
        return false;
    }

    /**
     * التحقق من كون المستخدم مدير
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->is_admin;
    }
}
