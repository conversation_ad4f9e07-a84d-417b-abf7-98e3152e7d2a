<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use App\Services\EncryptionService;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * الحقول القابلة للتعبئة الجماعية
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',                    // اسم المستخدم
        'email',                   // البريد الإلكتروني
        'password',                // كلمة المرور
        'card_number',             // رقم البطاقة
        'card_type',               // نوع البطاقة (كاك بنك، الكريمي، إلخ)
        'currency',                // نوع العملة (ريال يمني، دولار، ريال سعودي)
        'is_admin',                // هل المستخدم مدير
        'password_changed_at',     // تاريخ آخر تغيير لكلمة المرور
        'last_login_at',           // تاريخ آخر تسجيل دخول
        'last_login_ip',           // آخر IP لتسجيل الدخول
        'registration_ip',         // IP التسجيل
        'failed_login_attempts',   // عدد محاولات تسجيل الدخول الفاشلة
        'locked_until',            // مؤقت حتى
        'force_password_change',   // إجبار تغيير كلمة المرور
        'password_history',        // تاريخ كلمات المرور
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'password_history',
        'last_login_ip',
        'registration_ip',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_admin' => 'boolean',
            'password_changed_at' => 'datetime',
            'last_login_at' => 'datetime',
            'locked_until' => 'datetime',
            'force_password_change' => 'boolean',
            'password_history' => 'array',
        ];
    }

    /**
     * علاقة واحد إلى متعدد مع الإعلانات
     * مستخدم واحد يمكن أن يملك عدة إعلانات
     */
    public function ads(): HasMany
    {
        return $this->hasMany(Ad::class);
    }

    /**
     * الحصول على الإعلانات النشطة للمستخدم
     */
    public function activeAds(): HasMany
    {
        return $this->hasMany(Ad::class)->where('status', 'active')->notExpired();
    }

    /**
     * الحصول على الإعلانات المعلقة للمستخدم
     */
    public function pendingAds(): HasMany
    {
        return $this->hasMany(Ad::class)->where('status', 'pending');
    }

    /**
     * تشفير رقم البطاقة عند الحفظ
     */
    public function setCardNumberAttribute($value)
    {
        $this->attributes['card_number'] = EncryptionService::encryptText($value);
    }

    /**
     * فك تشفير رقم البطاقة عند الاستدعاء
     */
    public function getCardNumberAttribute($value)
    {
        return EncryptionService::decryptText($value);
    }

    /**
     * الحصول على رقم البطاقة مخفي للعرض
     */
    public function getMaskedCardNumberAttribute()
    {
        $cardNumber = $this->card_number;
        if (empty($cardNumber)) {
            return null;
        }

        if (strlen($cardNumber) <= 4) {
            return str_repeat('*', strlen($cardNumber));
        }

        return str_repeat('*', strlen($cardNumber) - 4) . substr($cardNumber, -4);
    }

    /**
     * الحصول على البريد الإلكتروني مخفي للعرض
     */
    public function getMaskedEmailAttribute()
    {
        return EncryptionService::maskEmail($this->email);
    }

    /**
     * التقييمات التي كتبها المستخدم
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * التقييمات المكتوبة للمستخدم (polymorphic)
     */
    public function receivedReviews()
    {
        return $this->morphMany(Review::class, 'reviewable');
    }

    /**
     * التقييمات المعتمدة المكتوبة للمستخدم
     */
    public function approvedReceivedReviews()
    {
        return $this->receivedReviews()->approved();
    }

    /**
     * الإشعارات
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * الحصول على متوسط تقييم المستخدم
     */
    public function getAverageRatingAttribute(): float
    {
        return Review::getAverageRating($this);
    }

    /**
     * الحصول على عدد التقييمات للمستخدم
     */
    public function getReceivedReviewsCountAttribute(): int
    {
        return Review::getReviewsCount($this);
    }

    /**
     * التحقق من انتهاء صلاحية كلمة المرور
     */
    public function isPasswordExpired(): bool
    {
        if (!$this->password_changed_at) {
            return true; // إذا لم يتم تسجيل تاريخ تغيير كلمة المرور
        }

        $maxAge = $this->is_admin ? 90 : 180; // 90 يوم للمديرين، 180 للمستخدمين العاديين

        return $this->password_changed_at->addDays($maxAge)->isPast();
    }

    /**
     * التحقق من قفل الحساب
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * قفل الحساب لفترة محددة
     */
    public function lockAccount(int $minutes = 15): void
    {
        $this->update([
            'locked_until' => now()->addMinutes($minutes),
            'failed_login_attempts' => $this->failed_login_attempts + 1,
        ]);
    }

    /**
     * إلغاء قفل الحساب
     */
    public function unlockAccount(): void
    {
        $this->update([
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);
    }

    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    public function recordFailedLogin(): void
    {
        $attempts = $this->failed_login_attempts + 1;

        $this->update([
            'failed_login_attempts' => $attempts,
        ]);

        // قفل الحساب بعد 5 محاولات فاشلة
        if ($attempts >= 5) {
            $this->lockAccount(15); // قفل لمدة 15 دقيقة
        }
    }

    /**
     * تسجيل تسجيل دخول ناجح
     */
    public function recordSuccessfulLogin(string $ip): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip,
            'failed_login_attempts' => 0,
            'locked_until' => null,
        ]);
    }

    /**
     * إجبار تغيير كلمة المرور
     */
    public function forcePasswordChange(): void
    {
        $this->update([
            'force_password_change' => true,
        ]);
    }

    /**
     * التحقق من ضرورة تغيير كلمة المرور
     */
    public function mustChangePassword(): bool
    {
        return $this->force_password_change || $this->isPasswordExpired();
    }

    /**
     * تحديث كلمة المرور مع حفظ التاريخ
     */
    public function updatePassword(string $newPassword): void
    {
        // حفظ كلمة المرور القديمة في التاريخ (اختياري)
        $history = $this->password_history ?? [];
        if (count($history) >= 5) {
            array_shift($history); // إزالة أقدم كلمة مرور
        }
        $history[] = [
            'hash' => $this->password,
            'changed_at' => now()->toISOString(),
        ];

        $this->update([
            'password' => $newPassword,
            'password_changed_at' => now(),
            'force_password_change' => false,
            'password_history' => $history,
        ]);
    }

    /**
     * التحقق من إعادة استخدام كلمة المرور
     */
    public function isPasswordReused(string $newPassword): bool
    {
        $history = $this->password_history ?? [];

        foreach ($history as $entry) {
            if (password_verify($newPassword, $entry['hash'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * التحقق من دور المستخدم
     *
     * @param string $role
     * @return bool
     */
    public function hasRole(string $role): bool
    {
        // في هذا النظام، لدينا فقط دور admin
        if ($role === 'admin') {
            return $this->is_admin;
        }

        // يمكن إضافة أدوار أخرى في المستقبل
        return false;
    }

    /**
     * التحقق من كون المستخدم مدير
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->is_admin;
    }

    /**
     * علاقة واحد إلى متعدد مع المفضلة
     * مستخدم واحد يمكن أن يملك عدة إعلانات مفضلة
     */
    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    /**
     * علاقة متعدد إلى متعدد مع الإعلانات المفضلة
     * الحصول على الإعلانات المفضلة للمستخدم مباشرة
     */
    public function favoriteAds()
    {
        return $this->belongsToMany(Ad::class, 'favorites', 'user_id', 'ad_id')
                    ->withTimestamps()
                    ->orderBy('favorites.created_at', 'desc');
    }

    /**
     * الحصول على الإعلانات المفضلة النشطة فقط
     */
    public function activeFavoriteAds()
    {
        return $this->favoriteAds()
                    ->where('ads.status', 'active')
                    ->where(function ($query) {
                        $query->whereNull('ads.expires_at')
                              ->orWhere('ads.expires_at', '>', now());
                    });
    }

    /**
     * التحقق من وجود إعلان في مفضلة المستخدم
     */
    public function hasFavorited(int $adId): bool
    {
        return $this->favorites()->where('ad_id', $adId)->exists();
    }

    /**
     * إضافة إعلان إلى المفضلة
     */
    public function addToFavorites(int $adId): bool
    {
        return Favorite::addToFavorites($this->id, $adId);
    }

    /**
     * إزالة إعلان من المفضلة
     */
    public function removeFromFavorites(int $adId): bool
    {
        return Favorite::removeFromFavorites($this->id, $adId);
    }

    /**
     * تبديل حالة الإعلان في المفضلة
     */
    public function toggleFavorite(int $adId): array
    {
        return Favorite::toggleFavorite($this->id, $adId);
    }

    /**
     * الحصول على عدد الإعلانات المفضلة
     */
    public function getFavoritesCountAttribute(): int
    {
        return Favorite::getUserFavoritesCount($this->id);
    }

    /**
     * تسجيل عرض معلومات المستخدم المتقدمة
     */
    public function logAdvancedProfileView(): void
    {
        Log::info('عرض معلومات المستخدم المتقدمة', [
            'user_id' => $this->id,
            'user_name' => $this->name,
            'active_ads_count' => $this->activeAds()->count(),
            'average_rating' => $this->average_rating,
            'reviews_count' => $this->received_reviews_count,
            'favorites_count' => $this->favorites_count,
            'is_verified' => $this->is_verified,
            'viewer_id' => auth()->id(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);
    }

    /**
     * الحصول على رابط الصورة الشخصية
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        // استخدام Gravatar كبديل
        $hash = md5(strtolower(trim($this->email)));
        return "https://www.gravatar.com/avatar/{$hash}?d=mp&s=200";
    }

    /**
     * الحصول على إحصائيات شاملة للمستخدم
     */
    public function getComprehensiveStats(): array
    {
        return [
            'total_ads' => $this->ads()->count(),
            'active_ads' => $this->activeAds()->count(),
            'pending_ads' => $this->pendingAds()->count(),
            'average_rating' => $this->average_rating,
            'reviews_count' => $this->received_reviews_count,
            'favorites_count' => $this->favorites_count,
            'join_year' => $this->created_at->format('Y'),
            'last_activity' => $this->last_seen_at ? $this->last_seen_at->diffForHumans() : null,
            'trust_indicators' => [
                'is_verified' => $this->is_verified,
                'phone_verified' => !is_null($this->phone_verified_at),
                'email_verified' => !is_null($this->email_verified_at),
                'is_admin' => $this->is_admin,
                'is_premium' => $this->is_premium ?? false,
            ]
        ];
    }

    /**
     * العلاقة مع معلومات التواصل
     */
    public function contacts(): HasMany
    {
        return $this->hasMany(UserContact::class);
    }

    /**
     * الحصول على معلومات التواصل العامة مرتبة
     */
    public function publicContacts(): HasMany
    {
        return $this->hasMany(UserContact::class)
                    ->public()
                    ->ordered();
    }

    /**
     * الحصول على معلومات التواصل للمستخدمين المسجلين
     */
    public function contactsForRegisteredUsers(): HasMany
    {
        return $this->hasMany(UserContact::class)
                    ->forRegisteredUsers()
                    ->ordered();
    }

    /**
     * الحصول على معلومات التواصل الأساسية
     */
    public function primaryContacts(): HasMany
    {
        return $this->hasMany(UserContact::class)
                    ->where('is_primary', true)
                    ->ordered();
    }

    /**
     * الحصول على رقم الهاتف الأساسي من معلومات التواصل
     */
    public function getPrimaryPhoneAttribute(): ?string
    {
        $phoneContact = $this->contacts()
                           ->where('contact_type', 'phone')
                           ->where('is_primary', true)
                           ->first();

        return $phoneContact ? $phoneContact->contact_value : null;
    }

    /**
     * الحصول على الإيميل الأساسي من معلومات التواصل
     */
    public function getPrimaryEmailContactAttribute(): ?string
    {
        $emailContact = $this->contacts()
                           ->where('contact_type', 'email')
                           ->where('is_primary', true)
                           ->first();

        return $emailContact ? $emailContact->contact_value : null;
    }

    /**
     * الحصول على رقم الواتساب الأساسي
     */
    public function getPrimaryWhatsappAttribute(): ?string
    {
        $whatsappContact = $this->contacts()
                              ->where('contact_type', 'whatsapp')
                              ->where('is_primary', true)
                              ->first();

        return $whatsappContact ? $whatsappContact->contact_value : null;
    }

    /**
     * التحقق من وجود معلومات تواصل عامة
     */
    public function hasPublicContacts(): bool
    {
        return $this->contacts()->public()->exists();
    }

    /**
     * الحصول على عدد معلومات التواصل المتحقق منها
     */
    public function getVerifiedContactsCountAttribute(): int
    {
        return $this->contacts()->verified()->count();
    }

    /**
     * العلاقة مع إعدادات الخصوصية
     */
    public function privacySettings(): HasOne
    {
        return $this->hasOne(UserPrivacySetting::class);
    }

    /**
     * الحصول على إعدادات الخصوصية أو إنشاؤها إذا لم تكن موجودة
     */
    public function getPrivacySettingsAttribute(): ?UserPrivacySetting
    {
        // استخدام العلاقة المباشرة بدلاً من الـ attribute لتجنب infinite loop
        if (!$this->relationLoaded('privacySettings')) {
            $this->load('privacySettings');
        }

        $settings = $this->getRelationValue('privacySettings');

        if (!$settings && $this->id) {
            // إنشاء إعدادات افتراضية إذا لم تكن موجودة
            try {
                $settings = UserPrivacySetting::createDefaultForUser($this->id);
                $this->setRelation('privacySettings', $settings);
            } catch (\Exception $e) {
                \Log::warning('Failed to create default privacy settings', [
                    'user_id' => $this->id,
                    'error' => $e->getMessage()
                ]);
                return null;
            }
        }

        return $settings;
    }

    /**
     * التحقق من إمكانية عرض معلومات التواصل للمستخدم المحدد
     */
    public function canViewContactsBy(?User $viewer = null): bool
    {
        return $this->privacy_settings->canViewContacts($viewer);
    }

    /**
     * الحصول على معلومات التواصل المرئية للمستخدم المحدد
     */
    public function getVisibleContactsFor(?User $viewer = null): HasMany
    {
        $query = $this->contacts()->ordered();

        // إذا كان المشاهد هو صاحب المعلومات أو مدير، عرض كل شيء
        if ($viewer && ($viewer->id === $this->id || $viewer->is_admin)) {
            return $query;
        }

        // تطبيق إعدادات الخصوصية
        $privacySettings = $this->privacy_settings;

        if (!$privacySettings->canViewContacts($viewer)) {
            return $this->contacts()->where('id', 0); // إرجاع استعلام فارغ
        }

        // إخفاء المعلومات غير المتحقق منها إذا كان مفعلاً
        if ($privacySettings->auto_hide_unverified_contacts) {
            $query->where('is_verified', true);
        }

        // تطبيق مستويات الخصوصية
        if (!$viewer) {
            // زائر غير مسجل
            $query->where('privacy_level', 'public');
        } else {
            // مستخدم مسجل
            $query->whereIn('privacy_level', ['public', 'registered_users']);
        }

        return $query;
    }
}
