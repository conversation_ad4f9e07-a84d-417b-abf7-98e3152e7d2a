<?php $__env->startSection('title', __('Categories') . ' - ' . __('site_name')); ?>
<?php $__env->startSection('description', __('choose_category')); ?>

<?php $__env->startSection('content'); ?>
<!-- القسم الرئيسي للتصنيفات -->
<section class="categories-section py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 40vh;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white">
                <h1 class="display-4 fw-bold arabic-text mb-3">
                    <?php echo e(__('main_categories')); ?>

                </h1>
                <p class="lead arabic-text mb-4">
                    <?php echo e(__('choose_category')); ?>

                </p>
                
                <!-- أزرار التنقل -->
                <div class="navigation-buttons mb-4">
                    <a href="<?php echo e(route('home')); ?>" class="btn btn-outline-light btn-sm arabic-text me-2">
                        <i class="fas fa-home me-2"></i>
                        <?php echo e(__('Home')); ?>

                    </a>
                    <a href="<?php echo e(route('action.choice')); ?>" class="btn btn-outline-light btn-sm arabic-text">
                        <i class="fas fa-arrow-right me-2"></i>
                        <?php echo e(__('back_to_home')); ?>

                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم بطاقات التصنيفات -->
<section class="categories-grid py-5 bg-light">
    <div class="container">
        <?php if($categories->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="category-card h-100">
                            <div class="card-body text-center p-4">
                                <!-- الأيقونة -->
                                <div class="category-icon mb-4">
                                    <i class="<?php echo e($category->icon); ?> fa-4x category-icon-color"></i>
                                </div>
                                
                                <!-- اسم التصنيف -->
                                <h3 class="arabic-text fw-bold text-dark mb-3">
                                    <?php echo e($category->name); ?>

                                </h3>
                                
                                <!-- وصف التصنيف -->
                                <p class="text-muted arabic-text mb-4">
                                    <?php echo e($category->description); ?>

                                </p>
                                
                                <!-- إحصائيات التصنيف -->
                                <div class="category-stats mb-4">
                                    <div class="stat-badge">
                                        <i class="fas fa-bullhorn me-2"></i>
                                        <span class="fw-bold"><?php echo e($category->ads_count); ?></span>
                                        <small class="arabic-text"><?php echo e(__('ads')); ?></small>
                                    </div>
                                </div>
                                
                                <!-- زر عرض الإعلانات -->
                                <a href="<?php echo e(route('ads.category', $category->slug)); ?>" class="btn btn-primary btn-lg px-4 py-3 arabic-text fw-bold category-btn">
                                    <i class="fas fa-eye me-2"></i>
                                    <?php echo e(__('browse_ads')); ?>

                                </a>
                                
                                <!-- رابط سريع للإحصائيات -->
                                <div class="mt-3">
                                    <a href="<?php echo e(route('categories.stats', $category->slug)); ?>" class="text-muted small arabic-text text-decoration-none">
                                        <i class="fas fa-chart-bar me-1"></i>
                                        <?php echo e(__('View Statistics')); ?>

                                    </a>
                                </div>
                            </div>
                            
                            <!-- شريط ملون في الأسفل -->
                            <div class="category-footer"></div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <!-- رسالة عدم وجود تصنيفات -->
            <div class="row justify-content-center">
                <div class="col-lg-6 text-center">
                    <div class="empty-state py-5">
                        <i class="fas fa-folder-open fa-5x text-muted mb-4"></i>
                        <h3 class="arabic-text text-muted mb-3"><?php echo e(__('No categories found')); ?></h3>
                        <p class="text-muted arabic-text mb-4"><?php echo e(__('Categories will appear here once added')); ?></p>
                        <a href="<?php echo e(route('home')); ?>" class="btn btn-primary arabic-text">
                            <i class="fas fa-home me-2"></i>
                            <?php echo e(__('Back to Home')); ?>

                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- قسم البحث السريع -->
<section class="search-section py-5 bg-white">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="search-card text-center p-5">
                    <h4 class="arabic-text fw-bold text-primary mb-3">
                        <i class="fas fa-search me-2"></i>
                        <?php echo e(__('Search in categories')); ?>

                    </h4>
                    <p class="text-muted arabic-text mb-4">
                        <?php echo e(__('Find the category you are looking for')); ?>

                    </p>
                    
                    <!-- نموذج البحث -->
                    <form action="<?php echo e(route('categories.search')); ?>" method="GET" class="search-form">
                        <div class="input-group input-group-lg">
                            <input type="text" 
                                   name="q" 
                                   class="form-control arabic-text" 
                                   placeholder="<?php echo e(__('Search in categories...')); ?>"
                                   value="<?php echo e(request('q')); ?>">
                            <button class="btn btn-primary px-4" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم الإحصائيات العامة -->
<section class="stats-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row text-center">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <i class="fas fa-th-large fa-3x mb-3"></i>
                    <h3 class="fw-bold"><?php echo e($categories->count()); ?></h3>
                    <p class="arabic-text"><?php echo e(__('categories')); ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <i class="fas fa-bullhorn fa-3x mb-3"></i>
                    <h3 class="fw-bold"><?php echo e($categories->sum('ads_count')); ?>+</h3>
                    <p class="arabic-text"><?php echo e(__('ads')); ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <h3 class="fw-bold">1000+</h3>
                    <p class="arabic-text"><?php echo e(__('Users')); ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <i class="fas fa-star fa-3x mb-3"></i>
                    <h3 class="fw-bold">4.8</h3>
                    <p class="arabic-text"><?php echo e(__('rating')); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* تنسيق القسم الرئيسي */
.categories-section {
    position: relative;
    overflow: hidden;
}

.categories-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* بطاقات التصنيفات */
.category-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.category-card:hover::before {
    left: 100%;
}

/* أيقونات التصنيفات */
.category-icon {
    position: relative;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

/* ألوان الأيقونات */
.category-card:nth-child(1) .category-icon-color { color: #17a2b8; } /* مدارس - أزرق فاتح */
.category-card:nth-child(2) .category-icon-color { color: #28a745; } /* جامعات - أخضر */
.category-card:nth-child(3) .category-icon-color { color: #ffc107; } /* معاهد - أصفر */
.category-card:nth-child(4) .category-icon-color { color: #dc3545; } /* مستشفيات - أحمر */
.category-card:nth-child(5) .category-icon-color { color: #8b5cf6; } /* محلات - بنفسجي */
.category-card:nth-child(6) .category-icon-color { color: #007bff; } /* شركات - أزرق */

/* شريط التصنيف */
.category-footer {
    height: 5px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.category-card:hover .category-footer {
    transform: scaleX(1);
}

/* إحصائيات التصنيف */
.stat-badge {
    display: inline-block;
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
}

/* أزرار التصنيفات */
.category-btn {
    border-radius: 50px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* بطاقة البحث */
.search-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.search-form .form-control {
    border-radius: 50px 0 0 50px;
    border: 2px solid #e9ecef;
    padding: 1rem 1.5rem;
}

.search-form .btn {
    border-radius: 0 50px 50px 0;
    border: 2px solid #007bff;
}

/* قسم الإحصائيات */
.stat-item {
    padding: 2rem 1rem;
}

.stat-item i {
    opacity: 0.8;
}

/* حالة فارغة */
.empty-state {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .categories-section {
        min-height: 30vh;
        padding: 3rem 0;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .category-card .card-body {
        padding: 2rem 1.5rem;
    }
    
    .category-icon i {
        font-size: 3rem;
    }
    
    .category-btn {
        width: 100%;
    }
    
    .navigation-buttons .btn {
        display: block;
        margin: 0.25rem 0;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .category-card .card-body {
        padding: 1.5rem 1rem;
    }
    
    .search-card {
        padding: 2rem 1.5rem;
    }
    
    .stat-item {
        padding: 1rem;
    }
    
    .stat-item h3 {
        font-size: 1.5rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\flutterproject2\MyAdsSite\resources\views/categories/index.blade.php ENDPATH**/ ?>