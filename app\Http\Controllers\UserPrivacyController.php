<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\UserPrivacySetting;
use App\Services\ContactSecurityService;
use Exception;

/**
 * Controller لإدارة إعدادات الخصوصية والأمان للمستخدمين
 */
class UserPrivacyController extends Controller
{
    protected ContactSecurityService $securityService;

    public function __construct(ContactSecurityService $securityService)
    {
        $this->securityService = $securityService;
    }

    /**
     * عرض إعدادات الخصوصية للمستخدم الحالي
     */
    public function index(): JsonResponse
    {
        try {
            $user = Auth::user();

            Log::info('User privacy settings request', [
                'user_id' => $user->id,
                'ip' => request()->ip()
            ]);

            $settings = $user->privacy_settings;

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'تم جلب إعدادات الخصوصية بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to retrieve privacy settings', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب إعدادات الخصوصية'
            ], 500);
        }
    }

    /**
     * تحديث إعدادات الخصوصية
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            Log::info('Updating user privacy settings', [
                'user_id' => $user->id,
                'request_data' => $request->except(['_token']),
                'ip' => request()->ip()
            ]);

            $validator = Validator::make($request->all(), [
                'show_contacts_to_guests' => 'boolean',
                'show_contacts_to_registered' => 'boolean',
                'require_verification_to_view' => 'boolean',
                'enable_contact_rate_limiting' => 'boolean',
                'max_contact_views_per_hour' => 'integer|min:1|max:1000',
                'max_contact_views_per_day' => 'integer|min:1|max:10000',
                'require_phone_verification' => 'boolean',
                'require_email_verification' => 'boolean',
                'auto_hide_unverified_contacts' => 'boolean',
                'notify_on_contact_view' => 'boolean',
                'notify_on_contact_copy' => 'boolean',
                'notify_on_suspicious_activity' => 'boolean',
                'enable_contact_encryption' => 'boolean',
                'enable_watermark' => 'boolean',
                'disable_right_click' => 'boolean',
                'disable_text_selection' => 'boolean',
                'allow_contact_sharing' => 'boolean',
                'allow_contact_export' => 'boolean',
                'show_contact_qr_code' => 'boolean',
                'track_contact_interactions' => 'boolean',
                'log_contact_access' => 'boolean',
                'enable_analytics' => 'boolean',
            ]);

            if ($validator->fails()) {
                Log::warning('Privacy settings validation failed', [
                    'user_id' => $user->id,
                    'errors' => $validator->errors()->toArray()
                ]);

                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => 'بيانات غير صحيحة'
                ], 422);
            }

            $settings = $user->privacy_settings;
            $oldSettings = $settings->toArray();

            $settings->update($request->only([
                'show_contacts_to_guests',
                'show_contacts_to_registered',
                'require_verification_to_view',
                'enable_contact_rate_limiting',
                'max_contact_views_per_hour',
                'max_contact_views_per_day',
                'require_phone_verification',
                'require_email_verification',
                'auto_hide_unverified_contacts',
                'notify_on_contact_view',
                'notify_on_contact_copy',
                'notify_on_suspicious_activity',
                'enable_contact_encryption',
                'enable_watermark',
                'disable_right_click',
                'disable_text_selection',
                'allow_contact_sharing',
                'allow_contact_export',
                'show_contact_qr_code',
                'track_contact_interactions',
                'log_contact_access',
                'enable_analytics',
            ]));

            Log::info('Privacy settings updated successfully', [
                'user_id' => $user->id,
                'old_settings' => $oldSettings,
                'new_settings' => $settings->fresh()->toArray()
            ]);

            return response()->json([
                'success' => true,
                'data' => $settings->fresh(),
                'message' => 'تم تحديث إعدادات الخصوصية بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to update privacy settings', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->except(['_token'])
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تحديث إعدادات الخصوصية'
            ], 500);
        }
    }

    /**
     * إعادة تعيين إعدادات الخصوصية للافتراضية
     */
    public function reset(): JsonResponse
    {
        try {
            $user = Auth::user();

            Log::info('Resetting user privacy settings to default', [
                'user_id' => $user->id,
                'ip' => request()->ip()
            ]);

            $settings = $user->privacy_settings;
            $oldSettings = $settings->toArray();

            $settings->update(UserPrivacySetting::getDefaultSettings());

            Log::info('Privacy settings reset to default successfully', [
                'user_id' => $user->id,
                'old_settings' => $oldSettings,
                'new_settings' => $settings->fresh()->toArray()
            ]);

            return response()->json([
                'success' => true,
                'data' => $settings->fresh(),
                'message' => 'تم إعادة تعيين إعدادات الخصوصية للافتراضية بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to reset privacy settings', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إعادة تعيين إعدادات الخصوصية'
            ], 500);
        }
    }

    /**
     * الحصول على إحصائيات الأمان للمستخدم
     */
    public function getSecurityStats(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $days = $request->get('days', 30);

            Log::info('User security stats request', [
                'user_id' => $user->id,
                'days' => $days,
                'ip' => request()->ip()
            ]);

            $stats = $this->securityService->getSecurityStats($user->id, $days);

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'تم جلب إحصائيات الأمان بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to retrieve security stats', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب إحصائيات الأمان'
            ], 500);
        }
    }

    /**
     * الحصول على النشاطات المشبوهة الأخيرة
     */
    public function getSuspiciousActivities(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $limit = $request->get('limit', 10);

            Log::info('User suspicious activities request', [
                'user_id' => $user->id,
                'limit' => $limit,
                'ip' => request()->ip()
            ]);

            $activities = $this->securityService->getSuspiciousActivities($user->id, $limit);

            return response()->json([
                'success' => true,
                'data' => $activities,
                'message' => 'تم جلب النشاطات المشبوهة بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to retrieve suspicious activities', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب النشاطات المشبوهة'
            ], 500);
        }
    }

    /**
     * حظر IP address مؤقتاً
     */
    public function blockIp(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            // التحقق من صلاحيات المدير
            if (!$user->is_admin) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بهذا الإجراء'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'ip_address' => 'required|ip',
                'minutes' => 'integer|min:1|max:1440', // حد أقصى يوم واحد
                'reason' => 'string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => 'بيانات غير صحيحة'
                ], 422);
            }

            $this->securityService->blockIpTemporarily(
                $request->ip_address,
                $request->get('minutes', 60),
                $request->get('reason', 'حظر يدوي من المدير')
            );

            Log::info('IP address blocked by admin', [
                'admin_user_id' => $user->id,
                'blocked_ip' => $request->ip_address,
                'minutes' => $request->get('minutes', 60),
                'reason' => $request->get('reason', 'حظر يدوي من المدير')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم حظر عنوان IP بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to block IP address', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->except(['_token'])
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حظر عنوان IP'
            ], 500);
        }
    }

    /**
     * إلغاء حظر IP address
     */
    public function unblockIp(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            // التحقق من صلاحيات المدير
            if (!$user->is_admin) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بهذا الإجراء'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'ip_address' => 'required|ip'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => 'بيانات غير صحيحة'
                ], 422);
            }

            $this->securityService->unblockIp($request->ip_address);

            Log::info('IP address unblocked by admin', [
                'admin_user_id' => $user->id,
                'unblocked_ip' => $request->ip_address
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء حظر عنوان IP بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to unblock IP address', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->except(['_token'])
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إلغاء حظر عنوان IP'
            ], 500);
        }
    }
}
