@extends('layouts.app')

@section('title', __('Reviews') . ' ' . $ad->title . ' - ' . __('site_name'))

@section('content')
<div class="container py-4">
    <!-- معلومات الإعلان -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" data-animation="fadeInDown">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            @if($ad->image)
                                <img src="{{ asset('storage/' . $ad->image) }}" 
                                     alt="{{ $ad->title }}" 
                                     class="img-fluid rounded">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="height: 100px;">
                                    <i class="fas fa-image fa-2x text-muted"></i>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-8">
                            <h4 class="mb-2 arabic-text">{{ $ad->title }}</h4>
                            <p class="text-muted mb-2 arabic-text">{{ $ad->category->name ?? __('Not specified') }}</p>
                            <div class="d-flex align-items-center gap-3">
                                <div class="d-flex align-items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $stats['average_rating'] ? 'text-warning' : 'text-muted' }}"></i>
                                    @endfor
                                    <span class="ms-2 fw-bold">{{ number_format($stats['average_rating'], 1) }}</span>
                                </div>
                                <span class="text-muted arabic-text">({{ number_format($stats['total_reviews']) }} {{ __('reviews') }})</span>
                            </div>
                        </div>
                        <div class="col-md-2 text-end">
                            <a href="{{ route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]) }}"
                               class="btn btn-outline-primary arabic-text">
                                <i class="fas fa-arrow-{{ app()->getLocale() === 'ar' ? 'left' : 'right' }} me-2"></i>
                                {{ __('View Ad') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- إحصائيات التقييمات -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm" data-animation="fadeInLeft">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0 arabic-text">
                        <i class="fas fa-chart-bar me-2"></i>
                        {{ __('Rating Statistics') }}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- متوسط التقييم -->
                    <div class="text-center mb-4">
                        <div class="display-4 fw-bold text-primary mb-2">
                            {{ number_format($stats['average_rating'], 1) }}
                        </div>
                        <div class="mb-2">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star fa-lg {{ $i <= $stats['average_rating'] ? 'text-warning' : 'text-muted' }}"></i>
                            @endfor
                        </div>
                        <p class="text-muted arabic-text">{{ __('From :count reviews', ['count' => number_format($stats['total_reviews'])]) }}</p>
                    </div>

                    <!-- توزيع التقييمات -->
                    @if($stats['total_reviews'] > 0)
                        @for($i = 5; $i >= 1; $i--)
                            @php
                                $count = $stats['rating_distribution'][$i] ?? 0;
                                $percentage = $stats['total_reviews'] > 0 ? ($count / $stats['total_reviews']) * 100 : 0;
                            @endphp
                            <div class="d-flex align-items-center mb-2">
                                <span class="me-2">{{ $i }}</span>
                                <i class="fas fa-star text-warning me-2"></i>
                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                    <div class="progress-bar bg-warning" 
                                         style="width: {{ $percentage }}%"></div>
                                </div>
                                <span class="text-muted small">{{ $count }}</span>
                            </div>
                        @endfor
                    @endif

                    <!-- زر كتابة تقييم -->
                    @auth
                        @if(!$userReview && $ad->user_id !== auth()->id())
                            <div class="d-grid mt-4">
                                <button class="btn btn-primary arabic-text" onclick="showReviewModal()">
                                    <i class="fas fa-star me-2"></i>
                                    {{ __('Write your review') }}
                                </button>
                            </div>
                        @elseif($userReview)
                            <div class="alert alert-info mt-4">
                                <i class="fas fa-info-circle me-2"></i>
                                <span class="arabic-text">{{ __('You have already reviewed this ad') }}</span>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-primary arabic-text" onclick="editReview({{ $userReview->id }})">
                                        {{ __('Edit Review') }}
                                    </button>
                                </div>
                            </div>
                        @endif
                    @else
                        <div class="d-grid mt-4">
                            <a href="{{ route('login') }}" class="btn btn-outline-primary arabic-text">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                {{ __('Login to write a review') }}
                            </a>
                        </div>
                    @endauth
                </div>
            </div>
        </div>

        <!-- قائمة التقييمات -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm" data-animation="fadeInRight">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 arabic-text">
                            <i class="fas fa-comments me-2"></i>
                            {{ __('Reviews and Ratings') }}
                        </h5>
                        
                        <!-- فلاتر الترتيب -->
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm arabic-text" id="sort-filter" style="width: auto;">
                                <option value="newest" {{ $sortBy === 'newest' ? 'selected' : '' }}>{{ __('Newest') }}</option>
                                <option value="oldest" {{ $sortBy === 'oldest' ? 'selected' : '' }}>{{ __('Oldest') }}</option>
                                <option value="helpful" {{ $sortBy === 'helpful' ? 'selected' : '' }}>{{ __('Most Helpful') }}</option>
                                <option value="rating_high" {{ $sortBy === 'rating_high' ? 'selected' : '' }}>{{ __('Highest Rating') }}</option>
                                <option value="rating_low" {{ $sortBy === 'rating_low' ? 'selected' : '' }}>{{ __('Lowest Rating') }}</option>
                            </select>

                            <select class="form-select form-select-sm arabic-text" id="rating-filter" style="width: auto;">
                                <option value="all" {{ $filterRating === 'all' ? 'selected' : '' }}>{{ __('All Ratings') }}</option>
                                <option value="5" {{ $filterRating === '5' ? 'selected' : '' }}>{{ __('5 Stars') }}</option>
                                <option value="4" {{ $filterRating === '4' ? 'selected' : '' }}>{{ __('4 Stars') }}</option>
                                <option value="3" {{ $filterRating === '3' ? 'selected' : '' }}>{{ __('3 Stars') }}</option>
                                <option value="2" {{ $filterRating === '2' ? 'selected' : '' }}>{{ __('2 Stars') }}</option>
                                <option value="1" {{ $filterRating === '1' ? 'selected' : '' }}>{{ __('1 Star') }}</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="card-body" id="reviews-container">
                    @forelse($reviews as $review)
                        <div class="review-item border-bottom pb-3 mb-3" data-review-id="{{ $review->id }}">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-user text-primary"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1">{{ $review->user->name }}</h6>
                                            <div class="d-flex align-items-center gap-2">
                                                <div class="d-flex">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star {{ $i <= $review->rating ? 'text-warning' : 'text-muted' }} small"></i>
                                                    @endfor
                                                </div>
                                                <span class="badge bg-{{ $review->rating_color }}">{{ $review->rating_text }}</span>
                                                @if($review->is_verified)
                                                    <span class="badge bg-success arabic-text">{{ __('Verified') }}</span>
                                                @endif
                                            </div>
                                        </div>
                                        <small class="text-muted">{{ $review->created_at->diffForHumans() }}</small>
                                    </div>
                                    
                                    @if($review->title)
                                        <h6 class="fw-bold mb-2">{{ $review->title }}</h6>
                                    @endif
                                    
                                    @if($review->comment)
                                        <p class="mb-2">{{ $review->comment }}</p>
                                    @endif
                                    
                                    <div class="d-flex align-items-center gap-3">
                                        <button class="btn btn-sm btn-outline-success arabic-text" onclick="markAsHelpful({{ $review->id }})">
                                            <i class="fas fa-thumbs-up me-1"></i>
                                            {{ __('Helpful') }} ({{ $review->helpful_count }})
                                        </button>

                                        @auth
                                            @if($review->user_id !== auth()->id())
                                                <button class="btn btn-sm btn-outline-danger arabic-text" onclick="reportReview({{ $review->id }})">
                                                    <i class="fas fa-flag me-1"></i>
                                                    {{ __('Report') }}
                                                </button>
                                            @else
                                                <button class="btn btn-sm btn-outline-primary arabic-text" onclick="editReview({{ $review->id }})">
                                                    <i class="fas fa-edit me-1"></i>
                                                    {{ __('Edit') }}
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger arabic-text" onclick="deleteReview({{ $review->id }})">
                                                    <i class="fas fa-trash me-1"></i>
                                                    {{ __('Delete') }}
                                                </button>
                                            @endif
                                        @endauth
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted arabic-text">{{ __('No reviews yet') }}</h5>
                            <p class="text-muted arabic-text">{{ __('Be the first to review this ad') }}</p>
                        </div>
                    @endforelse
                </div>
                
                @if($reviews->hasPages())
                    <div class="card-footer bg-white border-0">
                        {{ $reviews->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- نافذة كتابة التقييم -->
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title arabic-text">{{ __('Write your review') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="reviewForm">
                <div class="modal-body">
                    <!-- التقييم بالنجوم -->
                    <div class="mb-3">
                        <label class="form-label arabic-text">{{ __('Rating') }} *</label>
                        <div class="star-rating">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star star" data-rating="{{ $i }}"></i>
                            @endfor
                        </div>
                        <input type="hidden" name="rating" id="rating" required>
                    </div>

                    <!-- عنوان التقييم -->
                    <div class="mb-3">
                        <label for="title" class="form-label arabic-text">{{ __('Review Title') }}</label>
                        <input type="text" class="form-control" id="title" name="title" maxlength="255" placeholder="{{ __('Enter review title') }}">
                    </div>

                    <!-- تعليق التقييم -->
                    <div class="mb-3">
                        <label for="comment" class="form-label arabic-text">{{ __('Your Comment') }}</label>
                        <textarea class="form-control" id="comment" name="comment" rows="4" maxlength="1000" placeholder="{{ __('Write your detailed review here') }}"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary arabic-text" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary arabic-text">{{ __('Submit Review') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('styles')
<style>
.star-rating {
    font-size: 2rem;
    color: #ddd;
    cursor: pointer;
}

.star-rating .star {
    transition: color 0.2s;
}

.star-rating .star:hover,
.star-rating .star.active {
    color: #ffc107;
}

.review-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
</style>
@endpush

@push('scripts')
<script>
let selectedRating = 0;

// تفاعل النجوم
document.querySelectorAll('.star').forEach(star => {
    star.addEventListener('click', function() {
        selectedRating = parseInt(this.dataset.rating);
        document.getElementById('rating').value = selectedRating;
        updateStars();
    });
    
    star.addEventListener('mouseenter', function() {
        const rating = parseInt(this.dataset.rating);
        highlightStars(rating);
    });
});

document.querySelector('.star-rating').addEventListener('mouseleave', function() {
    updateStars();
});

function highlightStars(rating) {
    document.querySelectorAll('.star').forEach((star, index) => {
        if (index < rating) {
            star.classList.add('active');
        } else {
            star.classList.remove('active');
        }
    });
}

function updateStars() {
    highlightStars(selectedRating);
}

// عرض نافذة التقييم
function showReviewModal() {
    const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
    modal.show();
}

// إرسال التقييم
document.getElementById('reviewForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (selectedRating === 0) {
        alert('{{ __("Please select a rating") }}');
        return;
    }
    
    const formData = new FormData(this);
    
    fetch('{{ route('reviews.store', $ad) }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '{{ __("An error occurred") }}');
        }
    })
    .catch(() => {
        alert('{{ __("Connection error occurred") }}');
    });
});

// وضع علامة مفيد
function markAsHelpful(reviewId) {
    fetch(`/reviews/${reviewId}/helpful`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '{{ __("An error occurred") }}');
        }
    });
}

// حذف تقييم
function deleteReview(reviewId) {
    if (confirm('{{ __("Are you sure you want to delete this review?") }}')) {
        fetch(`{{ route('reviews.destroy', [$ad, '__REVIEW_ID__']) }}`.replace('__REVIEW_ID__', reviewId), {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{{ __("An error occurred") }}');
            }
        });
    }
}

// تغيير الفلاتر
document.getElementById('sort-filter').addEventListener('change', function() {
    updateFilters();
});

document.getElementById('rating-filter').addEventListener('change', function() {
    updateFilters();
});

function updateFilters() {
    const sort = document.getElementById('sort-filter').value;
    const rating = document.getElementById('rating-filter').value;
    
    const url = new URL(window.location);
    url.searchParams.set('sort', sort);
    url.searchParams.set('rating', rating);
    
    window.location.href = url.toString();
}
</script>
@endpush
@endsection
