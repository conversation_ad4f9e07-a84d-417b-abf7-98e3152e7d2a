@extends('layouts.app')

@section('title', __('Edit Announcement'))

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="arabic-text fw-bold text-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الإعلان
                </h1>
                <a href="{{ route('admin.announcements.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <!-- نموذج التعديل -->
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل بيانات الإعلان
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.announcements.update', $announcement) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- العنوان بالعربية -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label arabic-text">العنوان بالعربية *</label>
                                <input type="text" name="title_ar" class="form-control @error('title_ar') is-invalid @enderror" 
                                       value="{{ old('title_ar', $announcement->title_ar) }}" required>
                                @error('title_ar')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- العنوان بالإنجليزية -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">العنوان بالإنجليزية *</label>
                                <input type="text" name="title_en" class="form-control @error('title_en') is-invalid @enderror" 
                                       value="{{ old('title_en', $announcement->title_en) }}" required>
                                @error('title_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <!-- المحتوى بالعربية -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label arabic-text">المحتوى بالعربية *</label>
                                <textarea name="content_ar" class="form-control @error('content_ar') is-invalid @enderror" 
                                          rows="3" required>{{ old('content_ar', $announcement->content_ar) }}</textarea>
                                @error('content_ar')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- المحتوى بالإنجليزية -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المحتوى بالإنجليزية *</label>
                                <textarea name="content_en" class="form-control @error('content_en') is-invalid @enderror" 
                                          rows="3" required>{{ old('content_en', $announcement->content_en) }}</textarea>
                                @error('content_en')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <!-- الأيقونة -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label arabic-text">الأيقونة *</label>
                                <select name="icon" class="form-select @error('icon') is-invalid @enderror" required>
                                    <option value="">اختر الأيقونة</option>
                                    <option value="fas fa-bullhorn" {{ old('icon', $announcement->icon) === 'fas fa-bullhorn' ? 'selected' : '' }}>📢 مكبر صوت</option>
                                    <option value="fas fa-gift" {{ old('icon', $announcement->icon) === 'fas fa-gift' ? 'selected' : '' }}>🎁 هدية</option>
                                    <option value="fas fa-star" {{ old('icon', $announcement->icon) === 'fas fa-star' ? 'selected' : '' }}>⭐ نجمة</option>
                                    <option value="fas fa-heart" {{ old('icon', $announcement->icon) === 'fas fa-heart' ? 'selected' : '' }}>❤️ قلب</option>
                                    <option value="fas fa-fire" {{ old('icon', $announcement->icon) === 'fas fa-fire' ? 'selected' : '' }}>🔥 نار</option>
                                    <option value="fas fa-rocket" {{ old('icon', $announcement->icon) === 'fas fa-rocket' ? 'selected' : '' }}>🚀 صاروخ</option>
                                    <option value="fas fa-shield-alt" {{ old('icon', $announcement->icon) === 'fas fa-shield-alt' ? 'selected' : '' }}>🛡️ درع</option>
                                    <option value="fas fa-headset" {{ old('icon', $announcement->icon) === 'fas fa-headset' ? 'selected' : '' }}>🎧 سماعة</option>
                                    <option value="fas fa-percentage" {{ old('icon', $announcement->icon) === 'fas fa-percentage' ? 'selected' : '' }}>% نسبة مئوية</option>
                                    <option value="fas fa-thumbs-up" {{ old('icon', $announcement->icon) === 'fas fa-thumbs-up' ? 'selected' : '' }}>👍 إعجاب</option>
                                </select>
                                @error('icon')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- اللون -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label arabic-text">لون الأيقونة *</label>
                                <select name="color" class="form-select @error('color') is-invalid @enderror" required>
                                    <option value="">اختر اللون</option>
                                    <option value="text-warning" {{ old('color', $announcement->color) === 'text-warning' ? 'selected' : '' }}>🟡 أصفر</option>
                                    <option value="text-success" {{ old('color', $announcement->color) === 'text-success' ? 'selected' : '' }}>🟢 أخضر</option>
                                    <option value="text-danger" {{ old('color', $announcement->color) === 'text-danger' ? 'selected' : '' }}>🔴 أحمر</option>
                                    <option value="text-info" {{ old('color', $announcement->color) === 'text-info' ? 'selected' : '' }}>🔵 أزرق فاتح</option>
                                    <option value="text-primary" {{ old('color', $announcement->color) === 'text-primary' ? 'selected' : '' }}>🔵 أزرق</option>
                                    <option value="text-secondary" {{ old('color', $announcement->color) === 'text-secondary' ? 'selected' : '' }}>⚫ رمادي</option>
                                    <option value="text-light" {{ old('color', $announcement->color) === 'text-light' ? 'selected' : '' }}>⚪ أبيض</option>
                                </select>
                                @error('color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- ترتيب الظهور -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label arabic-text">ترتيب الظهور *</label>
                                <input type="number" name="sort_order" class="form-control @error('sort_order') is-invalid @enderror" 
                                       value="{{ old('sort_order', $announcement->sort_order) }}" min="0" required>
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- حالة التفعيل -->
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="is_active" 
                                       id="is_active" value="1" {{ old('is_active', $announcement->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label arabic-text" for="is_active">
                                    تفعيل الإعلان
                                </label>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>
                                تحديث الإعلان
                            </button>
                            <a href="{{ route('admin.announcements.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
