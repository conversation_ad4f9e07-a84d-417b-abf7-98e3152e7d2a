@extends('layouts.app')

@section('title', __('Server Error'))

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h1 class="display-4 text-danger mb-3">500</h1>
                    <h2 class="h4 mb-3">{{ __('Server Error') }}</h2>
                    
                    <p class="text-muted mb-4">
                        {{ __('Something went wrong on our end. Please try again later.') }}
                    </p>
                    
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{{ route('home') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            {{ __('Go to Home') }}
                        </a>
                        
                        <button onclick="location.reload()" class="btn btn-outline-secondary">
                            <i class="fas fa-redo me-2"></i>
                            {{ __('Try Again') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        border: none;
        border-radius: 15px;
    }
    
    .btn {
        border-radius: 25px;
        padding: 10px 25px;
    }
    
    .display-4 {
        font-weight: bold;
    }
</style>
@endpush
