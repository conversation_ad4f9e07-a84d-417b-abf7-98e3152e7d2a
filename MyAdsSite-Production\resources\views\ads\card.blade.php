{{-- بطاقة الإعلان المحسنة --}}
@if(!isset($noWrapper) || !$noWrapper)
<div class="col-lg-4 col-md-6 mb-4">
@endif
<div class="card ad-card h-100 shadow-sm border-0">
        <div class="card-header-custom">
            <!-- صورة الإعلان -->
            <div class="ad-image">
                @if($ad->image)
                    <img src="{{ asset('storage/' . $ad->image) }}" alt="{{ $ad->title }}" class="card-img-top">
                @else
                    <div class="placeholder-image">
                        <i class="{{ $ad->category->icon }} fa-4x text-muted"></i>
                    </div>
                @endif

                <!-- شارة التصنيف -->
                <div class="category-badge">
                    <i class="{{ $ad->category->icon }} me-1"></i>
                    <span class="arabic-text">{{ $ad->category->name }}</span>
                </div>

                <!-- شارة الحالة -->
                @if($ad->expires_at && $ad->expires_at->diffInDays(now()) <= 7)
                    <div class="status-badge urgent">
                        <i class="fas fa-clock me-1"></i>
                        <span class="arabic-text">{{ __('Expires Soon') }}</span>
                    </div>
                @elseif($ad->created_at->diffInDays(now()) <= 3)
                    <div class="status-badge new">
                        <i class="fas fa-star me-1"></i>
                        <span class="arabic-text">{{ __('New') }}</span>
                    </div>
                @endif
            </div>
        </div>

        <div class="card-body p-3">
            <!-- عنوان الإعلان -->
            <h5 class="ad-title arabic-text fw-bold">
                <a href="{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}" class="text-decoration-none text-dark">
                    {{ $ad->title }}
                </a>
            </h5>

            <!-- وصف مختصر -->
            <p class="ad-description text-muted arabic-text">
                {{ Str::limit($ad->description, 100) }}
            </p>

            <!-- معلومات إضافية -->
            <div class="ad-meta mb-2">
                <div class="row g-2">
                    <!-- الموقع -->
                    @if($ad->location)
                        <div class="col-6">
                            <div class="meta-item">
                                <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                <small class="arabic-text">{{ $ad->location }}</small>
                            </div>
                        </div>
                    @endif

                    <!-- عدد المشاهدات -->
                    <div class="col-6">
                        <div class="meta-item">
                            <i class="fas fa-eye text-info me-1"></i>
                            <small class="arabic-text">{{ $ad->views_count }} {{ __('views') }}</small>
                        </div>
                    </div>

                    <!-- تاريخ النشر -->
                    <div class="col-12">
                        <div class="meta-item">
                            <i class="fas fa-calendar text-success me-1"></i>
                            <small class="arabic-text">{{ $ad->created_at->diffForHumans() }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات التواصل -->
            @if($ad->contact_info)
                <div class="contact-info mb-2">
                    <div class="contact-preview">
                        <i class="fas fa-phone text-primary me-2"></i>
                        <small class="text-muted arabic-text">{{ __('Contact available') }}</small>
                    </div>
                </div>
            @endif
        </div>

        <div class="card-footer bg-transparent border-0 p-3 pt-0">
            <!-- أزرار الإجراءات -->
            <div class="ad-actions">
                <div class="row g-2">
                    <div class="col-8">
                        <a href="{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}"
                           class="btn btn-primary btn-sm w-100 arabic-text">
                            <i class="fas fa-eye me-2"></i>
                            {{ __('View Details') }}
                        </a>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-outline-secondary btn-sm w-100"
                                onclick="shareAd('{{ route('ads.show', [$ad->category->slug, $ad->slug]) }}', '{{ $ad->title }}')"
                                title="{{ __('Share') }}">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- تاريخ انتهاء الصلاحية -->
            @if($ad->expires_at)
                <div class="expiry-info mt-2 text-center">
                    <small class="text-muted arabic-text">
                        <i class="fas fa-hourglass-half me-1"></i>
                        {{ __('Expires') }}: {{ $ad->expires_at->format('Y/m/d') }}
                    </small>
                </div>
            @endif
        </div>

        <!-- تأثير hover -->
        <div class="card-overlay"></div>
    </div>
@if(!isset($noWrapper) || !$noWrapper)
</div>
@endif

@push('styles')
<style>
/* بطاقة الإعلان */
.ad-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid #e9ecef;
    min-height: 400px;
}

.ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* صورة الإعلان */
.ad-image {
    position: relative;
    height: 180px;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
}

.ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.ad-card:hover .ad-image img {
    transform: scale(1.05);
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* شارات الحالة */
.category-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    backdrop-filter: blur(10px);
}

.status-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: bold;
}

.status-badge.new {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.status-badge.urgent {
    background: rgba(220, 53, 69, 0.9);
    color: white;
}

/* عنوان الإعلان */
.ad-title {
    font-size: 1rem;
    line-height: 1.4;
    min-height: 2.5rem;
    margin-bottom: 0.75rem;
}

.ad-title a:hover {
    color: #007bff !important;
}

/* وصف الإعلان */
.ad-description {
    font-size: 0.85rem;
    line-height: 1.4;
    min-height: 2.5rem;
    margin-bottom: 0.75rem;
}

/* معلومات إضافية */
.meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.meta-item i {
    width: 16px;
    text-align: center;
}

/* معلومات التواصل */
.contact-preview {
    background: rgba(0, 123, 255, 0.1);
    padding: 0.5rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
}

/* أزرار الإجراءات */
.ad-actions .btn {
    border-radius: 8px;
    font-size: 0.85rem;
    padding: 0.5rem;
}

/* معلومات انتهاء الصلاحية */
.expiry-info {
    background: rgba(255, 193, 7, 0.1);
    padding: 0.25rem;
    border-radius: 5px;
}

/* تأثير overlay */
.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(0, 123, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.ad-card:hover .card-overlay {
    opacity: 1;
}

/* العرض القائمة */
.list-view .ad-card {
    flex-direction: row !important;
    align-items: stretch !important;
}

.list-view .ad-image {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
    border-radius: 15px 0 0 15px;
}

.list-view .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.list-view .ad-title {
    min-height: auto;
    font-size: 1.2rem;
}

.list-view .ad-description {
    min-height: auto;
    flex: 1;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .ad-image {
        height: 180px;
    }

    .ad-title {
        font-size: 1rem;
        min-height: 2.5rem;
    }

    .ad-description {
        font-size: 0.85rem;
        min-height: 2.5rem;
    }

    .ad-actions .btn {
        font-size: 0.8rem;
        padding: 0.4rem;
    }

    .list-view .ad-card {
        flex-direction: column !important;
    }

    .list-view .ad-image {
        width: 100%;
        height: 180px;
        border-radius: 15px 15px 0 0;
    }
}

@media (max-width: 576px) {
    .ad-image {
        height: 160px;
    }

    .category-badge,
    .status-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// دالة مشاركة الإعلان
function shareAd(url, title) {
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        }).catch(console.error);
    } else {
        // نسخ الرابط للحافظة
        navigator.clipboard.writeText(url).then(function() {
            // إظهار رسالة نجاح
            showToast('{{ __("Link copied to clipboard") }}', 'success');
        }).catch(function() {
            // فتح نافذة مشاركة تقليدية
            window.open('https://wa.me/?text=' + encodeURIComponent(title + ' - ' + url), '_blank');
        });
    }
}

// دالة إظهار الرسائل
function showToast(message, type = 'info') {
    // يمكن تطويرها لاحقاً لإظهار toast notifications
    alert(message);
}
</script>
@endpush
