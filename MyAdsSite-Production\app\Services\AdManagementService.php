<?php

namespace App\Services;

use App\Models\Ad;
use App\Models\User;
use App\Models\SecurityLog;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\UploadedFile;

/**
 * خدمة إدارة الإعلانات المتقدمة
 * تدير جميع عمليات الإعلانات مع التحسينات والأمان
 */
class AdManagementService
{
    /**
     * إنشاء إعلان جديد مع التحسينات
     */
    public static function createAd(array $data, ?UploadedFile $image = null, ?int $userId = null): array
    {
        try {
            DB::beginTransaction();

            $userId = $userId ?? auth()->id();

            // التحقق من صلاحيات المستخدم
            if (!self::canUserCreateAd($userId)) {
                return [
                    'success' => false,
                    'message' => 'لا يمكنك إنشاء إعلانات جديدة في الوقت الحالي'
                ];
            }

            // تنظيف وتحضير البيانات
            $cleanData = self::sanitizeAdData($data);
            $cleanData['user_id'] = $userId;
            $cleanData['status'] = 'pending'; // جميع الإعلانات تبدأ بحالة معلقة

            // معالجة الصورة إذا وجدت
            if ($image) {
                $imageResult = ImageOptimizationService::optimizeAndSave($image, 'ads');

                if ($imageResult['success']) {
                    $cleanData['image'] = $imageResult['main_path'];
                } else {
                    throw new \Exception('فشل في رفع الصورة: ' . $imageResult['error']);
                }
            }

            // إنشاء الإعلان
            $ad = Ad::create($cleanData);

            // تسجيل الحدث الأمني
            SecurityLog::logEvent(
                'ad_created',
                "تم إنشاء إعلان جديد: {$ad->title}",
                SecurityLog::SEVERITY_LOW,
                [
                    'ad_id' => $ad->id,
                    'title' => $ad->title,
                    'category_id' => $ad->category_id,
                    'has_image' => !empty($ad->image)
                ],
                $userId
            );

            // مسح الكاش المتعلق
            CacheService::clearAdsCache();
            CacheService::clearUserCache($userId);

            DB::commit();

            return [
                'success' => true,
                'message' => 'تم إنشاء الإعلان بنجاح وهو في انتظار المراجعة',
                'ad' => $ad
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('فشل في إنشاء الإعلان', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في إنشاء الإعلان: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحديث إعلان موجود
     */
    public static function updateAd(Ad $ad, array $data, ?UploadedFile $image = null): array
    {
        try {
            DB::beginTransaction();

            // التحقق من الصلاحيات
            if (!self::canUserEditAd($ad, auth()->id())) {
                return [
                    'success' => false,
                    'message' => 'ليس لديك صلاحية لتعديل هذا الإعلان'
                ];
            }

            $oldData = $ad->toArray();

            // تنظيف البيانات الجديدة
            $cleanData = self::sanitizeAdData($data);

            // معالجة الصورة الجديدة
            if ($image) {
                // حذف الصورة القديمة
                if ($ad->image) {
                    ImageOptimizationService::deleteImageSizes($ad->image);
                }

                $imageResult = ImageOptimizationService::optimizeAndSave($image, 'ads');

                if ($imageResult['success']) {
                    $cleanData['image'] = $imageResult['main_path'];
                } else {
                    throw new \Exception('فشل في رفع الصورة الجديدة: ' . $imageResult['error']);
                }
            }

            // تحديث الإعلان
            $ad->update($cleanData);

            // تسجيل التغييرات
            $changes = array_diff_assoc($cleanData, $oldData);
            if (!empty($changes)) {
                SecurityLog::logEvent(
                    'ad_updated',
                    "تم تحديث الإعلان: {$ad->title}",
                    SecurityLog::SEVERITY_LOW,
                    [
                        'ad_id' => $ad->id,
                        'changes' => $changes,
                        'old_data' => $oldData
                    ],
                    $ad->user_id
                );
            }

            // مسح الكاش
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);

            DB::commit();

            return [
                'success' => true,
                'message' => 'تم تحديث الإعلان بنجاح',
                'ad' => $ad->fresh()
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('فشل في تحديث الإعلان', [
                'ad_id' => $ad->id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في تحديث الإعلان: ' . $e->getMessage()
            ];
        }
    }

    /**
     * حذف إعلان
     */
    public static function deleteAd(Ad $ad): array
    {
        try {
            DB::beginTransaction();

            // التحقق من الصلاحيات
            if (!self::canUserDeleteAd($ad, auth()->id())) {
                return [
                    'success' => false,
                    'message' => 'ليس لديك صلاحية لحذف هذا الإعلان'
                ];
            }

            $adData = $ad->toArray();

            // حذف الصورة
            if ($ad->image) {
                ImageOptimizationService::deleteImageSizes($ad->image);
            }

            // حذف الإعلان
            $ad->delete();

            // تسجيل الحدث
            SecurityLog::logEvent(
                'ad_deleted',
                "تم حذف الإعلان: {$adData['title']}",
                SecurityLog::SEVERITY_MEDIUM,
                [
                    'ad_id' => $adData['id'],
                    'title' => $adData['title'],
                    'category_id' => $adData['category_id']
                ],
                $adData['user_id']
            );

            // مسح الكاش
            CacheService::clearAdsCache();
            CacheService::clearUserCache($adData['user_id']);

            DB::commit();

            return [
                'success' => true,
                'message' => 'تم حذف الإعلان بنجاح'
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('فشل في حذف الإعلان', [
                'ad_id' => $ad->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في حذف الإعلان'
            ];
        }
    }

    /**
     * تفعيل/إلغاء تفعيل إعلان (للإدارة)
     */
    public static function toggleAdStatus(Ad $ad, string $status, ?string $reason = null): array
    {
        try {
            $oldStatus = $ad->status;

            $ad->update([
                'status' => $status,
                'rejection_reason' => $status === 'rejected' ? $reason : null
            ]);

            // تسجيل التغيير
            SecurityLog::logEvent(
                'ad_status_changed',
                "تم تغيير حالة الإعلان من {$oldStatus} إلى {$status}",
                SecurityLog::SEVERITY_MEDIUM,
                [
                    'ad_id' => $ad->id,
                    'old_status' => $oldStatus,
                    'new_status' => $status,
                    'reason' => $reason
                ],
                auth()->id()
            );

            // مسح الكاش
            CacheService::clearAdsCache();

            return [
                'success' => true,
                'message' => 'تم تغيير حالة الإعلان بنجاح'
            ];

        } catch (\Exception $e) {
            Log::error('فشل في تغيير حالة الإعلان', [
                'ad_id' => $ad->id,
                'status' => $status,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في تغيير حالة الإعلان'
            ];
        }
    }

    /**
     * تنظيف بيانات الإعلان
     */
    private static function sanitizeAdData(array $data): array
    {
        return [
            'title_ar' => strip_tags(trim($data['title_ar'] ?? '')),
            'title_en' => strip_tags(trim($data['title_en'] ?? '')),
            'description_ar' => strip_tags(trim($data['description_ar'] ?? ''), '<p><br><strong><em>'),
            'description_en' => strip_tags(trim($data['description_en'] ?? ''), '<p><br><strong><em>'),
            'category_id' => (int)($data['category_id'] ?? 0),
            'phone' => preg_replace('/[^\d+\-\s]/', '', $data['phone'] ?? ''),
            'email' => filter_var($data['email'] ?? '', FILTER_SANITIZE_EMAIL),
            'location' => strip_tags(trim($data['location'] ?? '')),
            'expires_at' => !empty($data['expires_at']) ? $data['expires_at'] : now()->addDays(30),
        ];
    }

    /**
     * التحقق من قدرة المستخدم على إنشاء إعلان
     */
    private static function canUserCreateAd(?int $userId): bool
    {
        if (!$userId) return false;

        $user = User::find($userId);
        if (!$user) return false;

        // التحقق من عدد الإعلانات النشطة
        $activeAdsCount = $user->ads()->whereIn('status', ['active', 'pending'])->count();
        $maxAds = 10; // حد أقصى للإعلانات النشطة

        if ($activeAdsCount >= $maxAds) {
            return false;
        }

        // التحقق من عدم وجود حظر
        $recentRejections = $user->ads()
            ->where('status', 'rejected')
            ->where('created_at', '>=', now()->subDays(7))
            ->count();

        if ($recentRejections >= 3) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من قدرة المستخدم على تعديل إعلان
     */
    private static function canUserEditAd(Ad $ad, ?int $userId): bool
    {
        if (!$userId) return false;

        // المالك يمكنه التعديل
        if ($ad->user_id === $userId) return true;

        // الإدارة يمكنها التعديل
        $user = User::find($userId);
        return $user && $user->is_admin;
    }

    /**
     * التحقق من قدرة المستخدم على حذف إعلان
     */
    private static function canUserDeleteAd(Ad $ad, ?int $userId): bool
    {
        return self::canUserEditAd($ad, $userId);
    }

    /**
     * الحصول على إحصائيات الإعلانات
     */
    public static function getAdStats(?int $userId = null): array
    {
        $baseQuery = Ad::query();

        if ($userId) {
            $baseQuery->where('user_id', $userId);
        }

        return [
            'total' => (clone $baseQuery)->count(),
            'active' => (clone $baseQuery)->where('status', 'active')->count(),
            'pending' => (clone $baseQuery)->where('status', 'pending')->count(),
            'rejected' => (clone $baseQuery)->where('status', 'rejected')->count(),
            'expired' => (clone $baseQuery)->where('expires_at', '<', now())->count(),
            'featured' => (clone $baseQuery)->where('is_featured', true)->count(),
            'total_views' => (clone $baseQuery)->sum('views_count'),
        ];
    }
}
