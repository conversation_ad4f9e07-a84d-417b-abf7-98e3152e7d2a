@extends('layouts.app')

@section('title', __('Login') . ' - ' . __('site_name'))
@section('description', __('Login to your account to access all features'))

@section('content')
<!-- القسم الرئيسي لتسجيل الدخول -->
<section class="auth-section py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 80vh;">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-70">
            <div class="col-lg-5 col-md-7">
                <!-- بطاقة تسجيل الدخول -->
                <div class="auth-card">
                    <div class="auth-header text-center mb-4">
                        <div class="auth-icon mb-3">
                            <i class="fas fa-sign-in-alt fa-3x text-primary"></i>
                        </div>
                        <h2 class="arabic-text fw-bold text-dark mb-2">{{ __('Login') }}</h2>
                        <p class="text-muted arabic-text">{{ __('Login to your account to access all features') }}</p>
                    </div>

                    <!-- رسائل الأخطاء -->
                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li class="arabic-text">{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- نموذج تسجيل الدخول -->
                    <form method="POST" action="{{ route('auth.login.submit') }}" class="auth-form">
                        @csrf
                        
                        <!-- البريد الإلكتروني -->
                        <div class="form-group mb-3">
                            <label for="email" class="form-label arabic-text fw-bold">
                                <i class="fas fa-envelope me-2 text-primary"></i>
                                {{ __('Email') }}
                            </label>
                            <input type="email" 
                                   class="form-control @error('email') is-invalid @enderror" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}" 
                                   placeholder="{{ __('Enter your email') }}"
                                   required 
                                   autofocus>
                            @error('email')
                                <div class="invalid-feedback arabic-text">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- كلمة المرور -->
                        <div class="form-group mb-3">
                            <label for="password" class="form-label arabic-text fw-bold">
                                <i class="fas fa-lock me-2 text-primary"></i>
                                {{ __('Password') }}
                            </label>
                            <div class="password-input-group">
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       placeholder="{{ __('Enter your password') }}"
                                       required>
                                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="password-icon"></i>
                                </button>
                            </div>
                            @error('password')
                                <div class="invalid-feedback arabic-text">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- تذكرني -->
                        <div class="form-group mb-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label arabic-text" for="remember">
                                    {{ __('Remember me') }}
                                </label>
                            </div>
                        </div>

                        <!-- زر تسجيل الدخول -->
                        <div class="form-group mb-4">
                            <button type="submit" class="btn btn-primary btn-lg w-100 arabic-text fw-bold">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                {{ __('Login') }}
                            </button>
                        </div>

                        <!-- روابط إضافية -->
                        <div class="auth-links text-center">
                            <p class="text-muted arabic-text mb-2">
                                {{ __("Don't have an account?") }}
                                <a href="{{ route('auth.register') }}" class="text-primary fw-bold text-decoration-none">
                                    {{ __('Create new account') }}
                                </a>
                            </p>
                            <p class="text-muted arabic-text mb-3">
                                <a href="#" class="text-secondary small text-decoration-none" onclick="alert('{{ __('Contact admin to reset password') }}')">
                                    {{ __('Forgot password?') }}
                                </a>
                            </p>
                            
                            <!-- روابط التنقل -->
                            <div class="navigation-links">
                                <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-sm me-2">
                                    <i class="fas fa-home me-1"></i>
                                    {{ __('Home') }}
                                </a>
                                <a href="{{ route('action.choice') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-arrow-right me-1"></i>
                                    {{ __('Back') }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- معلومات إضافية -->
                <div class="auth-info mt-4 text-center">
                    <div class="info-card">
                        <h6 class="text-white arabic-text fw-bold mb-2">
                            <i class="fas fa-shield-alt me-2"></i>
                            {{ __('Secure Login') }}
                        </h6>
                        <p class="text-white-50 arabic-text small mb-0">
                            {{ __('Your data is protected with advanced encryption') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* تنسيق القسم الرئيسي */
.auth-section {
    position: relative;
    overflow: hidden;
}

.auth-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* بطاقة المصادقة */
.auth-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.auth-card:hover::before {
    left: 100%;
}

/* أيقونة المصادقة */
.auth-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* نموذج المصادقة */
.auth-form .form-control {
    border-radius: 15px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.auth-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-2px);
}

.auth-form .form-label {
    margin-bottom: 0.5rem;
    color: #495057;
}

/* مجموعة كلمة المرور */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #007bff;
}

/* زر تسجيل الدخول */
.auth-form .btn-primary {
    border-radius: 15px;
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.auth-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
}

/* روابط المصادقة */
.auth-links a {
    transition: all 0.3s ease;
}

.auth-links a:hover {
    transform: translateY(-1px);
}

.navigation-links .btn {
    border-radius: 50px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* معلومات إضافية */
.info-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .auth-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }
    
    .auth-section {
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .navigation-links .btn {
        display: block;
        margin: 0.25rem 0;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .auth-card {
        padding: 1.5rem 1rem;
    }
    
    .auth-form .form-control {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
    }
    
    .auth-form .btn-primary {
        font-size: 1rem;
        padding: 0.6rem 1rem;
    }
    
    .info-card {
        padding: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// دالة إظهار/إخفاء كلمة المرور
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '-icon');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // تركيز تلقائي على حقل البريد الإلكتروني
    const emailInput = document.getElementById('email');
    if (emailInput && !emailInput.value) {
        emailInput.focus();
    }
    
    // إضافة تأثيرات للحقول
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});
</script>
@endpush
