@extends('layouts.app')

@section('title', __('Manage Ads') . ' - ' . __('Admin Panel'))

@section('content')
<div class="container-fluid my-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 fw-bold text-primary">
                        <i class="fas fa-cogs me-2"></i>
                        {{ __('Manage Ads') }}
                    </h1>
                    <p class="text-muted arabic-text">{{ __('Review and manage all ads on the site') }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.ads.stats') }}" class="btn btn-outline-info me-2 arabic-text">
                        <i class="fas fa-chart-bar me-1"></i>
                        {{ __('Statistics') }}
                    </a>
                    <a href="{{ route('ads.index') }}" class="btn btn-outline-secondary arabic-text">
                        <i class="fas fa-eye me-1"></i>
                        {{ __('View Site') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h3 class="fw-bold">{{ $stats['pending'] }}</h3>
                    <small class="arabic-text">{{ __('Pending Review') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h3 class="fw-bold">{{ $stats['active'] }}</h3>
                    <small class="arabic-text">{{ __('Active Ads') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h3 class="fw-bold">{{ $stats['rejected'] }}</h3>
                    <small class="arabic-text">{{ __('Rejected Ads') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-list fa-2x mb-2"></i>
                    <h3 class="fw-bold">{{ $stats['total'] }}</h3>
                    <small class="arabic-text">{{ __('Total Ads') }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر الحالة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <a href="{{ route('admin.ads.index', ['status' => 'pending']) }}"
                           class="btn {{ $status === 'pending' ? 'btn-warning' : 'btn-outline-warning' }} arabic-text">
                            <i class="fas fa-clock me-1"></i>
                            {{ __('Pending Review') }} ({{ $stats['pending'] }})
                        </a>
                        <a href="{{ route('admin.ads.index', ['status' => 'active']) }}"
                           class="btn {{ $status === 'active' ? 'btn-success' : 'btn-outline-success' }} arabic-text">
                            <i class="fas fa-check-circle me-1"></i>
                            {{ __('Active') }} ({{ $stats['active'] }})
                        </a>
                        <a href="{{ route('admin.ads.index', ['status' => 'rejected']) }}"
                           class="btn {{ $status === 'rejected' ? 'btn-danger' : 'btn-outline-danger' }} arabic-text">
                            <i class="fas fa-times-circle me-1"></i>
                            {{ __('Rejected') }} ({{ $stats['rejected'] }})
                        </a>
                        <a href="{{ route('admin.ads.index', ['status' => 'all']) }}"
                           class="btn {{ $status === 'all' ? 'btn-info' : 'btn-outline-info' }} arabic-text">
                            <i class="fas fa-list me-1"></i>
                            {{ __('All Ads') }} ({{ $stats['total'] }})
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($ads->count() > 0)
        <!-- قائمة الإعلانات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 arabic-text">
                            <i class="fas fa-list me-2"></i>
                            {{ __('Ads List') }}
                        </h5>
                        @if($status === 'pending')
                            <div>
                                <button type="button" class="btn btn-sm btn-success arabic-text" onclick="bulkAction('approve')">
                                    <i class="fas fa-check me-1"></i>
                                    {{ __('Approve Selected') }}
                                </button>
                                <button type="button" class="btn btn-sm btn-danger arabic-text" onclick="bulkAction('reject')">
                                    <i class="fas fa-times me-1"></i>
                                    {{ __('Reject Selected') }}
                                </button>
                            </div>
                        @endif
                    </div>

                    <div class="card-body p-0">
                        <form id="bulkForm" method="POST" style="display: none;">
                            @csrf
                        </form>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            @if($status === 'pending')
                                                <th width="50">
                                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                                </th>
                                            @endif
                                            <th class="arabic-text">{{ __('Ad') }}</th>
                                            <th class="arabic-text">{{ __('Category') }}</th>
                                            <th class="arabic-text">{{ __('User') }}</th>
                                            <th class="arabic-text">{{ __('Status') }}</th>
                                            <th class="arabic-text">{{ __('Created Date') }}</th>
                                            <th class="arabic-text">{{ __('Actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($ads as $ad)
                                            <tr>
                                                @if($status === 'pending')
                                                    <td>
                                                        <input type="checkbox" name="ads[]" value="{{ $ad->id }}" class="form-check-input ad-checkbox" form="bulkForm">
                                                    </td>
                                                @endif
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        @if($ad->image)
                                                            <img src="{{ asset('storage/' . $ad->image) }}"
                                                                 alt="{{ $ad->title }}"
                                                                 class="rounded me-3"
                                                                 style="width: 60px; height: 60px; object-fit: cover;">
                                                        @else
                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                                 style="width: 60px; height: 60px;">
                                                                <i class="{{ $ad->category->icon }} text-muted"></i>
                                                            </div>
                                                        @endif
                                                        <div>
                                                            <h6 class="mb-1">{{ Str::limit($ad->title, 50) }}</h6>
                                                            <small class="text-muted">{{ Str::limit($ad->description, 80) }}</small>
                                                            @if($ad->is_featured)
                                                                <span class="badge bg-warning text-dark ms-1">
                                                                    <i class="fas fa-star"></i> {{ __('Featured') }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">
                                                        <i class="{{ $ad->category->icon }} me-1"></i>
                                                        {{ $ad->category->name }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong>{{ $ad->user->name }}</strong>
                                                        <br>
                                                        <small class="text-muted">{{ $ad->user->email }}</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    @php
                                                        $statusClasses = [
                                                            'pending' => 'bg-warning text-dark',
                                                            'active' => 'bg-success',
                                                            'rejected' => 'bg-danger',
                                                            'inactive' => 'bg-secondary'
                                                        ];
                                                        $statusTexts = [
                                                            'pending' => __('Pending Review'),
                                                            'active' => __('Active'),
                                                            'rejected' => __('Rejected'),
                                                            'inactive' => __('Inactive')
                                                        ];
                                                    @endphp
                                                    <span class="badge {{ $statusClasses[$ad->status] ?? 'bg-secondary' }}">
                                                        {{ $statusTexts[$ad->status] ?? $ad->status }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div>
                                                        {{ $ad->created_at->format('Y-m-d') }}
                                                        <br>
                                                        <small class="text-muted">{{ $ad->created_at->diffForHumans() }}</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ route('admin.ads.show', $ad) }}"
                                                           class="btn btn-sm btn-outline-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>

                                                        @if($ad->status === 'pending')
                                                            <form method="POST" action="{{ route('admin.ads.approve', $ad) }}" class="d-inline">
                                                                @csrf
                                                                <button type="submit" class="btn btn-sm btn-success"
                                                                        onclick="return confirm('{{ __('Do you want to approve this ad?') }}')">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </form>
                                                            <button type="button" class="btn btn-sm btn-danger"
                                                                    onclick="rejectAd({{ $ad->id }})">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        @endif

                                                        <form method="POST" action="{{ route('admin.ads.destroy', $ad) }}" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                    onclick="return confirm('{{ __('Are you sure you want to permanently delete this ad?') }}')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    @if($ads->hasPages())
                        <div class="card-footer bg-light">
                            {{ $ads->links('pagination.custom') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @else
        <!-- رسالة عدم وجود إعلانات -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-5x text-muted mb-3"></i>
                    <h3 class="text-muted arabic-text">{{ __('No ads found') }}</h3>
                    <p class="text-muted arabic-text">
                        @if($status === 'pending')
                            {{ __('No ads pending review') }}
                        @else
                            {{ __('No ads in this category') }}
                        @endif
                    </p>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Modal رفض الإعلان -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title arabic-text">{{ __('Reject Ad') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reason" class="form-label arabic-text">{{ __('Rejection Reason (Optional)') }}</label>
                        <textarea class="form-control arabic-text" id="reason" name="reason" rows="3"
                                  placeholder="{{ __('Write the reason for rejecting the ad...') }}"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary arabic-text" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-danger arabic-text">{{ __('Reject Ad') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تحديد/إلغاء تحديد جميع الإعلانات
document.getElementById('selectAll')?.addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.ad-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// رفض إعلان
function rejectAd(adId) {
    const form = document.getElementById('rejectForm');
    form.action = `/admin/ads/${adId}/reject`;
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

// إجراءات جماعية
function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.ad-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('{{ __("Please select at least one ad") }}');
        return;
    }

    const form = document.getElementById('bulkForm');
    if (action === 'approve') {
        form.action = '{{ route("admin.ads.bulk-approve") }}';
        if (confirm('{{ __("Do you want to approve the selected ads?") }}')) {
            form.submit();
        }
    } else if (action === 'reject') {
        form.action = '{{ route("admin.ads.bulk-reject") }}';
        if (confirm('{{ __("Do you want to reject the selected ads?") }}')) {
            form.submit();
        }
    }
}
</script>
@endsection
