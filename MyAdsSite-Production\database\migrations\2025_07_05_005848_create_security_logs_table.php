<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('security_logs', function (Blueprint $table) {
            $table->id();

            // معرف المستخدم (اختياري)
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            // معلومات الطلب
            $table->string('ip_address', 45); // يدعم IPv4 و IPv6
            $table->text('user_agent')->nullable();

            // تفاصيل الحدث الأمني
            $table->string('action', 100); // نوع الإجراء
            $table->text('description'); // وصف الحدث
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('low');
            $table->json('details')->nullable(); // تفاصيل إضافية

            // حالة الحدث
            $table->enum('status', ['open', 'investigating', 'resolved', 'false_positive'])->default('open');
            $table->timestamp('resolved_at')->nullable();
            $table->foreignId('resolved_by')->nullable()->constrained('users')->onDelete('set null');

            $table->timestamps();

            // فهارس لتحسين الأداء
            $table->index(['ip_address'], 'security_logs_ip_index');
            $table->index(['action'], 'security_logs_action_index');
            $table->index(['severity'], 'security_logs_severity_index');
            $table->index(['status'], 'security_logs_status_index');
            $table->index(['created_at'], 'security_logs_created_at_index');
            $table->index(['user_id', 'action'], 'security_logs_user_action_index');
            $table->index(['severity', 'status'], 'security_logs_severity_status_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('security_logs');
    }
};
