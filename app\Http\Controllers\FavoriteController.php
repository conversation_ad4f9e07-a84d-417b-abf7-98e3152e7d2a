<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use App\Models\Favorite;
use App\Models\Ad;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * تحكم في المفضلة (Favorites/Wishlist)
 * يدير إضافة وحذف وعرض الإعلانات المفضلة للمستخدمين
 */
class FavoriteController extends Controller
{
    /**
     * Constructor - تطبيق middleware للمصادقة
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('rate.limit:general')->except(['index', 'show']);
    }

    /**
     * عرض صفحة المفضلة للمستخدم
     */
    public function index(Request $request): View
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 12);
        
        // التحقق من صحة عدد العناصر في الصفحة
        if (!in_array($perPage, [6, 12, 24, 48])) {
            $perPage = 12;
        }

        // الحصول على المفضلة مع الإعلانات النشطة
        $favorites = Favorite::getUserFavoritesWithAds($user->id, $perPage);
        
        // إحصائيات سريعة
        $stats = [
            'total_favorites' => $user->favorites_count,
            'active_favorites' => $favorites->total(),
        ];

        return view('favorites.index', compact('favorites', 'stats', 'perPage'));
    }

    /**
     * إضافة إعلان إلى المفضلة (AJAX)
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'ad_id' => 'required|integer|exists:ads,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => __('Invalid data'),
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $adId = $request->input('ad_id');

            // التحقق من وجود الإعلان وأنه نشط
            $ad = Ad::active()->notExpired()->find($adId);
            if (!$ad) {
                return response()->json([
                    'success' => false,
                    'message' => __('Ad not found or inactive')
                ], 404);
            }

            // التحقق من أن المستخدم لا يضيف إعلانه الخاص
            if ($ad->user_id === $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => __('Cannot add your own ad to favorites')
                ], 400);
            }

            // إضافة إلى المفضلة
            $result = $user->addToFavorites($adId);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => __('Added to favorites successfully'),
                    'is_favorited' => true,
                    'favorites_count' => $user->fresh()->favorites_count
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => __('Already in favorites')
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Error adding ad to favorites: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * إزالة إعلان من المفضلة (AJAX)
     */
    public function destroy(Request $request, int $adId): JsonResponse
    {
        try {
            $user = Auth::user();

            // التحقق من وجود الإعلان في المفضلة
            if (!$user->hasFavorited($adId)) {
                return response()->json([
                    'success' => false,
                    'message' => __('Not in favorites')
                ], 404);
            }

            // إزالة من المفضلة
            $result = $user->removeFromFavorites($adId);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => __('Removed from favorites successfully'),
                    'is_favorited' => false,
                    'favorites_count' => $user->fresh()->favorites_count
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => __('Failed to remove from favorites')
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Error removing ad from favorites: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * تبديل حالة الإعلان في المفضلة (إضافة/إزالة) - AJAX
     */
    public function toggle(Request $request): JsonResponse
    {
        try {
            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'ad_id' => 'required|integer|exists:ads,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => __('Invalid data'),
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $adId = $request->input('ad_id');

            // التحقق من وجود الإعلان وأنه نشط
            $ad = Ad::active()->notExpired()->find($adId);
            if (!$ad) {
                return response()->json([
                    'success' => false,
                    'message' => __('Ad not found or inactive')
                ], 404);
            }

            // التحقق من أن المستخدم لا يضيف إعلانه الخاص
            if ($ad->user_id === $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => __('Cannot add your own ad to favorites')
                ], 400);
            }

            // تبديل حالة المفضلة
            $result = $user->toggleFavorite($adId);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'action' => $result['action'],
                    'is_favorited' => $result['is_favorited'],
                    'favorites_count' => $user->fresh()->favorites_count
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Error toggling favorite status: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * التحقق من حالة الإعلان في المفضلة (AJAX)
     */
    public function check(Request $request, int $adId): JsonResponse
    {
        try {
            $user = Auth::user();
            $isFavorited = $user->hasFavorited($adId);

            return response()->json([
                'success' => true,
                'is_favorited' => $isFavorited,
                'favorites_count' => $user->favorites_count
            ]);

        } catch (\Exception $e) {
            Log::error('Error checking favorite status: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Unexpected error')
            ], 500);
        }
    }

    /**
     * التحقق من حالة إعلانات متعددة في المفضلة (AJAX) - محسن للأداء
     */
    public function checkMultiple(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'ad_ids' => 'required|array|max:50',
                'ad_ids.*' => 'integer|exists:ads,id'
            ]);

            $user = Auth::user();
            $adIds = $request->input('ad_ids');

            // الحصول على جميع المفضلة للمستخدم في استعلام واحد
            $favoriteAdIds = $user->favorites()
                                  ->whereIn('ad_id', $adIds)
                                  ->pluck('ad_id')
                                  ->toArray();

            // إنشاء مصفوفة النتائج
            $favorites = [];
            foreach ($adIds as $adId) {
                $favorites[$adId] = in_array($adId, $favoriteAdIds);
            }

            return response()->json([
                'success' => true,
                'favorites' => $favorites,
                'favorites_count' => $user->favorites_count
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('Invalid data'),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error checking multiple favorites status: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error')
            ], 500);
        }
    }

    /**
     * مسح جميع المفضلة للمستخدم - AJAX
     */
    public function clearAll(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            // الحصول على عدد المفضلة قبل المسح
            $favoritesCount = $user->favorites()->count();

            if ($favoritesCount === 0) {
                return response()->json([
                    'success' => false,
                    'message' => __('No favorites to clear')
                ], 400);
            }

            // مسح جميع المفضلة
            $deletedCount = $user->favorites()->delete();

            return response()->json([
                'success' => true,
                'message' => __('All favorites cleared successfully'),
                'deleted_count' => $deletedCount,
                'favorites_count' => 0
            ]);

        } catch (\Exception $e) {
            Log::error('Error clearing all favorites: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error occurred')
            ], 500);
        }
    }

    /**
     * الحصول على إحصائيات المفضلة للمستخدم (AJAX)
     */
    public function stats(): JsonResponse
    {
        try {
            $user = Auth::user();

            $stats = [
                'total_favorites' => $user->favorites_count,
                'recent_favorites' => $user->favorites()
                                          ->with('ad')
                                          ->latest()
                                          ->limit(5)
                                          ->get()
                                          ->map(function ($favorite) {
                                              return [
                                                  'id' => $favorite->ad->id,
                                                  'title' => $favorite->ad->title,
                                                  'slug' => $favorite->ad->slug,
                                                  'added_at' => $favorite->created_at->diffForHumans()
                                              ];
                                          })
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting favorites statistics: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Unexpected error')
            ], 500);
        }
    }

}
