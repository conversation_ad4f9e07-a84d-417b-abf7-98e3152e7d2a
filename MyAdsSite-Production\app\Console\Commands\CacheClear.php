<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheService;

/**
 * أمر مسح الكاش
 * يقوم بمسح جميع أنواع الكاش في التطبيق
 */
class CacheClear extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'cache:clear-all {--type=all : نوع الكاش المراد مسحه (all, ads, categories, users, announcements)}';

    /**
     * وصف الأمر
     */
    protected $description = 'مسح الكاش حسب النوع المحدد';

    /**
     * تنفيذ الأمر
     */
    public function handle()
    {
        $type = $this->option('type');

        $this->info("بدء مسح الكاش من نوع: {$type}");

        try {
            switch ($type) {
                case 'ads':
                    CacheService::clearAdsCache();
                    $this->info('✅ تم مسح كاش الإعلانات بنجاح');
                    break;

                case 'categories':
                    CacheService::clearCategoriesCache();
                    $this->info('✅ تم مسح كاش التصنيفات بنجاح');
                    break;

                case 'announcements':
                    CacheService::clearAnnouncementsCache();
                    $this->info('✅ تم مسح كاش الإعلانات بنجاح');
                    break;

                case 'all':
                default:
                    $result = CacheService::clearAllCache();
                    if ($result) {
                        $this->info('✅ تم مسح جميع أنواع الكاش بنجاح');
                    } else {
                        $this->error('❌ فشل في مسح الكاش');
                        return 1;
                    }
                    break;
            }

            // إعادة تسخين الكاش إذا كان المسح شاملاً
            if ($type === 'all') {
                $this->info('🔄 إعادة تسخين الكاش...');
                CacheService::warmUpCache();
                $this->info('✅ تم إعادة تسخين الكاش بنجاح');
            }

        } catch (\Exception $e) {
            $this->error('❌ خطأ في مسح الكاش: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
