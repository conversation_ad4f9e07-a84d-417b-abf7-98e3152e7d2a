<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * نموذج التصنيفات
 * يحتوي على تصنيفات الإعلانات مثل (مدارس، جامعات، مستشفيات، إلخ)
 */
class Category extends Model
{
    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'name_ar',        // الاسم بالعربية
        'name_en',        // الاسم بالإنجليزية
        'slug',           // الرابط المختصر
        'icon',           // أيقونة التصنيف
        'description_ar', // الوصف بالعربية
        'description_en', // الوصف بالإنجليزية
        'is_active',      // حالة التصنيف (فعال/غير فعال)
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'is_active' => 'boolean', // تحويل إلى boolean
    ];

    /**
     * علاقة واحد إلى متعدد مع الإعلانات
     * تصنيف واحد يحتوي على عدة إعلانات
     */
    public function ads(): HasMany
    {
        return $this->hasMany(Ad::class);
    }

    /**
     * الحصول على اسم التصنيف حسب اللغة الحالية
     */
    public function getNameAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * الحصول على وصف التصنيف حسب اللغة الحالية
     */
    public function getDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : $this->description_en;
    }

    /**
     * البحث في التصنيفات النشطة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
