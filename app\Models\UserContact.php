<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج معلومات التواصل للمستخدمين
 * يحتوي على جميع طرق التواصل المرتبطة بكل مستخدم
 */
class UserContact extends Model
{
    /**
     * اسم الجدول
     */
    protected $table = 'user_contacts';

    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',
        'contact_type',
        'contact_value',
        'icon_type',
        'display_label',
        'is_primary',
        'is_public',
        'is_verified',
        'display_order',
        'privacy_level',
    ];

    /**
     * تحويل الحقول إلى أنواع محددة
     */
    protected $casts = [
        'is_primary' => 'boolean',
        'is_public' => 'boolean',
        'is_verified' => 'boolean',
        'display_order' => 'integer',
    ];

    /**
     * أنواع التواصل المتاحة
     */
    public const CONTACT_TYPES = [
        'phone' => 'هاتف',
        'whatsapp' => 'واتساب',
        'email' => 'إيميل',
        'telegram' => 'تليجرام',
        'instagram' => 'إنستجرام',
        'facebook' => 'فيسبوك',
        'twitter' => 'تويتر',
        'linkedin' => 'لينكد إن',
        'website' => 'موقع إلكتروني',
        'other' => 'أخرى',
    ];

    /**
     * مستويات الخصوصية المتاحة
     */
    public const PRIVACY_LEVELS = [
        'public' => 'عام للجميع',
        'registered_users' => 'للمستخدمين المسجلين فقط',
        'private' => 'خاص (لا يظهر)',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * نطاق للحصول على معلومات التواصل العامة فقط
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true)
                    ->where('privacy_level', 'public');
    }

    /**
     * نطاق للحصول على معلومات التواصل للمستخدمين المسجلين
     */
    public function scopeForRegisteredUsers($query)
    {
        return $query->where('is_public', true)
                    ->whereIn('privacy_level', ['public', 'registered_users']);
    }

    /**
     * نطاق للحصول على معلومات التواصل مرتبة حسب الأولوية
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('is_primary', 'desc')
                    ->orderBy('display_order', 'asc')
                    ->orderBy('created_at', 'asc');
    }

    /**
     * نطاق للحصول على معلومات التواصل المتحقق منها
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * الحصول على اسم نوع التواصل بالعربية
     */
    public function getContactTypeNameAttribute(): string
    {
        return self::CONTACT_TYPES[$this->contact_type] ?? 'غير محدد';
    }

    /**
     * الحصول على اسم مستوى الخصوصية بالعربية
     */
    public function getPrivacyLevelNameAttribute(): string
    {
        return self::PRIVACY_LEVELS[$this->privacy_level] ?? 'غير محدد';
    }

    /**
     * الحصول على الأيقونة المناسبة لنوع التواصل
     */
    public function getIconClassAttribute(): string
    {
        if ($this->icon_type) {
            return $this->icon_type;
        }

        $defaultIcons = [
            'phone' => 'fas fa-phone',
            'whatsapp' => 'fab fa-whatsapp',
            'email' => 'fas fa-envelope',
            'telegram' => 'fab fa-telegram',
            'instagram' => 'fab fa-instagram',
            'facebook' => 'fab fa-facebook',
            'twitter' => 'fab fa-twitter',
            'linkedin' => 'fab fa-linkedin',
            'website' => 'fas fa-globe',
            'other' => 'fas fa-link',
        ];

        return $defaultIcons[$this->contact_type] ?? 'fas fa-link';
    }

    /**
     * الحصول على رابط التواصل المباشر
     */
    public function getContactLinkAttribute(): string
    {
        switch ($this->contact_type) {
            case 'phone':
                return 'tel:' . $this->contact_value;
            case 'whatsapp':
                $cleanNumber = preg_replace('/[^0-9+]/', '', $this->contact_value);
                return 'https://wa.me/' . $cleanNumber;
            case 'email':
                return 'mailto:' . $this->contact_value;
            case 'telegram':
                return 'https://t.me/' . $this->contact_value;
            case 'instagram':
                return 'https://instagram.com/' . $this->contact_value;
            case 'facebook':
                return 'https://facebook.com/' . $this->contact_value;
            case 'twitter':
                return 'https://twitter.com/' . $this->contact_value;
            case 'linkedin':
                return 'https://linkedin.com/in/' . $this->contact_value;
            case 'website':
                return $this->contact_value;
            default:
                return $this->contact_value;
        }
    }

    /**
     * تحديد ما إذا كان يمكن للمستخدم الحالي رؤية معلومات التواصل
     */
    public function isVisibleTo($user = null): bool
    {
        if (!$this->is_public) {
            return false;
        }

        switch ($this->privacy_level) {
            case 'public':
                return true;
            case 'registered_users':
                return $user !== null;
            case 'private':
                return false;
            default:
                return false;
        }
    }
}
