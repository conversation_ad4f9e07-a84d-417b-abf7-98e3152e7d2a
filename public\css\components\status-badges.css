/*
 * ملف CSS متقدم لشارات الحالة والميزات
 * تم إنشاؤه كجزء من خطة تطوير صفحة تفاصيل الإعلان
 * يحتوي على أنماط متقدمة لجميع أنواع الشارات والحالات
 */

/* ===== متغيرات الألوان للشارات ===== */
:root {
    --badge-new: #10b981;
    --badge-featured: #f59e0b;
    --badge-pending: #6b7280;
    --badge-rejected: #ef4444;
    --badge-expired: #9ca3af;
    --badge-free: #06b6d4;
    --badge-negotiable: #8b5cf6;
    --badge-limited: #ec4899;
    --badge-discount: #f97316;
    --badge-warning: #eab308;
    --badge-popular: #3b82f6;
    --badge-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --badge-shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* ===== حاوي الشارات الرئيسي ===== */
.badges-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 10;
}

.badges-overlay {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
}

.badges-inline {
    position: relative;
    margin: 0.5rem 0;
}

.badges-detailed {
    gap: 0.75rem;
}

.badges-compact {
    gap: 0.25rem;
}

/* ===== مجموعات الشارات المحسّنة ===== */
.critical-badges-group,
.featured-badges-group,
.price-badges-group,
.feature-badges-group,
.warning-badges-group,
.achievement-badges-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    align-items: flex-start;
}

/* ترتيب الأولوية للمجموعات */
.critical-badges-group {
    order: 1; /* أولوية عالية جداً */
}

.featured-badges-group {
    order: 2; /* أولوية عالية */
}

.price-badges-group {
    order: 3; /* أولوية متوسطة-عالية */
}

.warning-badges-group {
    order: 4; /* أولوية متوسطة */
}

.feature-badges-group {
    order: 5; /* أولوية منخفضة-متوسطة */
}

.achievement-badges-group {
    order: 6; /* أولوية منخفضة */
}

/* ===== الشارات الأساسية المحسّنة ===== */
.status-badge,
.feature-badge,
.warning-badge,
.achievement-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--badge-shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(4px);
    /* إزالة max-width لمنع قطع النص */
    min-width: fit-content;
    white-space: nowrap;
    /* إزالة text-overflow و overflow لعرض النص كاملاً */
}

.status-badge:hover,
.feature-badge:hover,
.warning-badge:hover,
.achievement-badge:hover {
    transform: translateY(-1px);
    box-shadow: var(--badge-shadow-hover);
}

/* تحسينات للعرض المفصل */
.badges-detailed .status-badge,
.badges-detailed .feature-badge,
.badges-detailed .warning-badge,
.badges-detailed .achievement-badge {
    /* إزالة max-width لعرض النص كاملاً في الوضع المفصل */
    min-width: fit-content;
    max-width: none;
    padding: 0.4rem 0.75rem;
    font-size: 0.75rem;
}

/* تحسينات خاصة لشارة انتهاء الخصم في الوضع المفصل */
.badges-detailed .expires-badge {
    padding: 0.35rem 0.7rem;
    font-size: 0.7rem;
    font-weight: 600;
}

/* ===== شارات الحالة ===== */
.new-badge {
    background: linear-gradient(135deg, var(--badge-new) 0%, #059669 100%);
    animation: pulse-glow 2s infinite;
}

.featured-badge {
    background: linear-gradient(135deg, var(--badge-featured) 0%, #d97706 100%);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4);
}

.pending-badge {
    background: linear-gradient(135deg, var(--badge-pending) 0%, #4b5563 100%);
}

.rejected-badge {
    background: linear-gradient(135deg, var(--badge-rejected) 0%, #dc2626 100%);
}

.expired-badge {
    background: linear-gradient(135deg, var(--badge-expired) 0%, #6b7280 100%);
    opacity: 0.8;
}

/* ===== شارات الميزات ===== */
.free-badge {
    background: linear-gradient(135deg, var(--badge-free) 0%, #0891b2 100%);
    box-shadow: 0 2px 8px rgba(6, 182, 212, 0.4);
}

.negotiable-badge {
    background: linear-gradient(135deg, var(--badge-negotiable) 0%, #7c3aed 100%);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
}

.limited-badge {
    background: linear-gradient(135deg, var(--badge-limited) 0%, #db2777 100%);
    animation: pulse-limited 3s infinite;
}

.discount-badge {
    background: linear-gradient(135deg, var(--badge-discount) 0%, #ea580c 100%);
    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.4);
    animation: pulse-discount 2s infinite;
}

/* ===== شارات التحذير ===== */
.expires-soon-badge {
    background: linear-gradient(135deg, var(--badge-warning) 0%, #ca8a04 100%);
    animation: blink-warning 1.5s infinite;
}

.discount-expires-badge {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    animation: urgent-pulse 1s infinite;
}

/* ===== شارة انتهاء الخصم المحسّنة ===== */
.expires-badge {
    background: linear-gradient(135deg, var(--badge-warning) 0%, #ca8a04 100%);
    animation: blink-warning 1.5s infinite;
    /* تأكد من عرض النص كاملاً */
    min-width: max-content;
    max-width: none;
    padding: 0.3rem 0.6rem;
    font-size: 0.65rem;
    font-weight: 600;
    text-transform: none; /* إزالة uppercase للنصوص العربية */
    letter-spacing: normal;
}

/* ===== شارات الإنجاز ===== */
.popular-badge {
    background: linear-gradient(135deg, var(--badge-popular) 0%, #2563eb 100%);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

/* ===== الحركات والتأثيرات ===== */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.6);
        transform: scale(1.02);
    }
}

@keyframes pulse-limited {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(236, 72, 153, 0.3);
    }
    50% {
        box-shadow: 0 4px 12px rgba(236, 72, 153, 0.6);
    }
}

@keyframes pulse-discount {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(249, 115, 22, 0.3);
    }
    50% {
        box-shadow: 0 4px 12px rgba(249, 115, 22, 0.6);
    }
}

@keyframes blink-warning {
    0%, 50%, 100% {
        opacity: 1;
    }
    25%, 75% {
        opacity: 0.7;
    }
}

@keyframes urgent-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.6);
    }
}

/* ===== الاستجابة للأجهزة المحمولة ===== */
@media (max-width: 768px) {
    .badges-container {
        gap: 0.25rem;
    }

    .status-badge,
    .feature-badge,
    .warning-badge,
    .achievement-badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
        border-radius: 10px;
        /* الحفاظ على عرض النص كاملاً في الأجهزة المحمولة */
        min-width: fit-content;
        max-width: none;
    }

    /* تحسينات خاصة لشارة انتهاء الخصم في الأجهزة المحمولة */
    .expires-badge {
        font-size: 0.6rem;
        padding: 0.25rem 0.5rem;
        border-radius: 8px;
    }

    .badges-overlay {
        top: 8px;
        left: 8px;
        right: 8px;
    }

    .status-badges-group,
    .feature-badges-group,
    .warning-badges-group {
        gap: 0.2rem;
    }
}

@media (max-width: 576px) {
    .status-badge,
    .feature-badge,
    .warning-badge,
    .achievement-badge {
        font-size: 0.6rem;
        padding: 0.15rem 0.35rem;
        border-radius: 8px;
        /* الحفاظ على عرض النص كاملاً في الشاشات الصغيرة */
        min-width: fit-content;
        max-width: none;
    }

    /* تحسينات خاصة لشارة انتهاء الخصم في الشاشات الصغيرة */
    .expires-badge {
        font-size: 0.55rem;
        padding: 0.2rem 0.4rem;
        border-radius: 6px;
        /* تقليل المسافات للشاشات الصغيرة مع الحفاظ على وضوح النص */
        letter-spacing: 0;
    }

    .badges-container {
        gap: 0.2rem;
    }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .badges-container {
        position: static;
        display: block;
        margin-bottom: 0.5rem;
    }
    
    .status-badge,
    .feature-badge,
    .warning-badge,
    .achievement-badge {
        background: #f3f4f6 !important;
        color: #374151 !important;
        border: 1px solid #d1d5db;
        box-shadow: none;
        animation: none;
    }
    
    .status-badges-group,
    .feature-badges-group,
    .warning-badges-group {
        display: inline;
        margin-right: 0.5rem;
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
.status-badge:focus,
.feature-badge:focus,
.warning-badge:focus,
.achievement-badge:focus {
    outline: 2px solid currentColor;
    outline-offset: 2px;
}

/* ===== تحسينات الوضع المظلم (معطل مؤقتاً) ===== */
/* تم إلغاء تفعيل الوضع المظلم مؤقتاً - سيتم تفعيله لاحقاً عند الطلب */
/*
@media (prefers-color-scheme: dark) {
    .status-badge,
    .feature-badge,
    .warning-badge,
    .achievement-badge {
        border-color: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(8px);
    }

    .badges-container {
        filter: brightness(1.1);
    }
}
*/

/* ===== تحسينات خاصة للعرض المفصل ===== */
.badges-detailed .status-badge,
.badges-detailed .feature-badge,
.badges-detailed .warning-badge,
.badges-detailed .achievement-badge {
    padding: 0.4rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 14px;
    min-height: 28px;
}

.badges-detailed .badges-container {
    gap: 0.5rem;
}

.badges-detailed .critical-badges-group,
.badges-detailed .featured-badges-group,
.badges-detailed .price-badges-group,
.badges-detailed .feature-badges-group,
.badges-detailed .warning-badges-group,
.badges-detailed .achievement-badges-group {
    gap: 0.5rem;
}

/* ===== قواعد خاصة للشارات الحرجة ===== */
.critical-badges-group .status-badge {
    animation: criticalPulse 2s ease-in-out infinite;
    border-width: 2px;
}

@keyframes criticalPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* ===== تحسينات للشارات المميزة ===== */
.featured-badges-group .status-badge {
    background: linear-gradient(135deg, var(--badge-featured) 0%, #d97706 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

/* ===== تحسينات شارات الأسعار (مبسطة) ===== */
.price-badges-group .feature-badge {
    font-weight: 800;
    border-width: 2px;
}

/* ===== تحسينات خاصة لصف الشارات الإضافية ===== */
.additional-badges-row {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
    align-items: center !important;
    margin-top: 0.5rem;
}

.additional-badges-row .feature-badge {
    flex-shrink: 0; /* منع تقليص الشارات */
    min-width: fit-content !important;
    max-width: none !important;
}

/* تحسينات خاصة للنصوص العربية في الشارات */
.feature-badge span,
.expires-badge span {
    direction: rtl;
    text-align: right;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* إزالة تأثيرات الخصم المعقدة لتبسيط التصميم */

/* ===== تأثيرات التفاعل المتقدمة ===== */
.status-badge:active,
.feature-badge:active,
.warning-badge:active,
.achievement-badge:active {
    transform: translateY(0) scale(0.98);
    transition: transform 0.1s ease;
}

/* ===== تحسينات الأداء ===== */
.badges-container {
    will-change: transform;
}

.status-badge,
.feature-badge,
.warning-badge,
.achievement-badge {
    will-change: transform, box-shadow;
}

/* ===== تخصيصات إضافية للمواضع المختلفة ===== */
.badges-overlay.top-right {
    top: 10px;
    right: 10px;
    left: auto;
}

.badges-overlay.top-left {
    top: 10px;
    left: 10px;
    right: auto;
}

.badges-overlay.bottom-right {
    bottom: 10px;
    right: 10px;
    top: auto;
    left: auto;
}

.badges-overlay.bottom-left {
    bottom: 10px;
    left: 10px;
    top: auto;
    right: auto;
}
