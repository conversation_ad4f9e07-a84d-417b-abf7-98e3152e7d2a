/**
 * إدارة الرسوم المتحركة عند التمرير
 * ملف مبسط ومحسن للأداء
 */

class ScrollAnimations {
    constructor() {
        this.observer = null;
        this.init();
    }

    /**
     * تهيئة مراقب التمرير
     */
    init() {
        // إعدادات محسنة للأداء
        const options = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        this.observer = new IntersectionObserver(this.handleIntersection.bind(this), options);
        this.observeElements();
    }

    /**
     * معالجة تقاطع العناصر
     */
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.animateElement(entry.target);
                this.observer.unobserve(entry.target);
            }
        });
    }

    /**
     * تحريك العنصر
     */
    animateElement(element) {
        const animationType = element.dataset.animation || 'fadeInUp';
        element.classList.add(`animate-${animationType}`);
        
        // إزالة الكلاس بعد انتهاء الرسوم المتحركة
        element.addEventListener('animationend', () => {
            element.classList.remove(`animate-${animationType}`);
            element.classList.add('animation-complete');
        }, { once: true });
    }

    /**
     * مراقبة العناصر
     */
    observeElements() {
        const elements = document.querySelectorAll('[data-animation]');
        elements.forEach(el => this.observer.observe(el));
    }

    /**
     * إضافة عنصر جديد للمراقبة
     */
    observe(element) {
        if (this.observer && element) {
            this.observer.observe(element);
        }
    }

    /**
     * تدمير المراقب
     */
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
    }
}

export default ScrollAnimations;
