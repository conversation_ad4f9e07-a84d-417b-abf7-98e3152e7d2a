@extends('layouts.app')

@section('title', __('Edit Ad'))

@push('styles')
<style>
.arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
}

@if(app()->getLocale() == 'ar')
.btn-group .btn {
    border-radius: 0;
}
.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}
@endif
</style>
@endpush

@section('content')
<div class="container py-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1 arabic-text text-warning" data-animation="fadeInLeft">
                        <i class="fas fa-edit me-2"></i>
                        {{ __('Edit Ad') }}
                    </h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('Update your ad information') }}</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.ads.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'right' : 'left' }} me-2"></i>
                        {{ __('Back to My Ads') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج التعديل -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        {{ __('Update Ad Details') }}
                    </h5>
                </div>

                <div class="card-body p-4">
                    <form action="{{ route('dashboard.ads.update', $ad) }}"
                          method="POST"
                          enctype="multipart/form-data"
                          id="editAdForm">
                        @csrf
                        @method('PUT')

                        <!-- العنوان بالعربية -->
                        <div class="mb-3">
                            <label for="title_ar" class="form-label required arabic-text">
                                <i class="fas fa-heading me-1"></i>
                                {{ __('Title (Arabic)') }}
                            </label>
                            <input type="text"
                                   class="form-control @error('title_ar') is-invalid @enderror"
                                   id="title_ar"
                                   name="title_ar"
                                   value="{{ old('title_ar', $ad->title_ar) }}"
                                   required
                                   maxlength="255"
                                   placeholder="{{ __('Enter ad title in Arabic') }}"
                                   dir="rtl">
                            @error('title_ar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- العنوان بالإنجليزية -->
                        <div class="mb-3">
                            <label for="title_en" class="form-label arabic-text">
                                <i class="fas fa-heading me-1"></i>
                                {{ __('Title (English)') }}
                            </label>
                            <input type="text"
                                   class="form-control @error('title_en') is-invalid @enderror"
                                   id="title_en"
                                   name="title_en"
                                   value="{{ old('title_en', $ad->title_en) }}"
                                   maxlength="255"
                                   placeholder="{{ __('Enter ad title in English (optional)') }}"
                                   dir="ltr">
                            @error('title_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- التصنيف -->
                        <div class="mb-3">
                            <label for="category_id" class="form-label required arabic-text">
                                <i class="fas fa-tags me-1"></i>
                                {{ __('Category') }}
                            </label>
                            <select class="form-select @error('category_id') is-invalid @enderror"
                                    id="category_id"
                                    name="category_id"
                                    required>
                                <option value="">{{ __('Select Category') }}</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}"
                                            {{ old('category_id', $ad->category_id) == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الوصف بالعربية -->
                        <div class="mb-3">
                            <label for="description_ar" class="form-label required arabic-text">
                                <i class="fas fa-align-left me-1"></i>
                                {{ __('Description (Arabic)') }}
                            </label>
                            <textarea class="form-control @error('description_ar') is-invalid @enderror"
                                      id="description_ar"
                                      name="description_ar"
                                      rows="4"
                                      required
                                      maxlength="2000"
                                      placeholder="{{ __('Describe your ad in detail in Arabic') }}"
                                      dir="rtl">{{ old('description_ar', $ad->description_ar) }}</textarea>
                            @error('description_ar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الوصف بالإنجليزية -->
                        <div class="mb-3">
                            <label for="description_en" class="form-label arabic-text">
                                <i class="fas fa-align-left me-1"></i>
                                {{ __('Description (English)') }}
                            </label>
                            <textarea class="form-control @error('description_en') is-invalid @enderror"
                                      id="description_en"
                                      name="description_en"
                                      rows="4"
                                      maxlength="2000"
                                      placeholder="{{ __('Describe your ad in detail in English (optional)') }}"
                                      dir="ltr">{{ old('description_en', $ad->description_en) }}</textarea>
                            @error('description_en')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الصورة -->
                        <div class="mb-3">
                            <label for="image" class="form-label arabic-text">
                                <i class="fas fa-image me-1"></i>
                                {{ __('Image') }}
                            </label>

                            @if($ad->image)
                                <div class="mb-2">
                                    <img src="{{ asset('storage/' . $ad->image) }}"
                                         alt="{{ $ad->title }}"
                                         class="img-thumbnail"
                                         style="max-width: 200px; max-height: 150px;">
                                    <p class="text-muted small mt-1 arabic-text">{{ __('Current image') }}</p>
                                </div>
                            @endif

                            <input type="file"
                                   class="form-control @error('image') is-invalid @enderror"
                                   id="image"
                                   name="image"
                                   accept="image/*">
                            <div class="form-text arabic-text">
                                {{ __('Upload a new image to replace the current one (optional). Max size: 2MB') }}
                            </div>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror

                            <!-- معاينة الصورة الجديدة -->
                            <div id="imagePreview" style="display: none;" class="mt-2">
                                <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                <button type="button" class="btn btn-sm btn-outline-danger mt-1" onclick="removeImage()">
                                    <i class="fas fa-times"></i> {{ __('Remove') }}
                                </button>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label arabic-text">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ __('Phone') }}
                                </label>
                                <input type="tel"
                                       class="form-control @error('phone') is-invalid @enderror"
                                       id="phone"
                                       name="phone"
                                       value="{{ old('phone', $ad->phone) }}"
                                       maxlength="20"
                                       placeholder="{{ __('Phone number') }}"
                                       dir="ltr">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label arabic-text">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ __('Email') }}
                                </label>
                                <input type="email"
                                       class="form-control @error('email') is-invalid @enderror"
                                       id="email"
                                       name="email"
                                       value="{{ old('email', $ad->email) }}"
                                       maxlength="255"
                                       placeholder="{{ __('Email address') }}"
                                       dir="ltr">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- الموقع -->
                        <div class="mb-3">
                            <label for="location" class="form-label arabic-text">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ __('Location') }}
                            </label>
                            <input type="text"
                                   class="form-control @error('location') is-invalid @enderror"
                                   id="location"
                                   name="location"
                                   value="{{ old('location', $ad->location) }}"
                                   maxlength="255"
                                   placeholder="{{ __('City or area') }}"
                                   dir="rtl">
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- تاريخ انتهاء الصلاحية -->
                        <div class="mb-4">
                            <label for="expires_at" class="form-label arabic-text">
                                <i class="fas fa-calendar-alt me-1"></i>
                                {{ __('Expiry Date') }}
                            </label>
                            <input type="date"
                                   class="form-control @error('expires_at') is-invalid @enderror"
                                   id="expires_at"
                                   name="expires_at"
                                   value="{{ old('expires_at', $ad->expires_at ? $ad->expires_at->format('Y-m-d') : '') }}"
                                   min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                            <div class="form-text arabic-text">
                                {{ __('Leave empty for no expiry date') }}
                            </div>
                            @error('expires_at')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('dashboard.ads.index') }}" class="btn btn-outline-secondary arabic-text">
                                <i class="fas fa-times me-2"></i>
                                {{ __('Cancel') }}
                            </a>
                            <button type="submit" class="btn btn-warning arabic-text">
                                <i class="fas fa-save me-2"></i>
                                {{ __('Update Ad') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة الصورة قبل الرفع
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImg').src = e.target.result;
            document.getElementById('imagePreview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

// إزالة الصورة
function removeImage() {
    document.getElementById('image').value = '';
    document.getElementById('imagePreview').style.display = 'none';
}
</script>
@endsection
