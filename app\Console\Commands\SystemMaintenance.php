<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheService;
use App\Services\NotificationService;
use App\Models\SecurityLog;
use App\Models\Review;
use App\Models\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

/**
 * أمر صيانة النظام الشاملة
 * ينظف البيانات القديمة ويحسن الأداء
 */
class SystemMaintenance extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'system:maintenance 
                            {--clean-logs : تنظيف السجلات القديمة}
                            {--clean-notifications : تنظيف الإشعارات القديمة}
                            {--clean-reviews : تنظيف التقييمات المعلقة القديمة}
                            {--clean-cache : مسح وإعادة بناء الكاش}
                            {--clean-files : تنظيف الملفات غير المستخدمة}
                            {--all : تشغيل جميع عمليات الصيانة}';

    /**
     * وصف الأمر
     */
    protected $description = 'تشغيل عمليات صيانة النظام الشاملة';

    /**
     * تنفيذ الأمر
     */
    public function handle()
    {
        $this->info('🔧 بدء عمليات صيانة النظام...');
        $this->newLine();

        $startTime = microtime(true);
        $operations = [];

        // تحديد العمليات المطلوبة
        if ($this->option('all')) {
            $operations = ['logs', 'notifications', 'reviews', 'cache', 'files'];
        } else {
            if ($this->option('clean-logs')) $operations[] = 'logs';
            if ($this->option('clean-notifications')) $operations[] = 'notifications';
            if ($this->option('clean-reviews')) $operations[] = 'reviews';
            if ($this->option('clean-cache')) $operations[] = 'cache';
            if ($this->option('clean-files')) $operations[] = 'files';
        }

        if (empty($operations)) {
            $this->warn('لم يتم تحديد أي عمليات صيانة. استخدم --all أو حدد عمليات محددة.');
            return 1;
        }

        // تنفيذ العمليات
        foreach ($operations as $operation) {
            $this->performOperation($operation);
        }

        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);

        $this->newLine();
        $this->info("✅ تمت عمليات الصيانة بنجاح في {$duration} ثانية");

        return 0;
    }

    /**
     * تنفيذ عملية صيانة محددة
     */
    private function performOperation(string $operation): void
    {
        switch ($operation) {
            case 'logs':
                $this->cleanSecurityLogs();
                break;
            case 'notifications':
                $this->cleanNotifications();
                break;
            case 'reviews':
                $this->cleanReviews();
                break;
            case 'cache':
                $this->cleanCache();
                break;
            case 'files':
                $this->cleanFiles();
                break;
        }
    }

    /**
     * تنظيف السجلات الأمنية القديمة
     */
    private function cleanSecurityLogs(): void
    {
        $this->info('🗂️  تنظيف السجلات الأمنية القديمة...');

        try {
            $deletedCount = SecurityLog::cleanOldLogs(90); // الاحتفاظ بـ 90 يوم
            $this->line("   تم حذف {$deletedCount} سجل أمني قديم");

        } catch (\Exception $e) {
            $this->error("   خطأ في تنظيف السجلات: " . $e->getMessage());
            Log::error('فشل في تنظيف السجلات الأمنية', ['error' => $e->getMessage()]);
        }
    }

    /**
     * تنظيف الإشعارات القديمة
     */
    private function cleanNotifications(): void
    {
        $this->info('🔔 تنظيف الإشعارات القديمة...');

        try {
            $deletedCount = NotificationService::cleanOldNotifications(90); // الاحتفاظ بـ 90 يوم
            $this->line("   تم حذف {$deletedCount} إشعار قديم");

        } catch (\Exception $e) {
            $this->error("   خطأ في تنظيف الإشعارات: " . $e->getMessage());
            Log::error('فشل في تنظيف الإشعارات', ['error' => $e->getMessage()]);
        }
    }

    /**
     * تنظيف التقييمات المعلقة القديمة
     */
    private function cleanReviews(): void
    {
        $this->info('⭐ تنظيف التقييمات المعلقة القديمة...');

        try {
            $deletedCount = Review::cleanOldPendingReviews(30); // الاحتفاظ بـ 30 يوم للمعلقة
            $this->line("   تم حذف {$deletedCount} تقييم معلق قديم");

        } catch (\Exception $e) {
            $this->error("   خطأ في تنظيف التقييمات: " . $e->getMessage());
            Log::error('فشل في تنظيف التقييمات', ['error' => $e->getMessage()]);
        }
    }

    /**
     * تنظيف وإعادة بناء الكاش
     */
    private function cleanCache(): void
    {
        $this->info('🗄️  تنظيف وإعادة بناء الكاش...');

        try {
            // مسح جميع أنواع الكاش
            CacheService::clearAllCache();
            $this->line("   تم مسح جميع أنواع الكاش");

            // إعادة تسخين الكاش
            CacheService::warmUpCache();
            $this->line("   تم إعادة تسخين الكاش");

        } catch (\Exception $e) {
            $this->error("   خطأ في إدارة الكاش: " . $e->getMessage());
            Log::error('فشل في إدارة الكاش', ['error' => $e->getMessage()]);
        }
    }

    /**
     * تنظيف الملفات غير المستخدمة
     */
    private function cleanFiles(): void
    {
        $this->info('📁 تنظيف الملفات غير المستخدمة...');

        try {
            $deletedCount = 0;

            // تنظيف ملفات الصور المؤقتة
            $tempFiles = Storage::disk('public')->files('temp');
            foreach ($tempFiles as $file) {
                if (Storage::disk('public')->lastModified($file) < now()->subDays(1)->timestamp) {
                    Storage::disk('public')->delete($file);
                    $deletedCount++;
                }
            }

            // تنظيف ملفات اللوج القديمة
            $logFiles = Storage::disk('local')->files('logs');
            foreach ($logFiles as $file) {
                if (Storage::disk('local')->lastModified($file) < now()->subDays(30)->timestamp) {
                    Storage::disk('local')->delete($file);
                    $deletedCount++;
                }
            }

            $this->line("   تم حذف {$deletedCount} ملف غير مستخدم");

        } catch (\Exception $e) {
            $this->error("   خطأ في تنظيف الملفات: " . $e->getMessage());
            Log::error('فشل في تنظيف الملفات', ['error' => $e->getMessage()]);
        }
    }
}
