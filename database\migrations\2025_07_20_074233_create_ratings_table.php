<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratings', function (Blueprint $table) {
            $table->id();

            // العلاقات الأساسية
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('ad_id')->constrained()->onDelete('cascade');

            // بيانات التقييم
            $table->tinyInteger('rating')->unsigned()->comment('التقييم من 1 إلى 5');
            $table->text('comment')->nullable()->comment('تعليق اختياري على التقييم');

            // معلومات إضافية
            $table->boolean('is_verified')->default(false)->comment('هل التقييم موثق');
            $table->boolean('is_helpful')->default(false)->comment('هل التقييم مفيد');
            $table->integer('helpful_count')->default(0)->comment('عدد الأشخاص الذين وجدوا التقييم مفيد');

            // معلومات الجودة
            $table->enum('quality_rating', ['excellent', 'good', 'average', 'poor'])->nullable()->comment('تقييم الجودة');
            $table->enum('price_rating', ['excellent', 'good', 'average', 'poor'])->nullable()->comment('تقييم السعر');
            $table->enum('service_rating', ['excellent', 'good', 'average', 'poor'])->nullable()->comment('تقييم الخدمة');

            // معلومات التفاعل
            $table->json('interaction_data')->nullable()->comment('بيانات التفاعل مع الإعلان');
            $table->timestamp('last_updated_at')->nullable()->comment('آخر تحديث للتقييم');

            $table->timestamps();

            // فهارس لتحسين الأداء
            $table->index(['ad_id', 'rating']);
            $table->index(['user_id', 'created_at']);
            $table->index(['rating', 'is_verified']);

            // منع التقييم المكرر من نفس المستخدم لنفس الإعلان
            $table->unique(['user_id', 'ad_id'], 'unique_user_ad_rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratings');
    }
};
