[2025-07-27 00:41:28] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"logs.view","url":"http://127.0.0.1:8000/logs/view"} 
[2025-07-27 00:41:34] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"home","url":"http://127.0.0.1:8000"} 
[2025-07-27 00:41:35] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:41:35] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:41:35] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:41:38] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"ads.index","url":"http://127.0.0.1:8000/ads"} 
[2025-07-27 00:41:39] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:41:39] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:41:39] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:41:52] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites"} 
[2025-07-27 00:41:52] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites","is_admin":true,"require_admin":null} 
[2025-07-27 00:41:52] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.favorites.index"} 
[2025-07-27 00:41:53] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:41:53] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:41:53] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:49:42] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites"} 
[2025-07-27 00:49:42] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites","is_admin":true,"require_admin":null} 
[2025-07-27 00:49:42] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.favorites.index"} 
[2025-07-27 00:49:43] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:49:43] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:49:43] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:50:26] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites"} 
[2025-07-27 00:50:26] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites","is_admin":true,"require_admin":null} 
[2025-07-27 00:50:26] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.favorites.index"} 
[2025-07-27 00:50:28] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:50:28] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:50:28] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:52:59] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites"} 
[2025-07-27 00:52:59] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites","is_admin":true,"require_admin":null} 
[2025-07-27 00:52:59] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.favorites.index"} 
[2025-07-27 00:53:00] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:53:00] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:53:00] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:53:11] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"ads.index","url":"http://127.0.0.1:8000/ads"} 
[2025-07-27 00:53:12] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:53:12] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:53:12] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:53:21] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"ads.index","url":"http://127.0.0.1:8000/ads"} 
[2025-07-27 00:53:22] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:53:22] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:53:22] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:53:53] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"ads.index","url":"http://127.0.0.1:8000/ads"} 
[2025-07-27 00:53:54] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:53:54] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:53:54] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:54:20] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"ads.index","url":"http://127.0.0.1:8000/ads"} 
[2025-07-27 00:54:21] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:54:21] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:54:21] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:54:32] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.toggle","url":"http://127.0.0.1:8000/dashboard/api/favorites/toggle"} 
[2025-07-27 00:54:32] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.toggle","url":"http://127.0.0.1:8000/dashboard/api/favorites/toggle","is_admin":true,"require_admin":null} 
[2025-07-27 00:54:32] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.toggle"} 
[2025-07-27 00:54:35] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.toggle","url":"http://127.0.0.1:8000/dashboard/api/favorites/toggle"} 
[2025-07-27 00:54:35] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.toggle","url":"http://127.0.0.1:8000/dashboard/api/favorites/toggle","is_admin":true,"require_admin":null} 
[2025-07-27 00:54:35] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.toggle"} 
[2025-07-27 00:54:35] production.INFO: Cache cleared: user_stats_1  
[2025-07-27 00:54:35] production.INFO: User cache cleared for user: 1  
[2025-07-27 00:55:02] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites"} 
[2025-07-27 00:55:02] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.favorites.index","url":"http://127.0.0.1:8000/dashboard/favorites","is_admin":true,"require_admin":null} 
[2025-07-27 00:55:02] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.favorites.index"} 
[2025-07-27 00:55:03] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:55:03] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:55:03] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:55:44] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"categories.index","url":"http://127.0.0.1:8000/categories"} 
[2025-07-27 00:55:49] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"home","url":"http://127.0.0.1:8000"} 
[2025-07-27 00:55:50] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:55:50] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:55:50] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:55:59] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.toggle","url":"http://127.0.0.1:8000/dashboard/api/favorites/toggle"} 
[2025-07-27 00:55:59] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.toggle","url":"http://127.0.0.1:8000/dashboard/api/favorites/toggle","is_admin":true,"require_admin":null} 
[2025-07-27 00:55:59] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.toggle"} 
[2025-07-27 00:55:59] production.INFO: Cache cleared: user_stats_1  
[2025-07-27 00:55:59] production.INFO: User cache cleared for user: 1  
[2025-07-27 00:58:56] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"home","url":"http://127.0.0.1:8000"} 
[2025-07-27 00:58:57] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:58:57] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:58:57] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:59:21] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"home","url":"http://127.0.0.1:8000"} 
[2025-07-27 00:59:23] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:59:23] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:59:23] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:59:33] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"ads.index","url":"http://127.0.0.1:8000/ads"} 
[2025-07-27 00:59:34] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:59:34] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:59:34] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:59:38] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"home","url":"http://127.0.0.1:8000"} 
[2025-07-27 00:59:39] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:59:39] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:59:39] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 00:59:54] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"home","url":"http://127.0.0.1:8000"} 
[2025-07-27 00:59:55] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 00:59:55] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 00:59:55] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 01:00:07] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"home","url":"http://127.0.0.1:8000"} 
[2025-07-27 01:00:09] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple"} 
[2025-07-27 01:00:09] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.check-multiple","url":"http://127.0.0.1:8000/dashboard/api/favorites/check-multiple","is_admin":true,"require_admin":null} 
[2025-07-27 01:00:09] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.check-multiple"} 
[2025-07-27 01:01:52] production.INFO: SetLocale: تم تطبيق اللغة {"locale":"ar","route":"dashboard.api.favorites.toggle","url":"http://127.0.0.1:8000/dashboard/api/favorites/toggle"} 
[2025-07-27 01:01:52] production.INFO: CheckAccountSecurity: بدء فحص المستخدم {"user_id":1,"user_email":"<EMAIL>","route":"dashboard.api.favorites.toggle","url":"http://127.0.0.1:8000/dashboard/api/favorites/toggle","is_admin":true,"require_admin":null} 
[2025-07-27 01:01:52] production.INFO: CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح {"user_id":1,"route":"dashboard.api.favorites.toggle"} 
