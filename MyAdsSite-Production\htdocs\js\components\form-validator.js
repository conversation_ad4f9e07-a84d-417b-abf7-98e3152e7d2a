/**
 * مدقق النماذج المحسن
 * ملف مبسط للتحقق من صحة النماذج
 */

class FormValidator {
    constructor() {
        this.rules = {
            email: {
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'يرجى إدخال بريد إلكتروني صحيح'
            },
            phone: {
                pattern: /^[\d\s\-\+\(\)]+$/,
                minLength: 8,
                message: 'يرجى إدخال رقم هاتف صحيح'
            },
            password: {
                minLength: 8,
                message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
            }
        };
        
        this.init();
    }

    /**
     * تهيئة المدقق
     */
    init() {
        this.bindEvents();
        this.enhanceFormFields();
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // استخدام event delegation للأداء الأفضل
        document.addEventListener('input', this.handleInput.bind(this));
        document.addEventListener('focus', this.handleFocus.bind(this), true);
        document.addEventListener('blur', this.handleBlur.bind(this), true);
    }

    /**
     * معالجة الإدخال
     */
    handleInput(event) {
        const field = event.target;
        if (field.matches('.form-control[data-validate]')) {
            this.validateField(field);
        }
    }

    /**
     * معالجة التركيز
     */
    handleFocus(event) {
        const field = event.target;
        if (field.matches('.form-control')) {
            field.parentElement?.classList.add('focused');
        }
    }

    /**
     * معالجة فقدان التركيز
     */
    handleBlur(event) {
        const field = event.target;
        if (field.matches('.form-control') && !field.value.trim()) {
            field.parentElement?.classList.remove('focused');
        }
    }

    /**
     * التحقق من صحة الحقل
     */
    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const rule = this.rules[type];

        if (!rule) return true;

        const isValid = this.checkRule(value, rule);
        this.updateFieldState(field, isValid, rule.message);
        
        return isValid;
    }

    /**
     * فحص القاعدة
     */
    checkRule(value, rule) {
        if (rule.pattern && !rule.pattern.test(value)) {
            return false;
        }
        
        if (rule.minLength && value.length < rule.minLength) {
            return false;
        }
        
        return true;
    }

    /**
     * تحديث حالة الحقل
     */
    updateFieldState(field, isValid, message) {
        // إزالة الحالات السابقة
        field.classList.remove('is-valid', 'is-invalid');
        
        // إضافة الحالة الجديدة
        field.classList.add(isValid ? 'is-valid' : 'is-invalid');
        
        // تحديث رسالة الخطأ
        const feedback = field.parentElement?.querySelector('.invalid-feedback');
        if (feedback && !isValid) {
            feedback.textContent = message;
        }
    }

    /**
     * تحسين حقول النماذج
     */
    enhanceFormFields() {
        const fields = document.querySelectorAll('.form-control');
        fields.forEach(field => {
            if (field.value.trim()) {
                field.parentElement?.classList.add('focused');
            }
        });
    }

    /**
     * التحقق من صحة النموذج كاملاً
     */
    validateForm(form) {
        const fields = form.querySelectorAll('.form-control[data-validate]');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }
}

export default FormValidator;
