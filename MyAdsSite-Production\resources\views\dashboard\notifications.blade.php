@extends('layouts.app')

@section('title', __('Notifications'))

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 arabic-text">
                        <i class="fas fa-bell me-2"></i>
                        {{ __('Notifications') }}
                    </h1>
                    <p class="text-muted mb-0">{{ __('Manage your notifications') }}</p>
                </div>

                <!-- Back Button -->
                <x-back-button :url="route('dashboard.index')" text="{{ __('Back to Dashboard') }}" />
            </div>

            <!-- Notification Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <a href="{{ route('dashboard.notifications.index', ['type' => 'all']) }}"
                                   class="btn btn-sm {{ $type === 'all' ? 'btn-primary' : 'btn-outline-primary' }}">
                                    <i class="fas fa-list me-1"></i>
                                    {{ __('All') }}
                                </a>
                                <a href="{{ route('dashboard.notifications.index', ['type' => 'unread']) }}"
                                   class="btn btn-sm {{ $type === 'unread' ? 'btn-primary' : 'btn-outline-primary' }}">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ __('Unread') }}
                                    @if($unreadCount > 0)
                                        <span class="badge bg-danger ms-1">{{ $unreadCount }}</span>
                                    @endif
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6 text-end">
                            <div class="d-flex justify-content-end align-items-center gap-2">
                                <!-- View Toggle -->
                                <div class="btn-group me-3" role="group" id="viewToggle">
                                    <button type="button" class="btn btn-sm btn-outline-secondary active" data-view="list">
                                        <i class="fas fa-list"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" data-view="grid">
                                        <i class="fas fa-th"></i>
                                    </button>
                                </div>

                                @if($unreadCount > 0)
                                    <button type="button" class="btn btn-sm btn-success" id="markAllRead">
                                        <i class="fas fa-check-double me-1"></i>
                                        {{ __('Mark All as Read') }}
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="card">
                <div class="card-body p-0">
                    <!-- List View -->
                    <div id="listView">
                        @forelse($notifications as $notification)
                            <div class="notification-item border-bottom p-3 {{ $notification->isRead() ? '' : 'bg-light' }}"
                                 data-notification-id="{{ $notification->id }}"
                                 data-details-url="{{ route('dashboard.notifications.show', $notification->id) }}">
                            <div class="d-flex">
                                <!-- Icon -->
                                <div class="flex-shrink-0 me-3">
                                    <div class="notification-icon">
                                        <i class="{{ $notification->type_icon }} text-{{ $notification->type_color }} fs-4"></i>
                                    </div>
                                </div>

                                <!-- Content -->
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1 fw-bold">{{ $notification->title }}</h6>
                                            <p class="mb-2 text-muted">{{ $notification->message }}</p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                {{ \Carbon\Carbon::parse($notification->created_at)->diffForHumans() }}
                                            </small>
                                        </div>

                                        <!-- Actions -->
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                    type="button"
                                                    aria-expanded="false">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                @if(!$notification->isRead())
                                                    <li>
                                                        <button class="dropdown-item mark-read-btn"
                                                                data-notification-id="{{ $notification->id }}">
                                                            <i class="fas fa-check me-2"></i>
                                                            {{ __('Mark as Read') }}
                                                        </button>
                                                    </li>
                                                @endif
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('dashboard.notifications.show', $notification->id) }}">
                                                        <i class="fas fa-eye me-2"></i>
                                                        {{ __('View Details') }}
                                                    </a>
                                                </li>
                                                @if($notification->action_url)
                                                    <li>
                                                        <a class="dropdown-item" href="{{ $notification->action_url }}" target="_blank">
                                                            <i class="fas fa-external-link-alt me-2"></i>
                                                            {{ __('Go to Related Page') }}
                                                        </a>
                                                    </li>
                                                @endif
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <button class="dropdown-item text-danger delete-notification-btn"
                                                            data-notification-id="{{ $notification->id }}">
                                                        <i class="fas fa-trash me-2"></i>
                                                        {{ __('Delete') }}
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Unread Indicator -->
                                    @if(!$notification->isRead())
                                        <div class="position-absolute top-0 end-0 mt-2 me-2">
                                            <span class="badge bg-primary rounded-pill"></span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @empty
                            <div class="text-center py-5">
                                <div class="empty-state">
                                    <i class="fas fa-bell-slash text-muted mb-3" style="font-size: 3rem;"></i>
                                    <h5 class="text-muted">{{ __('No notifications found') }}</h5>
                                    <p class="text-muted">
                                        @if($type === 'unread')
                                            {{ __('You have no unread notifications') }}
                                        @else
                                            {{ __('You have no notifications yet') }}
                                        @endif
                                    </p>
                                </div>
                            </div>
                        @endforelse
                    </div>

                    <!-- Grid View -->
                    <div id="gridView" style="display: none;">
                        <div class="row p-3">
                            @forelse($notifications as $notification)
                                <div class="col-lg-6 col-xl-4 mb-3">
                                    <div class="card notification-card h-100 {{ $notification->isRead() ? '' : 'border-primary' }}"
                                         data-notification-id="{{ $notification->id }}"
                                         data-details-url="{{ route('dashboard.notifications.show', $notification->id) }}">
                                        <div class="card-body">
                                            <div class="d-flex align-items-start">
                                                <!-- Icon -->
                                                <div class="notification-icon-small me-3">
                                                    <i class="{{ $notification->type_icon }} text-{{ $notification->type_color }}"></i>
                                                </div>

                                                <!-- Content -->
                                                <div class="flex-grow-1">
                                                    <h6 class="card-title mb-2">{{ Str::limit($notification->title, 40) }}</h6>
                                                    <p class="card-text text-muted small mb-2">
                                                        {{ Str::limit($notification->message, 80) }}
                                                    </p>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        {{ \Carbon\Carbon::parse($notification->created_at)->diffForHumans() }}
                                                    </small>
                                                </div>

                                                <!-- Status -->
                                                @if(!$notification->isRead())
                                                    <div class="position-absolute top-0 end-0 m-2">
                                                        <span class="badge bg-primary rounded-pill"></span>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>

                                        <div class="card-footer bg-light border-top-0 p-2">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge bg-{{ $notification->type_color }} small">
                                                    {{ ucfirst($notification->type) }}
                                                </span>

                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                            type="button"
                                                            aria-expanded="false">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-end">
                                                        @if(!$notification->isRead())
                                                            <li>
                                                                <button class="dropdown-item mark-read-btn"
                                                                        data-notification-id="{{ $notification->id }}">
                                                                    <i class="fas fa-check me-2"></i>
                                                                    {{ __('Mark as Read') }}
                                                                </button>
                                                            </li>
                                                        @endif
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('dashboard.notifications.show', $notification->id) }}">
                                                                <i class="fas fa-eye me-2"></i>
                                                                {{ __('View Details') }}
                                                            </a>
                                                        </li>
                                                        @if($notification->action_url)
                                                            <li>
                                                                <a class="dropdown-item" href="{{ $notification->action_url }}" target="_blank">
                                                                    <i class="fas fa-external-link-alt me-2"></i>
                                                                    {{ __('Go to Related Page') }}
                                                                </a>
                                                            </li>
                                                        @endif
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <button class="dropdown-item text-danger delete-notification-btn"
                                                                    data-notification-id="{{ $notification->id }}">
                                                                <i class="fas fa-trash me-2"></i>
                                                                {{ __('Delete') }}
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="col-12">
                                    <div class="text-center py-5">
                                        <div class="empty-state">
                                            <i class="fas fa-bell-slash text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h5 class="text-muted">{{ __('No notifications found') }}</h5>
                                            <p class="text-muted">
                                                @if($type === 'unread')
                                                    {{ __('You have no unread notifications') }}
                                                @else
                                                    {{ __('You have no notifications yet') }}
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if($notifications->hasPages())
                    <div class="card-footer bg-light">
                        {{ $notifications->appends(request()->query())->links('pagination.custom') }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.notification-item {
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa !important;
    transform: translateX(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notification-item[data-details-url]:hover {
    cursor: pointer;
}

.notification-item[data-details-url]:hover::after {
    content: "انقر لعرض التفاصيل";
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    opacity: 0.9;
}

.notification-item.bg-light {
    border-left: 4px solid var(--primary-color);
}

.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.notification-icon-small {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    font-size: 0.9rem;
}

.notification-card {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-card.border-primary {
    border-left: 4px solid var(--bs-primary) !important;
}

/* View Toggle Buttons */
#viewToggle .btn.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

/* Fix dropdown z-index issues */
.notification-card .dropdown-menu {
    z-index: 1050 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-item .dropdown-menu {
    z-index: 1050 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Ensure dropdowns appear above cards */
.dropdown {
    position: relative;
    z-index: 10;
}

.dropdown.show {
    z-index: 1050;
}

/* Fix for grid view cards */
.notification-card {
    position: relative;
    z-index: 1;
}

.notification-card:hover {
    z-index: 2;
}

.notification-card .dropdown.show {
    z-index: 1051;
}

/* Additional dropdown fixes */
.dropdown-menu {
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.375rem;
    background-color: #fff;
    min-width: 160px;
}

.dropdown-menu.show {
    display: block !important;
    z-index: 1050 !important;
    position: absolute !important;
}

/* Ensure dropdown appears above everything */
.notification-item .dropdown,
.notification-card .dropdown {
    position: relative;
}

.notification-item .dropdown.show,
.notification-card .dropdown.show {
    z-index: 1055 !important;
}

.notification-item .dropdown-menu,
.notification-card .dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: auto !important;
    right: 0 !important;
    z-index: 1055 !important;
    transform: none !important;
    transition: opacity 0.15s ease-in-out;
}

/* تحسين مظهر القوائم المنسدلة */
.dropdown-menu {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.15s ease-in-out;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* تحسين زر القائمة المنسدلة */
.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.dropdown-toggle[aria-expanded="true"] {
    background-color: #e9ecef;
    border-color: #adb5bd;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View Toggle Functionality
    const viewToggleButtons = document.querySelectorAll('#viewToggle button');
    const listView = document.getElementById('listView');
    const gridView = document.getElementById('gridView');

    viewToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.dataset.view;

            // Update button states
            viewToggleButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Toggle views
            if (view === 'list') {
                listView.style.display = 'block';
                gridView.style.display = 'none';
                localStorage.setItem('notifications_view', 'list');
            } else {
                listView.style.display = 'none';
                gridView.style.display = 'block';
                localStorage.setItem('notifications_view', 'grid');
            }
        });
    });

    // Restore saved view preference
    const savedView = localStorage.getItem('notifications_view') || 'list';
    const savedViewButton = document.querySelector(`#viewToggle button[data-view="${savedView}"]`);
    if (savedViewButton) {
        savedViewButton.click();
    }
    // Click on notification to view details (both list and grid)
    document.querySelectorAll('.notification-item, .notification-card').forEach(item => {
        item.addEventListener('click', function(e) {
            // Don't trigger if clicking on dropdown or buttons
            if (e.target.closest('.dropdown') || e.target.closest('button') || e.target.closest('a')) {
                return;
            }

            const detailsUrl = this.dataset.detailsUrl;
            if (detailsUrl) {
                window.location.href = detailsUrl;
            }
        });
    });

    // إدارة القوائم المنسدلة - إغلاق القوائم الأخرى عند فتح قائمة جديدة
    function closeAllDropdowns() {
        document.querySelectorAll('.dropdown').forEach(dropdown => {
            const menu = dropdown.querySelector('.dropdown-menu');
            const toggle = dropdown.querySelector('.dropdown-toggle');

            dropdown.classList.remove('show');
            if (menu) menu.classList.remove('show');
            if (toggle) toggle.setAttribute('aria-expanded', 'false');
        });
    }

    // إضافة مستمع للنقر على أزرار القوائم المنسدلة
    document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.stopPropagation();

            const currentDropdown = this.closest('.dropdown');
            const isCurrentlyOpen = currentDropdown.classList.contains('show');

            // إغلاق جميع القوائم المنسدلة
            closeAllDropdowns();

            // إذا لم تكن القائمة الحالية مفتوحة، افتحها
            if (!isCurrentlyOpen) {
                currentDropdown.classList.add('show');
                const menu = currentDropdown.querySelector('.dropdown-menu');
                if (menu) menu.classList.add('show');
                this.setAttribute('aria-expanded', 'true');
            }
        });
    });

    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            closeAllDropdowns();
        }
    });

    // منع إغلاق القائمة عند النقر على عناصرها
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        menu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    // Mark single notification as read
    document.querySelectorAll('.mark-read-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const notificationId = this.dataset.notificationId;
            markNotificationAsRead(notificationId);
        });
    });

    // Mark all notifications as read
    document.getElementById('markAllRead')?.addEventListener('click', function() {
        if (confirm('{{ __("Are you sure you want to mark all notifications as read?") }}')) {
            markAllNotificationsAsRead();
        }
    });

    // Delete notification
    document.querySelectorAll('.delete-notification-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const notificationId = this.dataset.notificationId;
            if (confirm('{{ __("Are you sure you want to delete this notification?") }}')) {
                deleteNotification(notificationId);
            }
        });
    });
});

function markNotificationAsRead(notificationId) {
    fetch(`/dashboard/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '{{ __("Failed to mark notification as read") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("An error occurred") }}');
    });
}

function markAllNotificationsAsRead() {
    fetch('/dashboard/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '{{ __("Failed to mark all notifications as read") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("An error occurred") }}');
    });
}

function deleteNotification(notificationId) {
    fetch(`/dashboard/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '{{ __("Failed to delete notification") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("An error occurred") }}');
    });
}
</script>
@endpush
