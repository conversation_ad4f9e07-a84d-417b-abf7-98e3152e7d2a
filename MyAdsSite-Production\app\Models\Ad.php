<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use App\Services\CacheService;
use App\Services\EncryptionService;

/**
 * نموذج الإعلانات
 * يحتوي على جميع الإعلانات المنشورة في الموقع
 */
class Ad extends Model
{
    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',        // معرف المستخدم الذي أنشأ الإعلان
        'title_ar',       // العنوان بالعربية
        'title_en',       // العنوان بالإنجليزية
        'description_ar', // الوصف بالعربية
        'description_en', // الوصف بالإنجليزية
        'image',          // مسار الصورة
        'category_id',    // معرف التصنيف
        'status',         // حالة الإعلان
        'phone',          // رقم الهاتف
        'email',          // البريد الإلكتروني
        'location',       // الموقع أو المدينة
        'expires_at',     // تاريخ انتهاء الصلاحية
        'slug',           // الرابط المختصر
        'views_count',    // عدد المشاهدات
        'is_featured',    // هل الإعلان مميز
        'rejection_reason', // سبب الرفض
        // حقول الأسعار المتقدمة
        'price',          // السعر الحالي
        'original_price', // السعر الأصلي قبل الخصم
        'discount_percentage', // نسبة الخصم
        'discount_amount', // مبلغ الخصم
        'is_negotiable',  // هل السعر قابل للتفاوض
        'is_free',        // هل الإعلان مجاني
        'currency',       // نوع العملة
        'price_type',     // نوع السعر
        'discount_expires_at', // تاريخ انتهاء الخصم
        'is_limited_offer', // هل هو عرض محدود
        'price_notes',    // ملاحظات إضافية على السعر
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'expires_at' => 'date',           // تحويل إلى تاريخ
        'views_count' => 'integer',       // تحويل إلى رقم صحيح
        'is_featured' => 'boolean',       // تحويل إلى boolean
        // حقول الأسعار المتقدمة
        'price' => 'decimal:2',           // السعر الحالي
        'original_price' => 'decimal:2',  // السعر الأصلي
        'discount_percentage' => 'decimal:2', // نسبة الخصم
        'discount_amount' => 'decimal:2', // مبلغ الخصم
        'is_negotiable' => 'boolean',     // قابل للتفاوض
        'is_free' => 'boolean',           // مجاني
        'discount_expires_at' => 'date',  // تاريخ انتهاء الخصم
        'is_limited_offer' => 'boolean',  // عرض محدود
    ];

    /**
     * الأحداث التي تحدث عند إنشاء إعلان جديد
     */
    protected static function boot()
    {
        parent::boot();

        // إنشاء slug تلقائياً عند إنشاء إعلان جديد
        static::creating(function ($ad) {
            if (empty($ad->slug)) {
                $ad->slug = Str::slug($ad->title_en ?: $ad->title_ar) . '-' . time();
            }
        });

        // مسح الكاش عند إنشاء أو تحديث أو حذف إعلان
        static::created(function ($ad) {
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);
        });

        static::updated(function ($ad) {
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);
        });

        static::deleted(function ($ad) {
            CacheService::clearAdsCache();
            CacheService::clearUserCache($ad->user_id);
        });
    }

    /**
     * علاقة متعدد إلى واحد مع التصنيفات
     * إعلان واحد ينتمي إلى تصنيف واحد
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * علاقة متعدد إلى واحد مع المستخدمين
     * إعلان واحد ينتمي إلى مستخدم واحد
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * الحصول على عنوان الإعلان حسب اللغة الحالية
     */
    public function getTitleAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->title_ar : $this->title_en;
    }

    /**
     * الحصول على وصف الإعلان حسب اللغة الحالية
     */
    public function getDescriptionAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : $this->description_en;
    }

    /**
     * البحث في الإعلانات النشطة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * البحث في الإعلانات غير المنتهية الصلاحية
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * البحث في الإعلانات المعلقة (في انتظار الموافقة)
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * البحث في الإعلانات المميزة
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * البحث في إعلانات مستخدم معين
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * زيادة عدد المشاهدات
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * تشفير رقم الهاتف عند الحفظ
     */
    public function setPhoneAttribute($value)
    {
        $this->attributes['phone'] = EncryptionService::encryptPhone($value);
    }

    /**
     * فك تشفير رقم الهاتف عند الاستدعاء
     */
    public function getPhoneAttribute($value)
    {
        return EncryptionService::decryptPhone($value);
    }

    /**
     * تشفير البريد الإلكتروني عند الحفظ
     */
    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = EncryptionService::encryptEmail($value);
    }

    /**
     * فك تشفير البريد الإلكتروني عند الاستدعاء
     */
    public function getEmailAttribute($value)
    {
        return EncryptionService::decryptEmail($value);
    }

    /**
     * الحصول على رقم الهاتف مخفي للعرض
     */
    public function getMaskedPhoneAttribute()
    {
        return EncryptionService::maskPhone($this->phone);
    }

    /**
     * الحصول على البريد الإلكتروني مخفي للعرض
     */
    public function getMaskedEmailAttribute()
    {
        return EncryptionService::maskEmail($this->email);
    }

    /**
     * علاقة التقييمات (polymorphic)
     */
    public function reviews()
    {
        return $this->morphMany(Review::class, 'reviewable');
    }

    /**
     * التقييمات المعتمدة فقط
     */
    public function approvedReviews()
    {
        return $this->reviews()->approved();
    }

    /**
     * الحصول على متوسط التقييم
     */
    public function getAverageRatingAttribute(): float
    {
        return Review::getAverageRating($this);
    }

    /**
     * الحصول على عدد التقييمات
     */
    public function getReviewsCountAttribute(): int
    {
        return Review::getReviewsCount($this);
    }

    /**
     * الحصول على توزيع التقييمات
     */
    public function getRatingDistributionAttribute(): array
    {
        return Review::getRatingDistribution($this);
    }

    /**
     * التحقق من وجود تقييم من مستخدم معين
     */
    public function hasUserReviewed(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Review::hasUserReviewed($userId, $this) : false;
    }

    /**
     * الحصول على تقييم المستخدم
     */
    public function getUserReview(?int $userId = null): ?Review
    {
        $userId = $userId ?? auth()->id();
        return $userId ? Review::getUserReview($userId, $this) : null;
    }
}
