<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UserContact;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Services\ContactSecurityService;
use Exception;

/**
 * Controller لإدارة معلومات التواصل للمستخدمين
 */
class UserContactController extends Controller
{
    protected ContactSecurityService $securityService;

    public function __construct(ContactSecurityService $securityService)
    {
        $this->securityService = $securityService;
    }
    /**
     * عرض جميع معلومات التواصل للمستخدم الحالي
     */
    public function index(): JsonResponse
    {
        try {
            $user = Auth::user();
            Log::info('User contacts index request', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]);

            $contacts = $user->contacts()->ordered()->get();

            Log::info('User contacts retrieved successfully', [
                'user_id' => $user->id,
                'contacts_count' => $contacts->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => $contacts,
                'message' => 'تم جلب معلومات التواصل بنجاح'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to retrieve user contacts', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب معلومات التواصل'
            ], 500);
        }
    }

    /**
     * إنشاء معلومة تواصل جديدة
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            Log::info('Creating new user contact', [
                'user_id' => $user->id,
                'request_data' => $request->except(['_token']),
                'ip' => request()->ip()
            ]);

            $validator = Validator::make($request->all(), [
                'contact_type' => 'nullable|in:phone,whatsapp,email,telegram,instagram,facebook,twitter,linkedin,website,other',
                'contact_value' => 'required|string|max:255',
                'icon_type' => 'nullable|string|max:50',
                'display_label' => 'nullable|string|max:100',
                'is_primary' => 'boolean',
                'is_public' => 'boolean',
                'privacy_level' => 'required|in:public,registered_users,private',
                'display_order' => 'integer|min:0',
            ]);

            if ($validator->fails()) {
                Log::warning('User contact creation validation failed', [
                    'user_id' => $user->id,
                    'errors' => $validator->errors()->toArray()
                ]);

                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => 'بيانات غير صحيحة'
                ], 422);
            }

            // التحقق من عدم وجود تكرار
            $exists = UserContact::where('user_id', $user->id)
                                ->where('contact_type', $request->contact_type)
                                ->where('contact_value', $request->contact_value)
                                ->exists();

            if ($exists) {
                Log::warning('Duplicate contact attempt', [
                    'user_id' => $user->id,
                    'contact_type' => $request->contact_type,
                    'contact_value' => $request->contact_value
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'معلومة التواصل هذه موجودة بالفعل'
                ], 409);
            }

            // إذا كانت أساسية، إلغاء الأساسية من النوع نفسه
            if ($request->is_primary && $request->contact_type) {
                UserContact::where('user_id', $user->id)
                          ->where('contact_type', $request->contact_type)
                          ->update(['is_primary' => false]);

                Log::info('Updated primary status for existing contacts', [
                    'user_id' => $user->id,
                    'contact_type' => $request->contact_type
                ]);
            }

            $contact = UserContact::create([
                'user_id' => $user->id,
                'contact_type' => $request->contact_type,
                'contact_value' => $request->contact_value,
                'icon_type' => $request->icon_type,
                'display_label' => $request->display_label,
                'is_primary' => $request->boolean('is_primary', false),
                'is_public' => $request->boolean('is_public', true),
                'is_verified' => false, // يتم التحقق لاحقاً
                'privacy_level' => $request->privacy_level ?? 'public',
                'display_order' => $request->display_order ?? 0,
            ]);

            Log::info('User contact created successfully', [
                'user_id' => $user->id,
                'contact_id' => $contact->id,
                'contact_type' => $contact->contact_type
            ]);

            return response()->json([
                'success' => true,
                'data' => $contact,
                'message' => 'تم إضافة معلومة التواصل بنجاح'
            ], 201);

        } catch (Exception $e) {
            Log::error('Failed to create user contact', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->except(['_token'])
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إضافة معلومة التواصل'
            ], 500);
        }
    }

    /**
     * عرض معلومة تواصل محددة
     */
    public function show(UserContact $contact): JsonResponse
    {
        // التأكد من أن المعلومة تخص المستخدم الحالي
        if ($contact->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بالوصول لهذه المعلومة'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $contact,
            'message' => 'تم جلب معلومة التواصل بنجاح'
        ]);
    }

    /**
     * تحديث معلومة تواصل
     */
    public function update(Request $request, UserContact $contact): JsonResponse
    {
        // التأكد من أن المعلومة تخص المستخدم الحالي
        if ($contact->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بتعديل هذه المعلومة'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'contact_type' => 'nullable|in:phone,whatsapp,email,telegram,instagram,facebook,twitter,linkedin,website,other',
            'contact_value' => 'required|string|max:255',
            'icon_type' => 'nullable|string|max:50',
            'display_label' => 'nullable|string|max:100',
            'is_primary' => 'boolean',
            'is_public' => 'boolean',
            'privacy_level' => 'required|in:public,registered_users,private',
            'display_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'بيانات غير صحيحة'
            ], 422);
        }

        // التحقق من عدم وجود تكرار (باستثناء السجل الحالي)
        $exists = UserContact::where('user_id', Auth::id())
                            ->where('contact_type', $request->contact_type)
                            ->where('contact_value', $request->contact_value)
                            ->where('id', '!=', $contact->id)
                            ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'معلومة التواصل هذه موجودة بالفعل'
            ], 409);
        }

        // إذا كانت أساسية، إلغاء الأساسية من النوع نفسه
        if ($request->is_primary && $request->contact_type) {
            UserContact::where('user_id', Auth::id())
                      ->where('contact_type', $request->contact_type)
                      ->where('id', '!=', $contact->id)
                      ->update(['is_primary' => false]);
        }

        $contact->update([
            'contact_type' => $request->contact_type,
            'contact_value' => $request->contact_value,
            'icon_type' => $request->icon_type,
            'display_label' => $request->display_label,
            'is_primary' => $request->boolean('is_primary', false),
            'is_public' => $request->boolean('is_public', true),
            'privacy_level' => $request->privacy_level ?? 'public',
            'display_order' => $request->display_order ?? 0,
        ]);

        return response()->json([
            'success' => true,
            'data' => $contact->fresh(),
            'message' => 'تم تحديث معلومة التواصل بنجاح'
        ]);
    }

    /**
     * حذف معلومة تواصل
     */
    public function destroy(UserContact $contact): JsonResponse
    {
        try {
            $user = Auth::user();

            // التأكد من أن المعلومة تخص المستخدم الحالي
            if ($contact->user_id !== $user->id) {
                Log::warning('Unauthorized contact deletion attempt', [
                    'user_id' => $user->id,
                    'contact_id' => $contact->id,
                    'contact_owner_id' => $contact->user_id,
                    'ip' => request()->ip()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بحذف هذه المعلومة'
                ], 403);
            }

            Log::info('Deleting user contact', [
                'user_id' => $user->id,
                'contact_id' => $contact->id,
                'contact_type' => $contact->contact_type,
                'contact_value' => $contact->contact_value
            ]);

            $contact->delete();

            Log::info('User contact deleted successfully', [
                'user_id' => $user->id,
                'contact_id' => $contact->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم حذف معلومة التواصل بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to delete user contact', [
                'user_id' => Auth::id(),
                'contact_id' => $contact->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حذف معلومة التواصل'
            ], 500);
        }
    }

    /**
     * تحديث ترتيب معلومات التواصل
     */
    public function updateOrder(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'contacts' => 'required|array',
            'contacts.*.id' => 'required|integer|exists:user_contacts,id',
            'contacts.*.display_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'بيانات غير صحيحة'
            ], 422);
        }

        foreach ($request->contacts as $contactData) {
            UserContact::where('id', $contactData['id'])
                      ->where('user_id', Auth::id())
                      ->update(['display_order' => $contactData['display_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث ترتيب معلومات التواصل بنجاح'
        ]);
    }

    /**
     * تبديل حالة العرض العام لمعلومة تواصل
     */
    public function togglePublic(UserContact $contact): JsonResponse
    {
        try {
            $user = Auth::user();

            // التأكد من أن المعلومة تخص المستخدم الحالي
            if ($contact->user_id !== $user->id) {
                Log::warning('Unauthorized contact toggle public attempt', [
                    'user_id' => $user->id,
                    'contact_id' => $contact->id,
                    'contact_owner_id' => $contact->user_id,
                    'ip' => request()->ip()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بتعديل هذه المعلومة'
                ], 403);
            }

            $oldStatus = $contact->is_public;
            $newStatus = !$oldStatus;

            Log::info('Toggling contact public status', [
                'user_id' => $user->id,
                'contact_id' => $contact->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);

            $contact->update(['is_public' => $newStatus]);

            Log::info('Contact public status toggled successfully', [
                'user_id' => $user->id,
                'contact_id' => $contact->id,
                'new_status' => $newStatus
            ]);

            return response()->json([
                'success' => true,
                'data' => $contact->fresh(),
                'message' => 'تم تحديث حالة العرض بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to toggle contact public status', [
                'user_id' => Auth::id(),
                'contact_id' => $contact->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تحديث حالة العرض'
            ], 500);
        }
    }

    /**
     * تبديل حالة الأساسية لمعلومة تواصل
     */
    public function togglePrimary(UserContact $contact): JsonResponse
    {
        // التأكد من أن المعلومة تخص المستخدم الحالي
        if ($contact->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بتعديل هذه المعلومة'
            ], 403);
        }

        if (!$contact->is_primary) {
            // إلغاء الأساسية من النوع نفسه
            if ($contact->contact_type) {
                UserContact::where('user_id', Auth::id())
                          ->where('contact_type', $contact->contact_type)
                          ->where('id', '!=', $contact->id)
                          ->update(['is_primary' => false]);
            }
        }

        $contact->update(['is_primary' => !$contact->is_primary]);

        return response()->json([
            'success' => true,
            'data' => $contact->fresh(),
            'message' => 'تم تحديث حالة الأساسية بنجاح'
        ]);
    }

    /**
     * الحصول على أنواع التواصل المتاحة
     */
    public function getContactTypes(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => UserContact::CONTACT_TYPES,
            'message' => 'تم جلب أنواع التواصل بنجاح'
        ]);
    }

    /**
     * الحصول على مستويات الخصوصية المتاحة
     */
    public function getPrivacyLevels(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => UserContact::PRIVACY_LEVELS,
            'message' => 'تم جلب مستويات الخصوصية بنجاح'
        ]);
    }

    /**
     * كشف معلومة تواصل مخفية (للنظام المتقدم)
     */
    public function revealContact(Request $request, UserContact $contact): JsonResponse
    {
        try {
            $user = Auth::user();
            $adId = $request->get('ad_id');

            Log::info('Contact reveal request', [
                'contact_id' => $contact->id,
                'contact_owner_id' => $contact->user_id,
                'requester_user_id' => $user?->id,
                'ad_id' => $adId,
                'ip' => request()->ip()
            ]);

            // التحقق من إمكانية الوصول
            $accessCheck = $this->securityService->canAccessContacts($contact->user, $user);

            if (!$accessCheck['allowed']) {
                Log::warning('Contact reveal denied', [
                    'contact_id' => $contact->id,
                    'reason' => $accessCheck['reason'],
                    'code' => $accessCheck['code']
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $accessCheck['reason']
                ], 403);
            }

            // التحقق من إعدادات الخصوصية
            try {
                $privacySettings = $contact->user->privacySettings;

                if ($privacySettings && $privacySettings->require_verification_to_view && !$user) {
                    return response()->json([
                        'success' => false,
                        'message' => 'يتطلب تسجيل الدخول لعرض معلومات التواصل'
                    ], 401);
                }
            } catch (\Exception $e) {
                Log::warning('Error accessing privacy settings in reveal contact', [
                    'contact_id' => $contact->id,
                    'user_id' => $contact->user_id,
                    'error' => $e->getMessage()
                ]);

                // في حالة الخطأ، تطبيق سياسة أمان صارمة
                if (!$user) {
                    return response()->json([
                        'success' => false,
                        'message' => 'يتطلب تسجيل الدخول لعرض معلومات التواصل'
                    ], 401);
                }
            }

            // تسجيل الوصول
            $this->securityService->logContactAccess(
                $contact->user_id,
                'reveal',
                $contact->id,
                $user?->id,
                [
                    'ad_id' => $adId,
                    'contact_type' => $contact->contact_type,
                    'user_agent' => request()->userAgent()
                ]
            );

            // إرجاع معلومة التواصل
            return response()->json([
                'success' => true,
                'contact_id' => $contact->id,
                'contact_type' => $contact->contact_type,
                'contact_value' => $contact->contact_value,
                'display_label' => $contact->display_label,
                'is_primary' => $contact->is_primary,
                'is_verified' => $contact->is_verified,
                'message' => 'تم كشف معلومة التواصل بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to reveal contact', [
                'contact_id' => $contact->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في كشف معلومة التواصل'
            ], 500);
        }
    }

    /**
     * تسجيل الوصول لمعلومات التواصل (API عام)
     */
    public function logAccess(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            $validator = Validator::make($request->all(), [
                'contact_type' => 'required|string',
                'ad_id' => 'required|integer',
                'contact_id' => 'nullable|integer',
                'access_type' => 'required|in:view,click,copy,export,share,reveal,download_qr'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => 'بيانات غير صحيحة'
                ], 422);
            }

            // الحصول على معلومات الإعلان
            $ad = \App\Models\Ad::find($request->ad_id);
            if (!$ad) {
                return response()->json([
                    'success' => false,
                    'message' => 'الإعلان غير موجود'
                ], 404);
            }

            // تسجيل الوصول
            $this->securityService->logContactAccess(
                $ad->user_id,
                $request->access_type,
                $request->contact_id,
                $user?->id,
                [
                    'ad_id' => $request->ad_id,
                    'contact_type' => $request->contact_type,
                    'user_agent' => request()->userAgent()
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الوصول بنجاح'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to log contact access', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'request_data' => $request->except(['_token'])
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تسجيل الوصول'
            ], 500);
        }
    }
}
