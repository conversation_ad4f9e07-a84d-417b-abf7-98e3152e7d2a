<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use App\Models\User;
use App\Services\PasswordPolicyService;

/**
 * كونترولر المصادقة
 * يدير تسجيل الدخول والخروج وإنشاء الحسابات
 */
class AuthController extends Controller
{
    /**
     * عرض صفحة تسجيل الدخول
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * معالجة تسجيل الدخول
     */
    public function login(Request $request)
    {
        // التحقق من صحة البيانات
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        // التحقق من Rate Limiting
        $key = 'login.' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            return back()->withErrors([
                'email' => __('Too many login attempts. Please try again in :seconds seconds.', ['seconds' => $seconds]),
            ])->withInput($request->except('password'));
        }

        // محاولة تسجيل الدخول
        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials, $request->has('remember'))) {
            // مسح محاولات تسجيل الدخول الفاشلة
            RateLimiter::clear($key);

            // تجديد الجلسة لمنع session fixation
            $request->session()->regenerate();

            // تحديث آخر تسجيل دخول
            $user = Auth::user();
            $user->update([
                'last_login_at' => now(),
                'last_login_ip' => $request->ip(),
            ]);

            // التحقق من انتهاء صلاحية كلمة المرور
            if (PasswordPolicyService::isPasswordExpired($user)) {
                return redirect()->route('auth.profile')
                    ->with('warning', __('Password has expired. Please update it.'));
            }

            // رسالة ترحيب
            return redirect()->intended(route('home'))
                ->with('success', __('Welcome back :name!', ['name' => $user->name]));
        }

        // تسجيل محاولة فاشلة
        RateLimiter::hit($key, 900); // 15 دقيقة

        // في حالة فشل تسجيل الدخول
        return $this->authError('email', 'These credentials do not match our records.', $request);
    }

    /**
     * عرض صفحة إنشاء حساب جديد
     */
    public function showRegisterForm()
    {
        return view('auth.register');
    }

    /**
     * معالجة إنشاء حساب جديد
     */
    public function register(Request $request)
    {
        // التحقق من Rate Limiting للتسجيل
        $key = 'register.' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 3)) {
            $seconds = RateLimiter::availableIn($key);
            return back()->withErrors([
                'email' => __('Too many registration attempts. Please try again in :seconds seconds.', ['seconds' => $seconds]),
            ])->withInput($request->except('password', 'password_confirmation'));
        }

        // التحقق من صحة البيانات الأساسية
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|confirmed',
            'card_number' => 'nullable|string|max:20',
            'card_type' => 'nullable|string|max:100',
            'currency' => 'required|in:YER,USD,SAR',
        ]);

        // التحقق من قوة كلمة المرور
        $passwordValidation = PasswordPolicyService::validatePassword($request->password, false);
        if (!$passwordValidation['valid']) {
            return $this->registrationError('password', implode(' ', $passwordValidation['errors']), $request);
        }

        try {
            // إنشاء المستخدم الجديد باستخدام الخدمة الموحدة
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => \Hash::make($request->password),
                'card_number' => $request->card_number,
                'card_type' => $request->card_type,
                'currency' => $request->currency,
                'password_changed_at' => now(),
                'registration_ip' => $request->ip(),
            ]);

            // مسح محاولات التسجيل
            RateLimiter::clear($key);

            // تسجيل دخول تلقائي للمستخدم الجديد
            Auth::login($user);

            // تجديد الجلسة
            $request->session()->regenerate();

            // رسالة ترحيب
            return redirect()->route('home')
                ->with('success', __('Account created successfully! Welcome :name!', ['name' => $user->name]));

        } catch (\Exception $e) {
            // تسجيل محاولة فاشلة
            RateLimiter::hit($key, 1800); // 30 دقيقة

            return back()->withErrors([
                'email' => __('An error occurred while creating the account. Please try again.'),
            ])->withInput($request->except('password', 'password_confirmation'));
        }
    }

    /**
     * تسجيل الخروج
     */
    public function logout(Request $request)
    {
        Auth::logout();

        // إلغاء الجلسة وإعادة توليد التوكن
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('home')
            ->with('success', __('You have been logged out successfully.'));
    }

    /**
     * عرض صفحة الملف الشخصي
     */
    public function profile()
    {
        return view('auth.profile');
    }

    /**
     * تحديث الملف الشخصي
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        // التحقق من صحة البيانات
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'card_number' => 'nullable|string|max:20',
            'card_type' => 'nullable|string|max:100',
            'currency' => 'required|in:YER,USD,SAR',
            'current_password' => 'nullable|string',
            'new_password' => 'nullable|string|confirmed',
        ]);

        // تحديث كلمة المرور إذا تم إدخالها
        if ($request->filled('new_password')) {
            // التحقق من كلمة المرور الحالية
            if (!$request->filled('current_password') ||
                !\Hash::check($request->current_password, (string)$user->password)) {
                return back()->withErrors([
                    'current_password' => 'كلمة المرور الحالية غير صحيحة.',
                ])->withInput($request->except('current_password', 'new_password', 'new_password_confirmation'));
            }

            // التحقق من قوة كلمة المرور الجديدة
            $passwordValidation = PasswordPolicyService::validatePassword(
                $request->new_password,
                $user->is_admin
            );

            if (!$passwordValidation['valid']) {
                return back()->withErrors([
                    'new_password' => $passwordValidation['errors']
                ])->withInput($request->except('current_password', 'new_password', 'new_password_confirmation'));
            }

            // التحقق من عدم إعادة استخدام كلمة المرور
            if (\Hash::check($request->new_password, (string)$user->password)) {
                return back()->withErrors([
                    'new_password' => 'لا يمكن استخدام كلمة المرور الحالية ككلمة مرور جديدة.',
                ])->withInput($request->except('current_password', 'new_password', 'new_password_confirmation'));
            }

            // تحديث كلمة المرور
            $user->update([
                'password' => \Hash::make($request->new_password),
                'password_changed_at' => now(),
            ]);
        }

        // تحديث البيانات الأساسية
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'card_number' => $request->card_number,
            'card_type' => $request->card_type,
            'currency' => $request->currency,
        ]);

        return back()->with('success', 'تم تحديث ملفك الشخصي بنجاح!');
    }

    /**
     * إرجاع رسالة خطأ موحدة للمصادقة
     */
    private function authError(string $field, string $message, Request $request, array $excludeFields = ['password'])
    {
        return back()->withErrors([
            $field => __($message)
        ])->withInput($request->except($excludeFields));
    }

    /**
     * إرجاع رسالة خطأ موحدة للتسجيل
     */
    private function registrationError(string $field, string $message, Request $request)
    {
        return $this->authError($field, $message, $request, ['password', 'password_confirmation']);
    }

    /**
     * إرجاع رسالة خطأ موحدة لتحديث الملف الشخصي
     */
    private function profileError(string $field, string $message, Request $request)
    {
        return $this->authError($field, $message, $request, ['current_password', 'new_password', 'new_password_confirmation']);
    }
}
