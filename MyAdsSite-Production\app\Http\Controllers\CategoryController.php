<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Services\InputSanitizationService;
use Illuminate\Support\Facades\DB;

/**
 * كونترولر التصنيفات
 * يدير عرض التصنيفات والتنقل بينها
 */
class CategoryController extends Controller
{
    /**
     * عرض جميع التصنيفات
     * صفحة التصنيفات الستة الأساسية
     */
    public function index()
    {
        // جلب التصنيفات النشطة مع عدد الإعلانات لكل تصنيف
        $categories = Category::active()
            ->withCount(['ads' => function ($query) {
                $query->active()->notExpired();
            }])
            ->orderBy('name_ar')
            ->get();

        return view('categories.index', compact('categories'));
    }

    /**
     * عرض تصنيف محدد مع إعلاناته
     */
    public function show($slug)
    {
        // البحث عن التصنيف بالـ slug
        $category = Category::active()
            ->where('slug', $slug)
            ->firstOrFail();

        // جلب الإعلانات النشطة للتصنيف مع إمكانية البحث والتصفية
        $ads = $category->ads()
            ->active()
            ->notExpired()
            ->with('category')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('categories.show', compact('category', 'ads'));
    }

    /**
     * البحث في التصنيفات
     */
    public function search(Request $request)
    {
        // التحقق من صحة البيانات
        $request->validate([
            'q' => 'required|string|max:255',
        ]);

        $query = $request->get('q');

        // تنظيف مدخلات البحث لحماية من XSS وSQL Injection
        $query = InputSanitizationService::sanitizeText($query);

        if (empty($query)) {
            return redirect()->route('categories.index');
        }

        // البحث في أسماء التصنيفات مع حماية من SQL Injection
        $categories = Category::active()
            ->where(function ($q) use ($query) {
                $q->where('name_ar', 'LIKE', "%{$query}%")
                  ->orWhere('name_en', 'LIKE', "%{$query}%")
                  ->orWhere('description_ar', 'LIKE', "%{$query}%")
                  ->orWhere('description_en', 'LIKE', "%{$query}%");
            })
            ->withCount(['ads' => function ($q) {
                $q->active()->notExpired();
            }])
            ->get();

        return view('categories.search', compact('categories', 'query'));
    }

    /**
     * إحصائيات التصنيف
     * عرض صفحة إحصائيات مفصلة للتصنيف
     */
    public function stats($slug)
    {
        $category = Category::active()
            ->where('slug', $slug)
            ->firstOrFail();

        // إحصائيات أساسية
        $stats = [
            'total_ads' => $category->ads()->active()->notExpired()->count(),
            'pending_ads' => $category->ads()->where('status', 'pending')->count(),
            'active_ads' => $category->ads()->active()->notExpired()->count(),
            'expired_ads' => $category->ads()->where('expires_at', '<', now())->count(),
            'recent_ads' => $category->ads()->active()->notExpired()
                ->where('created_at', '>=', now()->subDays(7))->count(),
            'ads_today' => $category->ads()->whereDate('created_at', today())->count(),
            'ads_this_week' => $category->ads()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'ads_this_month' => $category->ads()->whereMonth('created_at', now()->month)->count(),
            'featured_ads' => $category->ads()->where('is_featured', true)->count(),
        ];

        // أحدث الإعلانات في هذا التصنيف
        $recentAds = $category->ads()
            ->with(['user'])
            ->active()
            ->notExpired()
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // إحصائيات المستخدمين
        $userStats = DB::table('ads')
            ->select('user_id', DB::raw('count(*) as ads_count'))
            ->where('category_id', $category->id)
            ->groupBy('user_id')
            ->orderBy('ads_count', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($stat) {
                $stat->user = \App\Models\User::find($stat->user_id);
                return $stat;
            });

        return view('categories.stats', compact('category', 'stats', 'recentAds', 'userStats'));
    }
}
