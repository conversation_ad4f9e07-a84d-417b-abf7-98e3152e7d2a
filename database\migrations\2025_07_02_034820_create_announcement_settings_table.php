<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('announcement_settings', function (Blueprint $table) {
            $table->id();
            $table->boolean('is_enabled')->default(true)->comment('تفعيل/إلغاء تفعيل الشريط');
            $table->enum('animation_type', ['rotation', 'marquee'])->default('rotation')->comment('نوع الحركة');
            $table->integer('transition_duration')->default(4)->comment('مدة التبديل بالثواني');
            $table->integer('scroll_speed')->default(20)->comment('سرعة التمرير بالثواني');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('announcement_settings');
    }
};
