<?php

return [

    /*
    |--------------------------------------------------------------------------
    | SSL/TLS Configuration - موقع إعلاناتي
    |--------------------------------------------------------------------------
    |
    | إعدادات SSL/TLS للتطبيق
    | تحدد سياسات الأمان والشهادات المطلوبة
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Force HTTPS
    |--------------------------------------------------------------------------
    |
    | فرض استخدام HTTPS في جميع الطلبات
    |
    */
    'force_https' => env('FORCE_HTTPS', true),

    /*
    |--------------------------------------------------------------------------
    | HSTS Settings
    |--------------------------------------------------------------------------
    |
    | إعدادات HTTP Strict Transport Security
    |
    */
    'hsts' => [
        'enabled' => env('HSTS_ENABLED', true),
        'max_age' => env('HSTS_MAX_AGE', 31536000), // سنة واحدة
        'include_subdomains' => env('HSTS_INCLUDE_SUBDOMAINS', true),
        'preload' => env('HSTS_PRELOAD', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Certificate Validation
    |--------------------------------------------------------------------------
    |
    | إعدادات التحقق من صحة الشهادات
    |
    */
    'certificate' => [
        'verify_peer' => env('SSL_VERIFY_PEER', true),
        'verify_host' => env('SSL_VERIFY_HOST', true),
        'allow_self_signed' => env('SSL_ALLOW_SELF_SIGNED', false),
        'ca_file' => env('SSL_CA_FILE', null),
        'ca_path' => env('SSL_CA_PATH', null),
    ],

    /*
    |--------------------------------------------------------------------------
    | TLS Version
    |--------------------------------------------------------------------------
    |
    | الحد الأدنى لإصدار TLS المطلوب
    |
    */
    'min_tls_version' => env('MIN_TLS_VERSION', '1.2'),

    /*
    |--------------------------------------------------------------------------
    | Cipher Suites
    |--------------------------------------------------------------------------
    |
    | مجموعات التشفير المسموحة
    |
    */
    'cipher_suites' => [
        'TLS_AES_256_GCM_SHA384',
        'TLS_CHACHA20_POLY1305_SHA256',
        'TLS_AES_128_GCM_SHA256',
        'ECDHE-RSA-AES256-GCM-SHA384',
        'ECDHE-RSA-AES128-GCM-SHA256',
        'ECDHE-RSA-AES256-SHA384',
        'ECDHE-RSA-AES128-SHA256',
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    |
    | Headers أمنية إضافية متعلقة بـ SSL
    |
    */
    'security_headers' => [
        'expect_ct' => [
            'enabled' => env('EXPECT_CT_ENABLED', true),
            'max_age' => env('EXPECT_CT_MAX_AGE', 86400),
            'enforce' => env('EXPECT_CT_ENFORCE', false),
            'report_uri' => env('EXPECT_CT_REPORT_URI', null),
        ],
        
        'public_key_pins' => [
            'enabled' => env('HPKP_ENABLED', false),
            'pins' => env('HPKP_PINS', []),
            'max_age' => env('HPKP_MAX_AGE', 5184000),
            'include_subdomains' => env('HPKP_INCLUDE_SUBDOMAINS', false),
            'report_uri' => env('HPKP_REPORT_URI', null),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Certificate Transparency
    |--------------------------------------------------------------------------
    |
    | إعدادات شفافية الشهادات
    |
    */
    'certificate_transparency' => [
        'enabled' => env('CT_ENABLED', true),
        'logs' => [
            'google_pilot' => 'https://ct.googleapis.com/pilot/',
            'google_aviator' => 'https://ct.googleapis.com/aviator/',
            'cloudflare_nimbus' => 'https://ct.cloudflare.com/logs/nimbus2023/',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | OCSP Settings
    |--------------------------------------------------------------------------
    |
    | إعدادات Online Certificate Status Protocol
    |
    */
    'ocsp' => [
        'enabled' => env('OCSP_ENABLED', true),
        'stapling' => env('OCSP_STAPLING', true),
        'must_staple' => env('OCSP_MUST_STAPLE', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Mixed Content Policy
    |--------------------------------------------------------------------------
    |
    | سياسة المحتوى المختلط
    |
    */
    'mixed_content' => [
        'block_all' => env('BLOCK_MIXED_CONTENT', true),
        'upgrade_insecure' => env('UPGRADE_INSECURE_REQUESTS', true),
    ],

];
