{{--
    Component موحد لأزرار العودة
    
    الاستخدام:
    <x-back-button 
        :url="route('dashboard.index')" 
        text="العودة إلى لوحة التحكم" 
        icon="fas fa-tachometer-alt"
        variant="outline-secondary" 
    />
--}}

@props([
    'url' => '#',
    'text' => __('Back'),
    'icon' => null,
    'variant' => 'outline-secondary',
    'size' => '',
    'class' => ''
])

@php
    // تحديد الأيقونة الافتراضية حسب اتجاه اللغة
    if (!$icon) {
        $icon = app()->getLocale() === 'ar' ? 'fas fa-arrow-right' : 'fas fa-arrow-left';
    }
    
    // تحديد حجم الزر
    $sizeClass = $size ? 'btn-' . $size : '';
    
    // دمج الكلاسات
    $buttonClass = trim("btn btn-{$variant} {$sizeClass} arabic-text {$class}");
@endphp

<a href="{{ $url }}" class="{{ $buttonClass }}">
    <i class="{{ $icon }} me-2"></i>
    {{ $text }}
</a>

@push('styles')
<style>
/* تحسينات أزرار العودة */
.btn.arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn.arabic-text:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تحسين للغة العربية */
@if(app()->getLocale() === 'ar')
.btn.arabic-text i {
    margin-right: 0;
    margin-left: 0.5rem;
}
@endif

/* تأثيرات بصرية إضافية */
.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.btn-outline-success:hover {
    background-color: #198754;
    border-color: #198754;
    color: white;
}

.btn-outline-info:hover {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: white;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}
</style>
@endpush
