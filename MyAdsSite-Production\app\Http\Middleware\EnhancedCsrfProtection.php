<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * حماية محسنة من CSRF
 * يوفر حماية إضافية من هجمات Cross-Site Request Forgery
 */
class EnhancedCsrfProtection
{
    /**
     * المسارات المستثناة من فحص CSRF
     */
    private array $except = [
        'api/*',
        'webhook/*',
        'auth/login',
        'auth/register',
        'login',
    ];

    /**
     * الطرق التي تحتاج فحص CSRF
     */
    private array $methodsRequiringCsrf = [
        'POST', 'PUT', 'PATCH', 'DELETE'
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // تخطي الفحص للطرق الآمنة
        if (!in_array($request->method(), $this->methodsRequiringCsrf)) {
            return $next($request);
        }

        // تخطي الفحص للمسارات المستثناة
        if ($this->shouldSkip($request)) {
            return $next($request);
        }

        // فحص CSRF token
        if (!$this->isValidCsrfToken($request)) {
            $this->logCsrfAttempt($request);
            return $this->handleInvalidToken($request);
        }

        // فحص إضافي للأمان
        if (!$this->additionalSecurityChecks($request)) {
            $this->logSuspiciousActivity($request);
            return $this->handleSuspiciousRequest($request);
        }

        return $next($request);
    }

    /**
     * التحقق من ضرورة تخطي الفحص
     */
    private function shouldSkip(Request $request): bool
    {
        foreach ($this->except as $pattern) {
            if ($request->is($pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * التحقق من صحة CSRF token
     */
    private function isValidCsrfToken(Request $request): bool
    {
        $token = $this->getTokenFromRequest($request);

        if (!$token) {
            return false;
        }

        // التحقق من صحة التوكن
        return hash_equals(
            $request->session()->token(),
            $token
        );
    }

    /**
     * الحصول على التوكن من الطلب
     */
    private function getTokenFromRequest(Request $request): ?string
    {
        // البحث في الحقول المختلفة
        $token = $request->input('_token') ?:
                 $request->header('X-CSRF-TOKEN') ?:
                 $request->header('X-XSRF-TOKEN');

        // فك تشفير X-XSRF-TOKEN إذا لزم الأمر
        if ($request->header('X-XSRF-TOKEN')) {
            $token = $this->decryptXsrfToken($request->header('X-XSRF-TOKEN'));
        }

        return $token;
    }

    /**
     * فك تشفير XSRF token
     */
    private function decryptXsrfToken(string $token): ?string
    {
        try {
            return decrypt($token);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * فحوصات أمنية إضافية
     */
    private function additionalSecurityChecks(Request $request): bool
    {
        // فحص Referer header
        if (!$this->isValidReferer($request)) {
            return false;
        }

        // فحص Origin header
        if (!$this->isValidOrigin($request)) {
            return false;
        }

        // فحص User-Agent
        if (!$this->isValidUserAgent($request)) {
            return false;
        }

        return true;
    }

    /**
     * فحص Referer header
     */
    private function isValidReferer(Request $request): bool
    {
        $referer = $request->header('referer');

        if (!$referer) {
            // السماح بالطلبات بدون referer في بعض الحالات
            return true;
        }

        // تخطي فحص Referer لمسارات المصادقة
        if ($request->is('auth/*') || $request->is('login')) {
            return true;
        }

        $allowedHosts = [
            $request->getHost(),
            parse_url(config('app.url'), PHP_URL_HOST),
            'myadssiteak.free.nf', // إضافة النطاق الحالي
        ];

        $refererHost = parse_url($referer, PHP_URL_HOST);

        return in_array($refererHost, $allowedHosts);
    }

    /**
     * فحص Origin header
     */
    private function isValidOrigin(Request $request): bool
    {
        $origin = $request->header('origin');

        if (!$origin) {
            return true;
        }

        // تخطي فحص Origin لمسارات المصادقة
        if ($request->is('auth/*') || $request->is('login')) {
            return true;
        }

        $allowedOrigins = [
            $request->getSchemeAndHttpHost(),
            config('app.url'),
            'https://myadssiteak.free.nf',
            'http://myadssiteak.free.nf',
        ];

        return in_array($origin, $allowedOrigins);
    }

    /**
     * فحص User-Agent
     */
    private function isValidUserAgent(Request $request): bool
    {
        $userAgent = $request->userAgent();

        if (!$userAgent) {
            // السماح بالطلبات بدون User-Agent لمسارات المصادقة
            if ($request->is('auth/*') || $request->is('login')) {
                return true;
            }
            return false;
        }

        // تخطي فحص User-Agent لمسارات المصادقة
        if ($request->is('auth/*') || $request->is('login')) {
            return true;
        }

        // فحص User-Agent مشبوه
        $suspiciousAgents = [
            'curl', 'wget', 'python', 'bot', 'crawler', 'spider'
        ];

        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                return false;
            }
        }

        return true;
    }

    /**
     * تسجيل محاولة CSRF
     */
    private function logCsrfAttempt(Request $request): void
    {
        Log::warning('محاولة CSRF مكتشفة', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'referer' => $request->header('referer'),
            'origin' => $request->header('origin'),
            'has_token' => $request->has('_token'),
            'session_id' => $request->session()->getId(),
        ]);
    }

    /**
     * تسجيل نشاط مشبوه
     */
    private function logSuspiciousActivity(Request $request): void
    {
        Log::warning('نشاط مشبوه مكتشف في CSRF middleware', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'referer' => $request->header('referer'),
            'origin' => $request->header('origin'),
        ]);
    }

    /**
     * التعامل مع التوكن غير الصحيح
     */
    private function handleInvalidToken(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'CSRF token mismatch.',
                'error' => 'رمز الأمان غير صحيح. يرجى تحديث الصفحة والمحاولة مرة أخرى.'
            ], 419);
        }

        return redirect()->back()
            ->withInput($request->except('_token'))
            ->withErrors(['csrf' => 'رمز الأمان غير صحيح. يرجى المحاولة مرة أخرى.']);
    }

    /**
     * التعامل مع الطلب المشبوه
     */
    private function handleSuspiciousRequest(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Suspicious request detected.',
                'error' => 'تم رصد نشاط مشبوه. تم تسجيل هذا الطلب.'
            ], 403);
        }

        return redirect()->route('home')
            ->withErrors(['security' => 'تم رصد نشاط مشبوه. يرجى المحاولة مرة أخرى.']);
    }

    /**
     * إضافة مسار للاستثناء
     */
    public function addException(string $pattern): void
    {
        $this->except[] = $pattern;
    }

    /**
     * الحصول على قائمة المسارات المستثناة
     */
    public function getExceptions(): array
    {
        return $this->except;
    }
}
