<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * نموذج التعليقات
 * يدعم التعليقات المتداخلة والردود
 */
class Comment extends Model
{
    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',
        'ad_id',
        'parent_id',
        'content',
        'content_html',
        'status',
        'is_pinned',
        'is_edited',
        'edited_at',
        'likes_count',
        'dislikes_count',
        'replies_count',
        'is_helpful',
        'metadata',
        'language',
        'depth',
        'approved_by',
        'approved_at',
        'rejection_reason',
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'user_id' => 'integer',
        'ad_id' => 'integer',
        'parent_id' => 'integer',
        'is_pinned' => 'boolean',
        'is_edited' => 'boolean',
        'edited_at' => 'datetime',
        'likes_count' => 'integer',
        'dislikes_count' => 'integer',
        'replies_count' => 'integer',
        'is_helpful' => 'boolean',
        'metadata' => 'array',
        'depth' => 'integer',
        'approved_by' => 'integer',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * القيم الافتراضية
     */
    protected $attributes = [
        'status' => 'pending',
        'is_pinned' => false,
        'is_edited' => false,
        'likes_count' => 0,
        'dislikes_count' => 0,
        'replies_count' => 0,
        'is_helpful' => false,
        'language' => 'ar',
        'depth' => 0,
    ];

    /**
     * علاقة متعدد إلى واحد مع المستخدمين
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * علاقة متعدد إلى واحد مع الإعلانات
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * علاقة متعدد إلى واحد مع التعليق الأب
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Comment::class, 'parent_id');
    }

    /**
     * علاقة واحد إلى متعدد مع التعليقات الفرعية (الردود)
     */
    public function replies(): HasMany
    {
        return $this->hasMany(Comment::class, 'parent_id')->orderBy('created_at', 'asc');
    }

    /**
     * علاقة مع المستخدم الذي وافق على التعليق
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * البحث في تعليقات مستخدم معين
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * البحث في تعليقات إعلان معين
     */
    public function scopeByAd(Builder $query, int $adId): Builder
    {
        return $query->where('ad_id', $adId);
    }

    /**
     * البحث في التعليقات الرئيسية (غير الردود)
     */
    public function scopeMainComments(Builder $query): Builder
    {
        return $query->whereNull('parent_id');
    }

    /**
     * البحث في الردود
     */
    public function scopeReplies(Builder $query): Builder
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * البحث في التعليقات المعتمدة
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('status', 'approved');
    }

    /**
     * البحث في التعليقات المعلقة
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * البحث في التعليقات المثبتة
     */
    public function scopePinned(Builder $query): Builder
    {
        return $query->where('is_pinned', true);
    }

    /**
     * ترتيب حسب الأحدث
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * ترتيب حسب الأقدم
     */
    public function scopeOldest(Builder $query): Builder
    {
        return $query->orderBy('created_at', 'asc');
    }

    /**
     * ترتيب حسب الأكثر إعجاباً
     */
    public function scopeMostLiked(Builder $query): Builder
    {
        return $query->orderBy('likes_count', 'desc');
    }

    /**
     * ترتيب حسب الأكثر ردوداً
     */
    public function scopeMostReplied(Builder $query): Builder
    {
        return $query->orderBy('replies_count', 'desc');
    }

    /**
     * إضافة تعليق جديد
     */
    public static function addComment(array $data): self
    {
        // تنظيف المحتوى
        $content = strip_tags($data['content']);
        $contentHtml = Str::markdown($content);

        // تحديد العمق
        $depth = 0;
        if (!empty($data['parent_id'])) {
            $parent = static::find($data['parent_id']);
            $depth = $parent ? $parent->depth + 1 : 0;
        }

        // إنشاء التعليق
        $comment = static::create([
            'user_id' => $data['user_id'],
            'ad_id' => $data['ad_id'],
            'parent_id' => $data['parent_id'] ?? null,
            'content' => $content,
            'content_html' => $contentHtml,
            'depth' => $depth,
            'language' => $data['language'] ?? 'ar',
            'metadata' => [
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'timestamp' => now()->toISOString()
            ]
        ]);

        // تحديث عداد الردود للتعليق الأب
        if ($comment->parent_id) {
            static::where('id', $comment->parent_id)->increment('replies_count');
        }

        return $comment;
    }

    /**
     * الموافقة على التعليق
     */
    public function approve(?int $approvedBy = null): bool
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy ?? auth()->id(),
            'approved_at' => now()
        ]);

        return true;
    }

    /**
     * رفض التعليق
     */
    public function reject(string $reason = null): bool
    {
        $this->update([
            'status' => 'rejected',
            'rejection_reason' => $reason
        ]);

        return true;
    }

    /**
     * إخفاء التعليق
     */
    public function hide(): bool
    {
        $this->update(['status' => 'hidden']);
        return true;
    }

    /**
     * تثبيت التعليق
     */
    public function pin(): bool
    {
        $this->update(['is_pinned' => true]);
        return true;
    }

    /**
     * إلغاء تثبيت التعليق
     */
    public function unpin(): bool
    {
        $this->update(['is_pinned' => false]);
        return true;
    }

    /**
     * إضافة إعجاب
     */
    public function addLike(): bool
    {
        $this->increment('likes_count');
        return true;
    }

    /**
     * إزالة إعجاب
     */
    public function removeLike(): bool
    {
        $this->decrement('likes_count');
        return true;
    }

    /**
     * إضافة عدم إعجاب
     */
    public function addDislike(): bool
    {
        $this->increment('dislikes_count');
        return true;
    }

    /**
     * إزالة عدم إعجاب
     */
    public function removeDislike(): bool
    {
        $this->decrement('dislikes_count');
        return true;
    }

    /**
     * الحصول على التعليقات مع الردود
     */
    public static function getCommentsWithReplies(int $adId, int $perPage = 10)
    {
        return static::byAd($adId)
                    ->mainComments()
                    ->approved()
                    ->with(['user:id,name,avatar', 'replies' => function ($query) {
                        $query->approved()
                              ->with('user:id,name,avatar')
                              ->oldest();
                    }])
                    ->pinned()
                    ->latest()
                    ->paginate($perPage);
    }

    /**
     * الحصول على عدد التعليقات لإعلان معين
     */
    public static function getCommentsCount(int $adId): int
    {
        $cacheKey = "ad_comments_count_{$adId}";

        return Cache::remember($cacheKey, 1800, function () use ($adId) {
            return static::byAd($adId)->approved()->count();
        });
    }

    /**
     * مسح الكاش عند إضافة أو تحديث تعليق
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($comment) {
            Cache::forget("ad_comments_count_{$comment->ad_id}");
        });

        static::deleted(function ($comment) {
            Cache::forget("ad_comments_count_{$comment->ad_id}");

            // تحديث عداد الردود للتعليق الأب
            if ($comment->parent_id) {
                static::where('id', $comment->parent_id)->decrement('replies_count');
            }
        });
    }

    /**
     * الحصول على نص الحالة
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'pending' => 'في الانتظار',
            'approved' => 'معتمد',
            'rejected' => 'مرفوض',
            'hidden' => 'مخفي',
            default => 'غير محدد'
        };
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'hidden' => 'secondary',
            default => 'secondary'
        };
    }

    /**
     * التحقق من إمكانية الرد على التعليق
     */
    public function canReply(): bool
    {
        return $this->status === 'approved' && $this->depth < 3; // حد أقصى 3 مستويات
    }

    /**
     * التحقق من إمكانية تعديل التعليق
     */
    public function canEdit(?int $userId = null): bool
    {
        $userId = $userId ?? auth()->id();
        return $this->user_id === $userId &&
               $this->created_at->diffInMinutes(now()) <= 30; // يمكن التعديل خلال 30 دقيقة
    }
}
