/**
 * تأثيرات واجهة المستخدم المبسطة
 * ملف محسن للتأثيرات البصرية
 */

class UIEffects {
    constructor() {
        this.init();
    }

    /**
     * تهيئة التأثيرات
     */
    init() {
        this.initHoverEffects();
        this.initClickEffects();
        this.initImageLazyLoading();
    }

    /**
     * تأثيرات التحويم المحسنة
     */
    initHoverEffects() {
        // استخدام event delegation للأداء الأفضل
        document.addEventListener('mouseenter', this.handleMouseEnter.bind(this), true);
        document.addEventListener('mouseleave', this.handleMouseLeave.bind(this), true);
    }

    /**
     * معالجة دخول الماوس
     */
    handleMouseEnter(event) {
        const target = event.target;
        
        // تأثير الرفع للبطاقات
        if (target.matches('.card, .hover-lift')) {
            this.applyLiftEffect(target, true);
        }
        
        // تأثير التكبير للصور
        if (target.matches('.hover-zoom img')) {
            this.applyZoomEffect(target, true);
        }
    }

    /**
     * معالجة خروج الماوس
     */
    handleMouseLeave(event) {
        const target = event.target;
        
        if (target.matches('.card, .hover-lift')) {
            this.applyLiftEffect(target, false);
        }
        
        if (target.matches('.hover-zoom img')) {
            this.applyZoomEffect(target, false);
        }
    }

    /**
     * تطبيق تأثير الرفع
     */
    applyLiftEffect(element, isHover) {
        const transform = isHover ? 'translateY(-5px)' : 'translateY(0)';
        const boxShadow = isHover ? '0 10px 25px rgba(0, 0, 0, 0.15)' : '';
        
        Object.assign(element.style, {
            transform,
            boxShadow,
            transition: 'all 0.3s ease'
        });
    }

    /**
     * تطبيق تأثير التكبير
     */
    applyZoomEffect(element, isHover) {
        const transform = isHover ? 'scale(1.05)' : 'scale(1)';
        
        Object.assign(element.style, {
            transform,
            transition: 'transform 0.3s ease'
        });
    }

    /**
     * تأثيرات النقر المحسنة
     */
    initClickEffects() {
        document.addEventListener('click', this.handleClick.bind(this));
    }

    /**
     * معالجة النقر
     */
    handleClick(event) {
        const target = event.target;
        
        // تأثير الموجة للأزرار (مع استثناءات)
        if (target.matches('.btn, .btn-ripple') && !this.shouldSkipRipple(target)) {
            this.createRippleEffect(event, target);
        }
    }

    /**
     * فحص ما إذا كان يجب تخطي تأثير الموجة
     */
    shouldSkipRipple(element) {
        return element.closest('.nav-link') ||
               element.id === 'languageToggleBtn' ||
               element.id === 'themeToggleBtn' ||
               element.classList.contains('language-toggle-btn');
    }

    /**
     * إنشاء تأثير الموجة المحسن
     */
    createRippleEffect(event, element) {
        // منع إنشاء موجات متعددة
        if (element.querySelector('.ripple-effect')) return;

        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        Object.assign(ripple.style, {
            width: `${size}px`,
            height: `${size}px`,
            left: `${x}px`,
            top: `${y}px`,
            position: 'absolute',
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.6)',
            transform: 'scale(0)',
            animation: 'ripple 0.6s linear',
            pointerEvents: 'none'
        });

        ripple.classList.add('ripple-effect');

        // تحضير العنصر للموجة
        Object.assign(element.style, {
            position: 'relative',
            overflow: 'hidden'
        });

        element.appendChild(ripple);

        // إزالة الموجة بعد انتهاء الرسوم المتحركة
        setTimeout(() => ripple.remove(), 600);
    }

    /**
     * تحميل الصور الكسول المحسن
     */
    initImageLazyLoading() {
        if (!('IntersectionObserver' in window)) return;

        const imageObserver = new IntersectionObserver(
            this.handleImageIntersection.bind(this),
            { threshold: 0.1 }
        );

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    /**
     * معالجة تقاطع الصور
     */
    handleImageIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                const src = img.dataset.src;
                
                if (src) {
                    img.src = src;
                    img.classList.add('loaded');
                    img.removeAttribute('data-src');
                }
                
                this.imageObserver?.unobserve(img);
            }
        });
    }

    /**
     * إضافة CSS للتأثيرات
     */
    static addStyles() {
        if (document.getElementById('ui-effects-styles')) return;

        const style = document.createElement('style');
        style.id = 'ui-effects-styles';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
            
            img[data-src] {
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            img.loaded {
                opacity: 1;
            }
        `;
        
        document.head.appendChild(style);
    }
}

// إضافة الأنماط عند تحميل الملف
UIEffects.addStyles();

export default UIEffects;
