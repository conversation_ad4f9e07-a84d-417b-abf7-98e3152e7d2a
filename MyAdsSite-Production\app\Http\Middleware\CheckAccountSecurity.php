<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware للتحقق من أمان الحساب
 * يتحقق من قفل الحساب وانتهاء صلاحية كلمة المرور
 * ويوفر فحص صلاحيات الإدارة عند الحاجة
 */
class CheckAccountSecurity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string|null  $requireAdmin  - إذا كان 'admin' فسيتم فحص صلاحيات الإدارة
     */
    public function handle(Request $request, Closure $next, ?string $requireAdmin = null): Response
    {
        // التحقق من تسجيل الدخول
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();
        $routeName = $request->route() ? $request->route()->getName() : 'unknown';

        // إضافة logging مفصل لتتبع العمليات
        Log::info('CheckAccountSecurity: بدء فحص المستخدم', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'route' => $routeName,
            'url' => $request->fullUrl(),
            'is_admin' => $user->is_admin,
            'require_admin' => $requireAdmin
        ]);

        // التحقق من قفل الحساب
        if ($user->isLocked()) {
            Log::warning('CheckAccountSecurity: حساب مقفل - تسجيل خروج تلقائي', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'locked_until' => $user->locked_until,
                'failed_attempts' => $user->failed_login_attempts,
                'route' => $routeName
            ]);

            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            $lockTime = $user->locked_until->diffForHumans();
            return redirect()->route('auth.login')
                ->withErrors(['email' => "حسابك مقفل حتى {$lockTime} بسبب محاولات تسجيل دخول متعددة فاشلة."]);
        }

        // التحقق من ضرورة تغيير كلمة المرور
        if ($user->mustChangePassword()) {
            Log::info('CheckAccountSecurity: مطلوب تغيير كلمة المرور', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'password_changed_at' => $user->password_changed_at,
                'force_password_change' => $user->force_password_change,
                'is_password_expired' => $user->isPasswordExpired(),
                'route' => $routeName
            ]);

            // السماح بالوصول للمسارات الضرورية فقط
            $allowedRoutes = [
                'auth.profile',
                'auth.update-profile',
                'auth.logout',
                'auth.change-password',
                'language.switch', // السماح بتغيير اللغة
                'home', // السماح بالعودة للصفحة الرئيسية
            ];

            if (!in_array($routeName, $allowedRoutes)) {
                Log::info('CheckAccountSecurity: إعادة توجيه لتغيير كلمة المرور', [
                    'user_id' => $user->id,
                    'blocked_route' => $routeName,
                    'redirect_to' => 'auth.profile'
                ]);

                return redirect()->route('auth.profile')
                    ->with('warning', 'يجب تغيير كلمة المرور قبل المتابعة. إذا كنت مستخدماً جديداً، يرجى تحديث كلمة المرور الخاصة بك.');
            }
        }

        // فحص صلاحيات الإدارة إذا كان مطلوباً
        if ($requireAdmin === 'admin') {
            Log::info('CheckAccountSecurity: فحص صلاحيات الإدارة', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'is_admin' => $user->is_admin,
                'route' => $routeName
            ]);

            return $this->checkAdminPermissions($request, $user, $next);
        }

        Log::info('CheckAccountSecurity: تم اجتياز جميع الفحوصات بنجاح', [
            'user_id' => $user->id,
            'route' => $routeName
        ]);

        return $next($request);
    }

    /**
     * فحص صلاحيات الإدارة
     */
    private function checkAdminPermissions(Request $request, $user, Closure $next): Response
    {
        // التحقق من صلاحيات الإدارة
        if (!$user->is_admin) {
            // تسجيل محاولة الوصول غير المصرح بها
            Log::warning('CheckAccountSecurity: محاولة وصول غير مصرح بها للوحة الإدارة', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_name' => $user->name,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'route' => $request->route() ? $request->route()->getName() : 'unknown',
                'timestamp' => now(),
                'is_admin' => false
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'error' => __('Access denied'),
                    'message' => __('Insufficient permissions. Admin access required'),
                    'redirect' => route('home')
                ], 403);
            }

            // إعادة توجيه للصفحة الرئيسية مع رسالة خطأ واضحة
            return redirect()->route('home')
                ->with('error', 'عذراً، هذه الصفحة مخصصة للمديرين فقط. إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع الإدارة.');
        }

        Log::info('CheckAccountSecurity: تم السماح بالوصول للوحة الإدارة', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'route' => $request->route() ? $request->route()->getName() : 'unknown'
        ]);

        return $next($request);
    }
}
