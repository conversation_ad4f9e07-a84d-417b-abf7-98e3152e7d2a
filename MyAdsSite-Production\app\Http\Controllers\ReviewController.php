<?php

namespace App\Http\Controllers;

use App\Models\Review;
use App\Models\Ad;
use App\Services\ReviewService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * كونترولر التقييمات والمراجعات
 * يدير جميع عمليات التقييمات للمستخدمين
 */
class ReviewController extends Controller
{
    /**
     * عرض التقييمات لإعلان معين
     */
    public function index(Request $request, Ad $ad)
    {
        $sortBy = $request->get('sort', 'newest');
        $filterRating = $request->get('rating', 'all');
        $perPage = min($request->get('per_page', 10), 20);

        $query = $ad->approvedReviews()->with('user');

        // الترتيب
        switch ($sortBy) {
            case 'oldest':
                $query->oldest();
                break;
            case 'helpful':
                $query->orderByHelpful();
                break;
            case 'rating_high':
                $query->orderBy('rating', 'desc');
                break;
            case 'rating_low':
                $query->orderBy('rating', 'asc');
                break;
            default:
                $query->latest();
        }

        // فلتر التقييم
        if ($filterRating !== 'all' && is_numeric($filterRating)) {
            $query->byRating((int)$filterRating);
        }

        $reviews = $query->paginate($perPage);

        // إحصائيات التقييمات
        $stats = [
            'average_rating' => $ad->average_rating,
            'total_reviews' => $ad->reviews_count,
            'rating_distribution' => $ad->rating_distribution,
        ];

        // تحقق من وجود تقييم للمستخدم الحالي
        $userReview = $ad->getUserReview();

        return view('reviews.index', compact(
            'ad',
            'reviews',
            'stats',
            'userReview',
            'sortBy',
            'filterRating'
        ));
    }

    /**
     * عرض نموذج إنشاء تقييم
     */
    public function create(Ad $ad)
    {
        // التحقق من تسجيل الدخول
        if (!auth()->check()) {
            return redirect()->route('login')
                ->with('message', 'يجب تسجيل الدخول لكتابة تقييم');
        }

        // التحقق من عدم وجود تقييم سابق
        if ($ad->hasUserReviewed()) {
            return redirect()->route('reviews.index', $ad)
                ->with('error', 'لقد قمت بتقييم هذا الإعلان مسبقاً');
        }

        // التحقق من أن المستخدم لا يقيم إعلانه
        if ($ad->user_id === auth()->id()) {
            return redirect()->route('reviews.index', $ad)
                ->with('error', 'لا يمكنك تقييم إعلانك الخاص');
        }

        return view('reviews.create', compact('ad'));
    }

    /**
     * حفظ تقييم جديد
     */
    public function store(Request $request, Ad $ad): JsonResponse
    {
        // التحقق من تسجيل الدخول
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول لكتابة تقييم'
            ], 401);
        }

        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'comment' => 'nullable|string|max:1000',
        ]);

        $result = ReviewService::createReview(
            auth()->id(),
            $ad,
            $request->rating,
            $request->title,
            $request->comment
        );

        return response()->json($result);
    }

    /**
     * عرض نموذج تعديل التقييم
     */
    public function edit(Ad $ad, Review $review)
    {
        // التحقق من الصلاحيات
        if ($review->user_id !== auth()->id()) {
            abort(403, 'ليس لديك صلاحية لتعديل هذا التقييم');
        }

        return view('reviews.edit', compact('ad', 'review'));
    }

    /**
     * تحديث تقييم موجود
     */
    public function update(Request $request, Ad $ad, Review $review): JsonResponse
    {
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'comment' => 'nullable|string|max:1000',
        ]);

        $result = ReviewService::updateReview(
            $review,
            $request->rating,
            $request->title,
            $request->comment
        );

        return response()->json($result);
    }

    /**
     * حذف تقييم
     */
    public function destroy(Ad $ad, Review $review): JsonResponse
    {
        $result = ReviewService::deleteReview($review);
        return response()->json($result);
    }

    /**
     * وضع علامة مفيد على التقييم
     */
    public function markAsHelpful(Review $review): JsonResponse
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول'
            ], 401);
        }

        $result = ReviewService::markAsHelpful($review, auth()->id());
        return response()->json($result);
    }

    /**
     * الإبلاغ عن تقييم
     */
    public function report(Request $request, Review $review): JsonResponse
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول'
            ], 401);
        }

        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        try {
            $review->incrementReported();

            // تسجيل البلاغ
            SecurityLog::logEvent(
                'review_reported',
                "تم الإبلاغ عن تقييم",
                SecurityLog::SEVERITY_MEDIUM,
                [
                    'review_id' => $review->id,
                    'reason' => $request->reason,
                    'reported_by' => auth()->id()
                ],
                auth()->id()
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال البلاغ بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في إرسال البلاغ'
            ], 500);
        }
    }

    /**
     * الحصول على التقييمات عبر AJAX
     */
    public function getReviews(Request $request, Ad $ad): JsonResponse
    {
        $sortBy = $request->get('sort', 'newest');
        $filterRating = $request->get('rating', 'all');
        $page = $request->get('page', 1);

        $query = $ad->approvedReviews()->with('user');

        // الترتيب
        switch ($sortBy) {
            case 'oldest':
                $query->oldest();
                break;
            case 'helpful':
                $query->orderByHelpful();
                break;
            case 'rating_high':
                $query->orderBy('rating', 'desc');
                break;
            case 'rating_low':
                $query->orderBy('rating', 'asc');
                break;
            default:
                $query->latest();
        }

        // فلتر التقييم
        if ($filterRating !== 'all' && is_numeric($filterRating)) {
            $query->byRating((int)$filterRating);
        }

        $reviews = $query->paginate(5, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'reviews' => $reviews->items(),
            'pagination' => [
                'current_page' => $reviews->currentPage(),
                'last_page' => $reviews->lastPage(),
                'total' => $reviews->total(),
                'has_more' => $reviews->hasMorePages()
            ]
        ]);
    }

    /**
     * الحصول على إحصائيات التقييمات
     */
    public function getStats(Ad $ad): JsonResponse
    {
        $stats = [
            'average_rating' => $ad->average_rating,
            'total_reviews' => $ad->reviews_count,
            'rating_distribution' => $ad->rating_distribution,
        ];

        return response()->json($stats);
    }

    /**
     * عرض تقييمات المستخدم
     */
    public function userReviews(Request $request)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $status = $request->get('status', 'all');
        $perPage = min($request->get('per_page', 10), 20);

        $query = auth()->user()->reviews()->with(['reviewable']);

        // فلتر حسب الحالة
        if ($status === 'approved') {
            $query->approved();
        } elseif ($status === 'pending') {
            $query->pending();
        }

        $reviews = $query->latest()->paginate($perPage);

        return view('reviews.user-reviews', compact('reviews', 'status'));
    }
}
