<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\AdController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\LanguageController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| هنا يمكنك تسجيل مسارات الويب لتطبيقك. هذه المسارات
| يتم تحميلها بواسطة RouteServiceProvider ضمن مجموعة "web"
| middleware التي تحتوي على حالة الجلسة، CSRF protection، وغيرها.
|
*/

// ===== مسارات الصفحة الرئيسية =====
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/action-choice', [HomeController::class, 'actionChoice'])->name('action.choice');

// ===== مسارات الصفحات الثابتة =====
Route::get('/about', [HomeController::class, 'about'])->name('about')->middleware('security.headers');
Route::get('/testimonials', [HomeController::class, 'testimonials'])->name('testimonials')->middleware('security.headers');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact')->middleware('security.headers');
Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit')->middleware(['rate.limit:contact', 'csrf.enhanced']);

// ===== مسارات التصنيفات =====
Route::prefix('categories')->name('categories.')->group(function () {
    Route::get('/', [CategoryController::class, 'index'])->name('index');
    Route::get('/search', [CategoryController::class, 'search'])->name('search')->middleware('rate.limit:search');
    Route::get('/{slug}', [CategoryController::class, 'show'])->name('show');
    Route::get('/{slug}/stats', [CategoryController::class, 'stats'])->name('stats');
});

// ===== مسارات الإعلانات العامة (للعرض فقط) =====
Route::prefix('ads')->name('ads.')->group(function () {
    Route::get('/', [AdController::class, 'index'])->name('index');
    // توجيه البحث القديم إلى البحث الموحد
    Route::get('/search', function(Request $request) {
        return redirect()->route('search.index', $request->all());
    })->name('search');
    Route::get('/{categorySlug}', [AdController::class, 'byCategory'])->name('category'); // إعلانات الفئة
    Route::get('/{categorySlug}/{adSlug}', [AdController::class, 'show'])->name('show'); // تفاصيل الإعلان
});

// ===== مسارات البحث الموحد =====
Route::prefix('search')->name('search.')->group(function () {
    Route::get('/', [App\Http\Controllers\SearchController::class, 'index'])->name('index')->middleware('rate.limit:search');
    Route::get('/quick', [App\Http\Controllers\SearchController::class, 'quickSearch'])->name('quick')->middleware('rate.limit:search');
    Route::get('/suggestions', [App\Http\Controllers\SearchController::class, 'suggestions'])->name('suggestions')->middleware('rate.limit:search');
    // توجيه البحث المتقدم القديم إلى البحث الموحد
    Route::get('/advanced', function(Request $request) {
        return redirect()->route('search.index', $request->all());
    })->name('advanced');
    Route::get('/location', [App\Http\Controllers\SearchController::class, 'byLocation'])->name('location');
    Route::post('/save-favorite', [App\Http\Controllers\SearchController::class, 'saveFavorite'])->name('save-favorite')->middleware(['auth', 'rate.limit:general']);
    Route::post('/clear-history', [App\Http\Controllers\SearchController::class, 'clearHistory'])->name('clear-history')->middleware(['auth', 'rate.limit:general']);
    Route::get('/export', [App\Http\Controllers\SearchController::class, 'export'])->name('export')->middleware(['auth', 'rate.limit:general']);
});

// ===== مسار تسجيل الدخول المباشر (حسب الخطة) =====
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login')->middleware('guest');

// ===== مسارات المصادقة =====
Route::prefix('auth')->name('auth.')->group(function () {
    // مسارات الضيوف فقط
    Route::middleware('guest')->group(function () {
        Route::post('/login', [AuthController::class, 'login'])->name('login.submit')->middleware('rate.limit:login');
        Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
        Route::post('/register', [AuthController::class, 'register'])->name('register.submit')->middleware('rate.limit:register');
    });

    // مسارات المستخدمين المسجلين فقط
    Route::middleware('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('/profile', [AuthController::class, 'profile'])->name('profile');
        Route::put('/profile', [AuthController::class, 'updateProfile'])->name('profile.update');
    });
});

// ===== مسارات لوحة التحكم للمستخدم - موحدة ومنظمة =====
Route::prefix('dashboard')->name('dashboard.')->middleware(['auth', 'account.security', 'rate.limit:general'])->group(function () {
    // الصفحة الرئيسية للوحة التحكم - تحتوي على كل شيء
    Route::get('/', [App\Http\Controllers\UserDashboardController::class, 'index'])->name('index');

    // إدارة الإعلانات - جميع العمليات موحدة تحت dashboard
    Route::prefix('ads')->name('ads.')->group(function () {
        Route::get('/', [App\Http\Controllers\UserDashboardController::class, 'myAds'])->name('index'); // عرض جميع الإعلانات
        Route::get('/create', [App\Http\Controllers\UserDashboardController::class, 'createAd'])->name('create');
        Route::post('/', [App\Http\Controllers\UserDashboardController::class, 'storeAd'])->name('store')->middleware('rate.limit:upload');
        Route::get('/{ad}/edit', [App\Http\Controllers\UserDashboardController::class, 'editAd'])->name('edit');
        Route::put('/{ad}', [App\Http\Controllers\UserDashboardController::class, 'updateAd'])->name('update')->middleware('rate.limit:upload');
        Route::delete('/{ad}', [App\Http\Controllers\UserDashboardController::class, 'deleteAd'])->name('delete');
    });

    // الإشعارات - مبسطة
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\UserDashboardController::class, 'notifications'])->name('index');
        Route::get('/{notification}', [App\Http\Controllers\UserDashboardController::class, 'showNotification'])->name('show');
        Route::post('/{notification}/read', [App\Http\Controllers\UserDashboardController::class, 'markNotificationAsRead'])->name('mark-read');
        Route::post('/mark-all-read', [App\Http\Controllers\UserDashboardController::class, 'markAllNotificationsAsRead'])->name('mark-all-read');
        Route::delete('/{notification}', [App\Http\Controllers\UserDashboardController::class, 'deleteNotification'])->name('delete');
    });

    // الملف الشخصي
    Route::get('/profile', [App\Http\Controllers\UserDashboardController::class, 'profile'])->name('profile');
    Route::put('/profile', [App\Http\Controllers\UserDashboardController::class, 'updateProfile'])->name('update-profile');

    // API endpoints للـ AJAX
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('/notifications', [App\Http\Controllers\UserDashboardController::class, 'getRecentNotifications'])->name('notifications');
        Route::get('/stats', [App\Http\Controllers\UserDashboardController::class, 'getQuickStats'])->name('stats');
    });
});

// ===== مسارات التقييمات والمراجعات =====
Route::prefix('reviews')->name('reviews.')->group(function () {
    // عرض التقييمات لإعلان معين
    Route::get('/ad/{ad}', [App\Http\Controllers\ReviewController::class, 'index'])->name('index');
    Route::get('/ad/{ad}/create', [App\Http\Controllers\ReviewController::class, 'create'])->name('create')->middleware('auth');
    Route::post('/ad/{ad}', [App\Http\Controllers\ReviewController::class, 'store'])->name('store')->middleware(['auth', 'rate.limit:general']);
    Route::get('/ad/{ad}/{review}/edit', [App\Http\Controllers\ReviewController::class, 'edit'])->name('edit')->middleware('auth');
    Route::put('/ad/{ad}/{review}', [App\Http\Controllers\ReviewController::class, 'update'])->name('update')->middleware(['auth', 'rate.limit:general']);
    Route::delete('/ad/{ad}/{review}', [App\Http\Controllers\ReviewController::class, 'destroy'])->name('destroy')->middleware(['auth', 'rate.limit:general']);

    // إجراءات التقييمات
    Route::post('/{review}/helpful', [App\Http\Controllers\ReviewController::class, 'markAsHelpful'])->name('helpful')->middleware(['auth', 'rate.limit:general']);
    Route::post('/{review}/report', [App\Http\Controllers\ReviewController::class, 'report'])->name('report')->middleware(['auth', 'rate.limit:general']);

    // API endpoints
    Route::get('/ad/{ad}/api', [App\Http\Controllers\ReviewController::class, 'getReviews'])->name('api.get');
    Route::get('/ad/{ad}/stats', [App\Http\Controllers\ReviewController::class, 'getStats'])->name('api.stats');

    // تقييمات المستخدم
    Route::get('/my-reviews', [App\Http\Controllers\ReviewController::class, 'userReviews'])->name('user-reviews')->middleware('auth');
});

// ===== مسارات اللغة =====
Route::prefix('language')->name('language.')->group(function () {
    Route::get('/{locale}', [LanguageController::class, 'switch'])->name('switch');

    // مسارات للاختبار والتطوير
    Route::get('/debug', [LanguageController::class, 'debug'])->name('debug');
    Route::get('/clear/session', function() {
        Session::forget('locale');
        return redirect()->back()->with('success', 'تم حذف Session بنجاح');
    })->name('clear.session');

    Route::get('/clear/cookie', function() {
        return redirect()->back()
            ->withCookie(Cookie::forget('locale'))
            ->with('success', 'تم حذف Cookie بنجاح');
    })->name('clear.cookie');

    Route::get('/clear/all', function() {
        Session::forget('locale');
        return redirect()->back()
            ->withCookie(Cookie::forget('locale'))
            ->with('success', 'تم حذف Session و Cookie بنجاح');
    })->name('clear.all');
});

// ===== مسارات إدارة الـ Logs (للتطوير فقط) =====
Route::prefix('logs')->name('logs.')->middleware('throttle:10,1')->group(function () {
    // عرض محتوى الـ logs
    Route::get('/view', function() {
        if (!config('app.debug')) {
            abort(404);
        }

        $logFile = storage_path('logs/laravel.log');
        $content = file_exists($logFile) ? file_get_contents($logFile) : 'الملف فارغ أو غير موجود';
        $size = file_exists($logFile) ? filesize($logFile) : 0;

        return response()->view('debug.logs', [
            'content' => $content,
            'size' => $size,
            'formatted_size' => $size > 0 ? number_format($size / 1024, 2) . ' KB' : '0 bytes'
        ]);
    })->name('view');

    // تصفير الـ logs
    Route::post('/clear', function() {
        if (!config('app.debug')) {
            abort(404);
        }

        \Artisan::call('logs:clear');
        return redirect()->back()->with('success', 'تم تصفير ملف الـ logs بنجاح');
    })->name('clear');

    // تحميل الـ logs
    Route::get('/download', function() {
        if (!config('app.debug')) {
            abort(404);
        }

        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile) && filesize($logFile) > 0) {
            return response()->download($logFile, 'laravel_' . date('Y-m-d_H-i-s') . '.log');
        }

        return redirect()->back()->with('error', 'ملف الـ logs فارغ أو غير موجود');
    })->name('download');
});

// ===== مسارات لوحة التحكم الإدارية =====
Route::prefix('admin')->name('admin.')->middleware(['account.security:admin', 'rate.limit:admin'])->group(function () {
    // مسارات الإعدادات العامة
    Route::get('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings');
    Route::get('/settings/{section}', [\App\Http\Controllers\Admin\SettingsController::class, 'section'])->name('settings.section');

    // مسارات إعدادات الإعلانات
    Route::put('/settings/ads', [\App\Http\Controllers\Admin\SettingsController::class, 'updateAdSettings'])->name('settings.ads.update');

    // مسارات إدارة الإعلانات
    Route::resource('announcements', \App\Http\Controllers\Admin\AnnouncementController::class);
    Route::post('announcements/settings', [\App\Http\Controllers\Admin\AnnouncementController::class, 'updateSettings'])->name('announcements.settings');
    Route::patch('announcements/{announcement}/toggle', [\App\Http\Controllers\Admin\AnnouncementController::class, 'toggleStatus'])->name('announcements.toggle');

    // مسارات إدارة إعلانات المستخدمين
    Route::prefix('ads')->name('ads.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\AdController::class, 'index'])->name('index');
        Route::get('/stats', [\App\Http\Controllers\Admin\AdController::class, 'stats'])->name('stats');
        Route::get('/{ad}', [\App\Http\Controllers\Admin\AdController::class, 'show'])->name('show');
        Route::post('/{ad}/approve', [\App\Http\Controllers\Admin\AdController::class, 'approve'])->name('approve');
        Route::post('/{ad}/reject', [\App\Http\Controllers\Admin\AdController::class, 'reject'])->name('reject');
        Route::patch('/{ad}/status', [\App\Http\Controllers\Admin\AdController::class, 'updateStatus'])->name('update-status');
        Route::patch('/{ad}/featured', [\App\Http\Controllers\Admin\AdController::class, 'toggleFeatured'])->name('toggle-featured');
        Route::delete('/{ad}', [\App\Http\Controllers\Admin\AdController::class, 'destroy'])->name('destroy');
        Route::post('/bulk-approve', [\App\Http\Controllers\Admin\AdController::class, 'bulkApprove'])->name('bulk-approve');
        Route::post('/bulk-reject', [\App\Http\Controllers\Admin\AdController::class, 'bulkReject'])->name('bulk-reject');
    });
});

// ===== مسارات آمنة للمصادقة =====
// تم إزالة مسارات التطوير غير الآمنة
// استخدم php artisan admin:create لإنشاء حساب مدير آمن
