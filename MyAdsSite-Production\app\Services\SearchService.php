<?php

namespace App\Services;

use App\Models\Ad;
use App\Models\Category;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * خدمة البحث المتقدمة
 * تدير البحث الذكي والفلاتر المتقدمة واقتراحات البحث
 */
class SearchService
{
    /**
     * البحث المتقدم في الإعلانات
     */
    public static function searchAds(array $params)
    {
        $query = Ad::query()
            ->with(['category', 'user'])
            ->active()
            ->notExpired();

        // البحث النصي
        if (!empty($params['q'])) {
            $searchTerm = trim($params['q']);
            self::logSearchTerm($searchTerm);

            $query->where(function ($q) use ($searchTerm) {
                $q->where('title_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('title_en', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_en', 'LIKE', "%{$searchTerm}%");
            });
        }

        // فلتر التصنيف
        if (!empty($params['category_id'])) {
            $query->where('category_id', $params['category_id']);
        }

        // فلتر الموقع
        if (!empty($params['location'])) {
            $query->where('location', 'LIKE', "%{$params['location']}%");
        }

        // فلتر الإعلانات المميزة
        if (!empty($params['featured']) || !empty($params['featured_only'])) {
            $query->where('is_featured', true);
        }

        // فلتر التاريخ بالنطاق
        if (!empty($params['date_range'])) {
            switch ($params['date_range']) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'week':
                    $query->where('created_at', '>=', now()->subWeek());
                    break;
                case 'month':
                    $query->where('created_at', '>=', now()->subMonth());
                    break;
                case 'year':
                    $query->where('created_at', '>=', now()->subYear());
                    break;
            }
        }

        // فلتر التاريخ المخصص
        if (!empty($params['date_from'])) {
            $query->whereDate('created_at', '>=', $params['date_from']);
        }

        if (!empty($params['date_to'])) {
            $query->whereDate('created_at', '<=', $params['date_to']);
        }

        // فلتر عدد المشاهدات
        if (!empty($params['min_views'])) {
            $query->where('views_count', '>=', $params['min_views']);
        }

        // الترتيب
        $sortBy = $params['sort_by'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';

        switch ($sortBy) {
            case 'views':
                $query->orderBy('views_count', $sortOrder);
                break;
            case 'title':
                $query->orderBy('title_ar', $sortOrder);
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')
                      ->orderBy('created_at', 'desc');
                break;
            default:
                $query->orderBy('created_at', $sortOrder);
        }

        // التصفح
        $perPage = min($params['per_page'] ?? 12, 50); // حد أقصى 50 عنصر

        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * البحث الذكي مع اقتراحات
     */
    public static function smartSearch(string $query, int $limit = 10)
    {
        $cacheKey = "smart_search_" . md5($query) . "_{$limit}";

        return Cache::remember($cacheKey, 300, function () use ($query, $limit) {
            $results = [];

            // البحث في العناوين أولاً
            $titleMatches = Ad::active()
                ->notExpired()
                ->where(function ($q) use ($query) {
                    $q->where('title_ar', 'LIKE', "%{$query}%")
                      ->orWhere('title_en', 'LIKE', "%{$query}%");
                })
                ->with(['category'])
                ->limit($limit)
                ->get();

            foreach ($titleMatches as $ad) {
                $results[] = [
                    'type' => 'ad',
                    'id' => $ad->id,
                    'title' => $ad->title,
                    'category' => $ad->category->name ?? '',
                    'url' => route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]),
                    'relevance' => 'high'
                ];
            }

            // البحث في التصنيفات
            if (count($results) < $limit) {
                $categoryMatches = Category::active()
                    ->where(function ($q) use ($query) {
                        $q->where('name_ar', 'LIKE', "%{$query}%")
                          ->orWhere('name_en', 'LIKE', "%{$query}%");
                    })
                    ->limit($limit - count($results))
                    ->get();

                foreach ($categoryMatches as $category) {
                    $results[] = [
                        'type' => 'category',
                        'id' => $category->id,
                        'title' => $category->name,
                        'url' => route('categories.show', $category->slug),
                        'relevance' => 'medium'
                    ];
                }
            }

            return $results;
        });
    }

    /**
     * الحصول على اقتراحات البحث
     */
    public static function getSearchSuggestions(string $query, int $limit = 5)
    {
        $cacheKey = "search_suggestions_" . md5($query) . "_{$limit}";

        return Cache::remember($cacheKey, 600, function () use ($query, $limit) {
            $suggestions = [];

            // اقتراحات من العناوين
            $titleSuggestions = DB::table('ads')
                ->select('title_ar as suggestion')
                ->where('status', 'active')
                ->where('title_ar', 'LIKE', "%{$query}%")
                ->distinct()
                ->limit($limit)
                ->pluck('suggestion')
                ->toArray();

            $suggestions = array_merge($suggestions, $titleSuggestions);

            // اقتراحات من التصنيفات
            if (count($suggestions) < $limit) {
                $categorySuggestions = DB::table('categories')
                    ->select('name_ar as suggestion')
                    ->where('is_active', true)
                    ->where('name_ar', 'LIKE', "%{$query}%")
                    ->distinct()
                    ->limit($limit - count($suggestions))
                    ->pluck('suggestion')
                    ->toArray();

                $suggestions = array_merge($suggestions, $categorySuggestions);
            }

            return array_slice(array_unique($suggestions), 0, $limit);
        });
    }

    /**
     * الحصول على البحثات الشائعة
     */
    public static function getPopularSearches(int $limit = 10)
    {
        return Cache::remember('popular_searches', 3600, function () use ($limit) {
            return DB::table('search_logs')
                ->select('search_term', DB::raw('COUNT(*) as count'))
                ->where('created_at', '>=', now()->subDays(30))
                ->groupBy('search_term')
                ->orderBy('count', 'desc')
                ->limit($limit)
                ->pluck('search_term')
                ->toArray();
        });
    }

    /**
     * الحصول على البحثات الحديثة للمستخدم
     */
    public static function getRecentSearches(?int $userId = null, int $limit = 5)
    {
        if (!$userId) {
            $userId = auth()->id();
        }

        if (!$userId) {
            return [];
        }

        return Cache::remember("recent_searches_{$userId}", 1800, function () use ($userId, $limit) {
            return DB::table('search_logs')
                ->select('search_term')
                ->where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->distinct()
                ->limit($limit)
                ->pluck('search_term')
                ->toArray();
        });
    }

    /**
     * البحث بالموقع الجغرافي
     */
    public static function searchByLocation(string $location, int $radius = 50)
    {
        // بحث بسيط بالموقع (يمكن تطويره لاحقاً لاستخدام GPS)
        return Ad::active()
            ->notExpired()
            ->where('location', 'LIKE', "%{$location}%")
            ->with(['category', 'user'])
            ->orderBy('created_at', 'desc')
            ->paginate(12);
    }

    /**
     * البحث المتقدم بالفلاتر المتعددة
     */
    public static function advancedFilter(array $filters)
    {
        $query = Ad::query()
            ->with(['category', 'user'])
            ->active()
            ->notExpired();

        // فلتر متعدد التصنيفات
        if (!empty($filters['categories']) && is_array($filters['categories'])) {
            $query->whereIn('category_id', $filters['categories']);
        }

        // فلتر متعدد المواقع
        if (!empty($filters['locations']) && is_array($filters['locations'])) {
            $query->where(function ($q) use ($filters) {
                foreach ($filters['locations'] as $location) {
                    $q->orWhere('location', 'LIKE', "%{$location}%");
                }
            });
        }

        // فلتر نطاق التاريخ
        if (!empty($filters['date_range'])) {
            switch ($filters['date_range']) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'week':
                    $query->where('created_at', '>=', now()->subWeek());
                    break;
                case 'month':
                    $query->where('created_at', '>=', now()->subMonth());
                    break;
                case 'year':
                    $query->where('created_at', '>=', now()->subYear());
                    break;
            }
        }

        // فلتر نطاق المشاهدات
        if (!empty($filters['views_range'])) {
            switch ($filters['views_range']) {
                case 'low':
                    $query->where('views_count', '<', 100);
                    break;
                case 'medium':
                    $query->whereBetween('views_count', [100, 1000]);
                    break;
                case 'high':
                    $query->where('views_count', '>', 1000);
                    break;
            }
        }

        return $query->orderBy('created_at', 'desc')->paginate(12);
    }

    /**
     * تسجيل مصطلح البحث
     */
    private static function logSearchTerm(string $term)
    {
        try {
            DB::table('search_logs')->insert([
                'user_id' => auth()->id(),
                'search_term' => $term,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            Log::warning('Failed to log search term', [
                'term' => $term,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * تصحيح الأخطاء الإملائية البسيط
     */
    public static function correctSpelling(string $query)
    {
        $corrections = [
            'مدرسه' => 'مدرسة',
            'جامعه' => 'جامعة',
            'مستشفى' => 'مستشفى',
            'شركه' => 'شركة',
            'اعلان' => 'إعلان',
            'اعلانات' => 'إعلانات',
        ];

        return str_replace(array_keys($corrections), array_values($corrections), $query);
    }

    /**
     * الحصول على إحصائيات البحث
     */
    public static function getSearchStats()
    {
        return Cache::remember('search_stats', 3600, function () {
            return [
                'total_searches' => DB::table('search_logs')->count(),
                'unique_terms' => DB::table('search_logs')->distinct('search_term')->count(),
                'searches_today' => DB::table('search_logs')->whereDate('created_at', today())->count(),
                'top_terms' => DB::table('search_logs')
                    ->select('search_term', DB::raw('COUNT(*) as count'))
                    ->groupBy('search_term')
                    ->orderBy('count', 'desc')
                    ->limit(10)
                    ->get()
                    ->toArray(),
            ];
        });
    }
}
