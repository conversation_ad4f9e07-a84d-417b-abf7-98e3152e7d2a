{{-- مكون Toast للرسائل المنبثقة --}}
<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
    {{-- Toast للنجاح --}}
    @if(session('success'))
        <div class="toast align-items-center text-white bg-success border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Toast للخطأ --}}
    @if(session('error'))
        <div class="toast align-items-center text-white bg-danger border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ session('error') }}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Toast للتحذير --}}
    @if(session('warning'))
        <div class="toast align-items-center text-dark bg-warning border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ session('warning') }}
                </div>
                <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Toast للمعلومات --}}
    @if(session('info'))
        <div class="toast align-items-center text-white bg-info border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-info-circle me-2"></i>
                    {{ session('info') }}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    @endif
</div>

{{-- CSS مخصص للتوست --}}
<style>
.toast-container {
    z-index: 9999 !important;
}

.toast {
    min-width: 300px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
    font-family: 'Cairo', sans-serif;
    animation: slideInRight 0.3s ease-out;
}

.toast.show {
    opacity: 1;
}

.toast .toast-body {
    padding: 1rem;
    font-size: 0.95rem;
    font-weight: 500;
}

.toast .btn-close {
    padding: 0.5rem;
}

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast.hiding {
    animation: slideOutRight 0.3s ease-in;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 576px) {
    .toast-container {
        left: 1rem;
        right: 1rem;
        top: 1rem;
    }

    .toast {
        min-width: auto;
        width: 100%;
    }
}

/* ألوان مخصصة للتوست */
.toast.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.toast.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%) !important;
}

.toast.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%) !important;
}

.toast.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%) !important;
}

/* تحسين الخط العربي */
.toast .toast-body {
    direction: {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }};
    text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};
}
</style>

{{-- JavaScript محسن ومبسط للتوست --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل جميع التوست بطريقة مبسطة
    document.querySelectorAll('.toast').forEach(function(toastEl) {
        const toast = new bootstrap.Toast(toastEl, {
            autohide: true,
            delay: 3000
        });

        toast.show();

        // إضافة تأثير الاختفاء وإزالة من DOM
        toastEl.addEventListener('hide.bs.toast', () => toastEl.classList.add('hiding'));
        toastEl.addEventListener('hidden.bs.toast', () => {
            setTimeout(() => toastEl.remove(), 300);
        });
    });
});

// دالة مبسطة لإنشاء توست جديد
function showToast(message, type = 'success') {
    const container = document.getElementById('toast-container');
    if (!container) return;

    // تبسيط الكود باستخدام object lookup
    const config = {
        success: { icon: 'fa-check-circle', bg: 'bg-success', text: 'text-white' },
        error: { icon: 'fa-exclamation-circle', bg: 'bg-danger', text: 'text-white' },
        warning: { icon: 'fa-exclamation-triangle', bg: 'bg-warning', text: 'text-dark' },
        info: { icon: 'fa-info-circle', bg: 'bg-info', text: 'text-white' }
    };

    const { icon, bg, text } = config[type] || config.info;
    const closeClass = type === 'warning' ? 'btn-close' : 'btn-close btn-close-white';
    const toastId = 'toast-' + Date.now();

    container.insertAdjacentHTML('beforeend', `
        <div id="${toastId}" class="toast align-items-center ${text} ${bg} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${icon} me-2"></i>${message}
                </div>
                <button type="button" class="${closeClass} me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });

    toast.show();

    // تبسيط event listeners
    toastElement.addEventListener('hide.bs.toast', () => toastElement.classList.add('hiding'));
    toastElement.addEventListener('hidden.bs.toast', () => {
        setTimeout(() => toastElement.remove(), 300);
    });
}
</script>
