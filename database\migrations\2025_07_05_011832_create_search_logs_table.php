<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('search_logs', function (Blueprint $table) {
            $table->id();

            // معرف المستخدم (اختياري)
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            // مصطلح البحث
            $table->string('search_term', 255);

            // معلومات الطلب
            $table->string('ip_address', 45);
            $table->text('user_agent')->nullable();

            // نتائج البحث
            $table->integer('results_count')->default(0);
            $table->json('filters_used')->nullable(); // الفلاتر المستخدمة

            // معلومات إضافية
            $table->string('source', 50)->default('web'); // مصدر البحث (web, mobile, api)
            $table->decimal('response_time', 8, 3)->nullable(); // وقت الاستجابة بالثواني

            $table->timestamps();

            // فهارس لتحسين الأداء
            $table->index(['search_term'], 'search_logs_term_index');
            $table->index(['user_id'], 'search_logs_user_index');
            $table->index(['created_at'], 'search_logs_created_at_index');
            $table->index(['ip_address'], 'search_logs_ip_index');
            $table->index(['search_term', 'created_at'], 'search_logs_term_date_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('search_logs');
    }
};
