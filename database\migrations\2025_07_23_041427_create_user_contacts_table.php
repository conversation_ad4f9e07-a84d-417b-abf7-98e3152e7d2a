<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration لإنشاء جدول معلومات التواصل للمستخدمين
 * يحتوي على جميع طرق التواصل المرتبطة بكل مستخدم
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_contacts', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id();

            // معرف المستخدم - مفتاح خارجي
            $table->unsignedBigInteger('user_id');

            // نوع التواصل - اختياري
            $table->enum('contact_type', [
                'phone', 'whatsapp', 'email', 'telegram', 'instagram',
                'facebook', 'twitter', 'linkedin', 'website', 'other'
            ])->nullable();

            // قيمة التواصل (رقم الهاتف، الإيميل، إلخ) - مطلوبة
            $table->string('contact_value', 255)->nullable();

            // نوع الأيقونة المخصصة
            $table->string('icon_type', 50)->nullable();

            // تسمية مخصصة للعرض
            $table->string('display_label', 100)->nullable();

            // هل هذه الطريقة الأساسية للتواصل
            $table->boolean('is_primary')->default(false);

            // هل تظهر للجميع
            $table->boolean('is_public')->default(true);

            // هل تم التحقق من صحة المعلومة
            $table->boolean('is_verified')->default(false);

            // ترتيب العرض في البطاقة
            $table->integer('display_order')->default(0);

            // مستوى الخصوصية
            $table->enum('privacy_level', ['public', 'registered_users', 'private'])->default('public');

            // تواريخ الإنشاء والتحديث
            $table->timestamps();

            // المفاتيح الخارجية
            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade'); // حذف معلومات التواصل عند حذف المستخدم

            // الفهارس لتحسين الأداء
            $table->index('user_id', 'idx_user_contacts_user_id');
            $table->index('contact_type', 'idx_user_contacts_type');
            $table->index('is_public', 'idx_user_contacts_public');
            $table->index('privacy_level', 'idx_user_contacts_privacy');
            $table->index(['user_id', 'display_order'], 'idx_user_contacts_order');

            // فهرس مركب لمنع التكرار
            $table->unique(['user_id', 'contact_type', 'contact_value'], 'unique_user_contact');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_contacts');
    }
};
