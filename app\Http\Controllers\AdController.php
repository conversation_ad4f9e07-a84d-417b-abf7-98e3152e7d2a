<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Ad;
use App\Models\Category;
use App\Models\User;
use App\Http\Controllers\UserDashboardController;
use App\Services\FileSecurityService;

/**
 * كونترولر الإعلانات
 * يدير عرض الإعلانات وتفاصيلها والبحث فيها
 */
class AdController extends Controller
{
    /**
     * عرض جميع الإعلانات
     */
    public function index(Request $request)
    {
        // بناء الاستعلام الأساسي
        $query = Ad::active()->notExpired()->with('category');

        // تطبيق التصفية حسب التصنيف
        if ($request->has('category') && !empty($request->category)) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // تطبيق التصفية حسب الموقع
        if ($request->has('location') && !empty($request->location)) {
            $query->where('location', 'LIKE', "%{$request->location}%");
        }

        // تطبيق البحث النصي
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('title_en', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_en', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('phone', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('location', 'LIKE', "%{$searchTerm}%");
            });
        }

        // ترتيب النتائج
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'popular':
                $query->orderBy('views_count', 'desc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            default: // latest
                $query->orderBy('created_at', 'desc');
                break;
        }

        // جلب النتائج مع التصفح
        $ads = $query->paginate(12)->withQueryString();

        // جلب التصنيفات للفلتر مع عدد الإعلانات
        $categories = Category::active()
            ->withCount(['ads' => function ($query) {
                $query->active()->notExpired();
            }])
            ->orderBy('name_ar')
            ->get();

        return view('ads.index', compact('ads', 'categories'));
    }

    /**
     * عرض إعلان محدد
     */
    public function show($categorySlug, $adSlug)
    {
        // البحث عن الإعلان مع التصنيف
        $ad = Ad::active()
            ->notExpired()
            ->where('slug', $adSlug)
            ->whereHas('category', function ($query) use ($categorySlug) {
                $query->where('slug', $categorySlug);
            })
            ->with('category')
            ->firstOrFail();

        // زيادة عدد المشاهدات
        $ad->incrementViews();

        // جلب إعلانات مشابهة من نفس التصنيف
        $relatedAds = Ad::active()
            ->notExpired()
            ->where('category_id', $ad->category_id)
            ->where('id', '!=', $ad->id)
            ->orderBy('created_at', 'desc')
            ->limit(4)
            ->get();

        // جلب الإعلان السابق والتالي للتنقل
        $previousAd = Ad::active()
            ->notExpired()
            ->where('category_id', $ad->category_id)
            ->where('id', '<', $ad->id)
            ->orderBy('id', 'desc')
            ->first();

        $nextAd = Ad::active()
            ->notExpired()
            ->where('category_id', $ad->category_id)
            ->where('id', '>', $ad->id)
            ->orderBy('id', 'asc')
            ->first();

        // إذا كان الطلب AJAX، أرجع JSON
        if (request()->ajax() || request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'ad' => [
                    'id' => $ad->id,
                    'title' => $ad->title,
                    'description' => $ad->description,
                    'price' => $ad->price,
                    'phone' => $ad->phone,
                    'location' => $ad->location,
                    'slug' => $ad->slug,
                    'images' => $ad->images ?? []
                ],
                'relatedAds' => $relatedAds->map(function($relatedAd) {
                    return [
                        'id' => $relatedAd->id,
                        'title' => $relatedAd->title,
                        'slug' => $relatedAd->slug,
                        'price' => $relatedAd->price
                    ];
                }),
                'previousAd' => $previousAd ? [
                    'id' => $previousAd->id,
                    'title' => $previousAd->title,
                    'slug' => $previousAd->slug
                ] : null,
                'nextAd' => $nextAd ? [
                    'id' => $nextAd->id,
                    'title' => $nextAd->title,
                    'slug' => $nextAd->slug
                ] : null
            ]);
        }

        return view('ads.show', compact('ad', 'relatedAds', 'previousAd', 'nextAd'));
    }

    /**
     * البحث في الإعلانات
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (empty($query)) {
            return redirect()->route('ads.index');
        }

        // البحث في الإعلانات
        $ads = Ad::active()
            ->notExpired()
            ->where(function ($q) use ($query) {
                $q->where('title_ar', 'LIKE', "%{$query}%")
                  ->orWhere('title_en', 'LIKE', "%{$query}%")
                  ->orWhere('description_ar', 'LIKE', "%{$query}%")
                  ->orWhere('description_en', 'LIKE', "%{$query}%")
                  ->orWhere('phone', 'LIKE', "%{$query}%")
                  ->orWhere('location', 'LIKE', "%{$query}%");
            })
            ->with('category')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('ads.search', compact('ads', 'query'));
    }

    /**
     * عرض الإعلانات حسب التصنيف
     */
    public function byCategory($categorySlug, Request $request)
    {
        // البحث عن التصنيف
        $category = Category::active()
            ->where('slug', $categorySlug)
            ->firstOrFail();

        // جلب إعلانات التصنيف
        $query = $category->ads()->active()->notExpired();

        // تطبيق البحث إذا وجد
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('title_en', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_ar', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description_en', 'LIKE', "%{$searchTerm}%");
            });
        }

        // تطبيق التصفية حسب الموقع
        if ($request->has('location') && !empty($request->location)) {
            $query->where('location', 'LIKE', "%{$request->location}%");
        }

        $ads = $query->orderBy('created_at', 'desc')->paginate(12)->withQueryString();

        return view('ads.category', compact('category', 'ads'));
    }

    /**
     * عرض نموذج إنشاء إعلان جديد
     * تم نقل هذه الوظيفة إلى UserDashboardController::createAd()
     * هذا Method محتفظ به للتوافق مع الروابط القديمة
     */
    public function create()
    {
        // إعادة توجيه إلى dashboard
        return redirect()->route('dashboard.create-ad');
    }

    /**
     * حفظ إعلان جديد
     * تم نقل هذه الوظيفة إلى UserDashboardController::storeAd()
     * هذا Method محتفظ به للتوافق مع النماذج القديمة
     */
    public function store(Request $request)
    {
        // إعادة توجيه إلى dashboard مع البيانات
        return app(UserDashboardController::class)->storeAd($request);
    }

    /**
     * عرض نموذج تعديل الإعلان
     * تم نقل هذه الوظيفة إلى UserDashboardController::editAd()
     */
    public function edit(Ad $ad)
    {
        // إعادة توجيه إلى dashboard
        return redirect()->route('dashboard.edit-ad', $ad);
    }

    /**
     * تحديث الإعلان
     * تم نقل هذه الوظيفة إلى UserDashboardController::updateAd()
     */
    public function update(Request $request, Ad $ad)
    {
        // إعادة توجيه إلى dashboard
        return app(UserDashboardController::class)->updateAd($request, $ad);
    }

    /**
     * حذف الإعلان
     * تم نقل هذه الوظيفة إلى UserDashboardController::deleteAd()
     */
    public function destroy(Ad $ad)
    {
        // إعادة توجيه إلى dashboard
        return app(UserDashboardController::class)->deleteAd($ad);
    }

    /**
     * رفع الصورة وحفظها بشكل آمن
     */
    private function uploadImage($image)
    {
        // فحص أمان الملف
        $validation = FileSecurityService::validateUploadedFile($image);

        if (!$validation['valid']) {
            throw new \Exception(__('File is not secure: :errors', ['errors' => implode(', ', $validation['errors'])]));
        }

        // حفظ الملف بشكل آمن
        $result = FileSecurityService::secureStore($image, 'ads');

        if (!$result['success']) {
            throw new \Exception(__('Failed to save file: :errors', ['errors' => implode(', ', $result['errors'])]));
        }

        return $result['path'];
    }

    /**
     * عرض إعلانات المستخدم
     */
    public function myAds()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $ads = Auth::user()->ads()
            ->with('category')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('ads.my-ads', compact('ads'));
    }

    /**
     * الحصول على معلومات أساسية للإعلان (للـ API)
     */
    public function getBasicInfo(int $adId)
    {
        try {
            $ad = Ad::find($adId);

            if (!$ad) {
                return response()->json([
                    'success' => false,
                    'message' => __('Ad not found')
                ], 404);
            }

            return response()->json([
                'success' => true,
                'id' => $ad->id,
                'title' => $ad->title,
                'price' => $ad->price,
                'image_url' => $ad->image_url,
                'category' => $ad->category->name ?? __('Uncategorized'),
                'url' => route('ads.show', [$ad->category->slug ?? 'general', $ad->slug])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Error fetching ad information')
            ], 500);
        }
    }

    /**
     * كشف رقم الهاتف للإعلان
     */
    public function revealPhone($adId)
    {
        try {
            $ad = Ad::findOrFail($adId);

            // تسجيل العملية
            $ad->logPhoneReveal();

            return response()->json([
                'success' => true,
                'phone' => $ad->phone,
                'message' => 'تم كشف رقم الهاتف بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في كشف رقم الهاتف', [
                'ad_id' => $adId,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'ip' => request()->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في كشف رقم الهاتف'
            ], 500);
        }
    }

    /**
     * كشف البريد الإلكتروني للإعلان
     */
    public function revealEmail($adId)
    {
        try {
            $ad = Ad::findOrFail($adId);

            // تسجيل العملية
            $ad->logEmailReveal();

            return response()->json([
                'success' => true,
                'email' => $ad->email,
                'message' => 'تم كشف البريد الإلكتروني بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في كشف البريد الإلكتروني', [
                'ad_id' => $adId,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'ip' => request()->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في كشف البريد الإلكتروني'
            ], 500);
        }
    }

    /**
     * عرض إعلانات مستخدم معين
     */
    public function byUser($userId)
    {
        try {
            $user = User::findOrFail($userId);

            // تسجيل عرض معلومات المستخدم
            $user->logAdvancedProfileView();

            $ads = $user->activeAds()
                        ->with(['category', 'user'])
                        ->latest()
                        ->paginate(12);

            return view('ads.by-user', [
                'user' => $user,
                'ads' => $ads,
                'stats' => $user->getComprehensiveStats()
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في عرض إعلانات المستخدم', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'viewer_id' => auth()->id(),
                'ip' => request()->ip()
            ]);

            return redirect()->route('ads.index')
                           ->with('error', 'حدث خطأ في عرض إعلانات المستخدم');
        }
    }

    /**
     * جلب إحصائيات الإعلان (API)
     */
    public function getStats(Ad $ad)
    {
        try {
            $stats = [
                'views' => $ad->views_count ?? 0,
                'favorites' => $ad->favorites_count ?? 0,
                'ratings' => $ad->ratings_count ?? 0,
                'comments' => $ad->comments_count ?? 0,
                'contact_reveals' => $ad->contact_reveals_count ?? 0,
                'average_rating' => $ad->average_rating ?? 0,
            ];

            // حساب معدل التفاعل
            $totalInteractions = $stats['favorites'] + $stats['ratings'] + $stats['comments'] + $stats['contact_reveals'];
            $stats['engagement_rate'] = $stats['views'] > 0 ? round(($totalInteractions / $stats['views']) * 100, 1) : 0;
            $stats['total_interactions'] = $totalInteractions;

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في جلب إحصائيات الإعلان', [
                'ad_id' => $ad->id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'ip' => request()->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الإحصائيات'
            ], 500);
        }
    }

    /**
     * تصدير إحصائيات الإعلان (PDF)
     */
    public function exportStats(Ad $ad)
    {
        try {
            // التحقق من الصلاحيات
            if (auth()->id() !== $ad->user_id && !auth()->user()->is_admin) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بتصدير هذه الإحصائيات'
                ], 403);
            }

            // جمع البيانات
            $stats = [
                'ad' => $ad,
                'views' => $ad->views_count ?? 0,
                'favorites' => $ad->favorites_count ?? 0,
                'ratings' => $ad->ratings_count ?? 0,
                'comments' => $ad->comments_count ?? 0,
                'contact_reveals' => $ad->contact_reveals_count ?? 0,
                'average_rating' => $ad->average_rating ?? 0,
                'generated_at' => now(),
            ];

            // حساب الإحصائيات الإضافية
            $totalInteractions = $stats['favorites'] + $stats['ratings'] + $stats['comments'] + $stats['contact_reveals'];
            $stats['engagement_rate'] = $stats['views'] > 0 ? round(($totalInteractions / $stats['views']) * 100, 1) : 0;
            $stats['total_interactions'] = $totalInteractions;

            // إنشاء PDF (مبسط - يمكن تحسينه لاحقاً)
            $html = view('exports.ad-stats', compact('stats'))->render();

            // تسجيل العملية
            Log::info('تصدير إحصائيات الإعلان', [
                'ad_id' => $ad->id,
                'ad_title' => $ad->title,
                'user_id' => auth()->id(),
                'ip' => request()->ip(),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تصدير الإحصائيات بنجاح',
                'download_url' => route('api.ads.stats.export', $ad->id)
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في تصدير إحصائيات الإعلان', [
                'ad_id' => $ad->id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'ip' => request()->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في تصدير الإحصائيات'
            ], 500);
        }
    }

    /**
     * تسجيل مشاهدة الإعلان (API)
     */
    public function recordView(Ad $ad)
    {
        try {
            // زيادة عداد المشاهدات
            $ad->increment('views_count');

            // تسجيل المشاهدة
            Log::info('مشاهدة إعلان', [
                'ad_id' => $ad->id,
                'ad_title' => $ad->title,
                'user_id' => auth()->id(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => true,
                'views_count' => $ad->fresh()->views_count
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في تسجيل مشاهدة الإعلان', [
                'ad_id' => $ad->id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'ip' => request()->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في تسجيل المشاهدة'
            ], 500);
        }
    }
}
