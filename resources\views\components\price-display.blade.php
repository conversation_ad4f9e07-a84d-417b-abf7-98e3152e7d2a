{{-- مكون عرض الأسعار المتقدم - محسن للتفاصيل --}}
@props(['ad', 'compact' => false, 'detailed' => false])

{{-- إضافة Logging لعرض الأسعار --}}
@php
    if ($detailed) {
        $ad->logAdvancedPriceView();
    }
@endphp

<div class="price-section-modern {{ $compact ? 'price-compact' : '' }} {{ $detailed ? 'price-detailed' : '' }}">
    @if($ad->is_free)
        {{-- إعلان مجاني --}}
        <div class="price-badge price-free-modern">
            <i class="fas fa-gift"></i>
            <span>مجاني</span>
        </div>
    @elseif($ad->price_type === 'on_request')
        {{-- السعر عند الطلب --}}
        <div class="price-badge price-on-request-modern">
            <i class="fas fa-phone"></i>
            <span>السعر عند الطلب</span>
        </div>
    @elseif($ad->price)
        <div class="price-display-container">
            @if($ad->hasDiscount())
                {{-- عرض مع خصم - شارات على الحواف --}}
                <div class="price-with-discount-overlay">
                    {{-- السعر الحالي مع الشارات على الحواف --}}
                    <div class="current-price-with-overlay">
                        {{-- شارة الخصم على الحافة اليسرى مع أيقونة --}}
                        <div class="discount-tag-overlay-modern">
                            <i class="fas fa-tag"></i>
                            <span>{{ $ad->calculated_discount_percentage }}%</span>
                        </div>

                        {{-- السعر الأصلي على الحافة اليمنى --}}
                        <div class="original-price-overlay-modern">
                            <span>{!! $ad->getFormattedOriginalPriceWithSmallCurrency() !!}</span>
                        </div>

                        {{-- محتوى السعر الحالي --}}
                        <div class="current-price-content">
                            <span class="price-label-overlay">السعر</span>
                            <span class="price-value-overlay">{!! $ad->getFormattedPriceWithSmallCurrency() !!}</span>
                        </div>
                    </div>

                    {{-- مبلغ التوفير --}}
                    <div class="savings-badge-improved">
                        <i class="fas fa-piggy-bank"></i>
                        <span>وفر {{ number_format($ad->savings_amount, 0) }} <span class="currency-small">{{ $ad->currency === 'YER' ? 'ر.ي' : $ad->currency }}</span></span>
                    </div>
                </div>
            @else
                {{-- سعر عادي - شارة عريضة --}}
                <div class="price-regular-improved">
                    <div class="current-price-full-width">
                        <span class="price-label-full">السعر</span>
                        <span class="price-value-full">{!! $ad->getFormattedPriceWithSmallCurrency() !!}</span>
                    </div>
                </div>
            @endif

            {{-- الشارات الإضافية --}}
            <div class="additional-badges-row" style="display: flex; flex-wrap: wrap; gap: 0.5rem; align-items: center;">
                @if($ad->is_negotiable)
                    <span class="feature-badge negotiable-badge">
                        <i class="fas fa-handshake"></i>
                        <span>قابل للتفاوض</span>
                    </span>
                @endif

                @if($ad->is_limited_offer)
                    <span class="feature-badge limited-offer-badge">
                        <i class="fas fa-clock"></i>
                        <span>عرض محدود</span>
                    </span>
                @endif

                {{-- شارة انتهاء الخصم المحسّنة --}}
                @if($ad->discount_expires_at && !$ad->isDiscountExpired())
                    <span class="feature-badge expires-badge"
                          style="white-space: nowrap; min-width: fit-content; max-width: none;"
                          title="انتهاء الخصم: {{ $ad->discount_expires_at->format('Y-m-d H:i') }}">
                        <i class="fas fa-hourglass-half"></i>
                        <span>ينتهي في {{ $ad->discount_expires_at->diffForHumans() }}</span>
                    </span>
                @endif
            </div>

            {{-- ملاحظات السعر للعرض المفصل --}}
            @if($detailed && $ad->price_notes)
                <div class="price-notes-detailed">
                    <div class="price-notes-header">
                        <i class="fas fa-info-circle text-info"></i>
                        <span>ملاحظات السعر</span>
                    </div>
                    <div class="price-notes-content">
                        {{ $ad->price_notes }}
                    </div>
                </div>
            @endif

            {{-- معلومات العملة للعرض المفصل --}}
            @if($detailed && $ad->currency)
                <div class="currency-info-detailed">
                    <div class="currency-info-item">
                        <i class="fas fa-coins text-warning"></i>
                        <span>العملة: {{ $ad->currency === 'YER' ? 'ريال يمني' : $ad->currency }}</span>
                    </div>
                </div>
            @endif
        </div>
    @endif
</div>

{{-- CSS محسن ونظيف للمكون --}}
@once
@push('styles')
<style>
/* === أساسيات المكون === */
.price-section-modern {
    width: 100%;
}

.price-compact {
    margin: 0.5rem 0;
}

.price-detailed {
    margin: 1rem 0;
    max-width: 100%;
}

/* === شارات الحالات الخاصة === */
.price-free-modern,
.price-on-request-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.price-free-modern {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.price-on-request-modern {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.price-free-modern:hover,
.price-on-request-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* === التصميم الرئيسي مع الشارات على الحواف === */
.price-with-discount-overlay,
.price-regular-improved {
    margin: 0.4rem 0;
}

.price-with-discount-overlay {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

/* الشارة الخضراء الرئيسية */
.current-price-with-overlay,
.current-price-full-width {
    position: relative;
    width: 100%;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-radius: 8px;
    padding: 0.6rem 1rem;
    box-shadow: 0 3px 8px rgba(16, 185, 129, 0.3);
    transition: all 0.3s ease;
    text-align: center;
}

.current-price-with-overlay {
    overflow: visible;
}

.current-price-full-width:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* شارات الحواف - التصميم الحديث */
.discount-tag-overlay-modern {
    position: absolute;
    top: -8px;
    left: -8px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 700;
    box-shadow: 0 3px 8px rgba(239, 68, 68, 0.4);
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.2rem;
}

.discount-tag-overlay-modern i {
    font-size: 0.7rem;
}

.original-price-overlay-modern {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    text-decoration: line-through;
    box-shadow: 0 3px 8px rgba(107, 114, 128, 0.4);
    z-index: 2;
}

.original-price-overlay-modern .currency-small {
    font-size: 0.6rem;
}

/* === محتوى الأسعار === */
.current-price-content,
.current-price-full-width {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.current-price-content {
    position: relative;
    z-index: 1;
}

/* تسميات الأسعار */
.price-label-overlay,
.price-label-full {
    font-size: 0.7rem;
    font-weight: 500;
    opacity: 0.9;
    margin-bottom: 0.3rem;
}

/* قيم الأسعار */
.price-value-overlay,
.price-value-full {
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 1;
}

/* رموز العملة الصغيرة */
.price-value-overlay .currency-small,
.price-value-full .currency-small,
.currency-small {
    font-size: 0.8em;
    opacity: 0.9;
}

/* === شارة التوفير === */
.savings-badge-improved {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 0.35rem 0.7rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    margin: 0.1rem auto 0;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
    width: fit-content;
}

/* === الشارات الإضافية === */
.additional-badges-row {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
    margin-top: 0.3rem;
    justify-content: center;
}

.feature-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.3rem 0.6rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.feature-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.negotiable-badge {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    border: 1px solid rgba(6, 182, 212, 0.3);
}

.limited-offer-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: 1px solid rgba(245, 158, 11, 0.3);
    animation: pulse 2s infinite;
}

/* === حاوي عرض الأسعار === */
.price-display-container {
    background: transparent;
    border-radius: 8px;
    padding: 12px; /* مساحة كافية للعناصر المطلقة الموضع */
    margin: 0;
    position: relative;
    overflow: visible; /* السماح للعناصر بالظهور خارج الحدود */
    /* خلفية شفافة لتجنب التداخل مع شارات الأسعار */
}

/* === التأثيرات والحركات === */
.price-display-container:hover {
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* === الاستجابة للشاشات المختلفة === */
@media (max-width: 768px) {
    .current-price-with-overlay,
    .current-price-full-width {
        padding: 0.5rem 0.8rem;
    }

    .discount-tag-overlay-modern,
    .original-price-overlay-modern {
        font-size: 0.65rem;
        padding: 0.25rem 0.45rem;
        top: -6px;
    }

    .discount-tag-overlay-modern {
        left: -6px;
    }

    .discount-tag-overlay-modern i {
        font-size: 0.6rem;
    }

    .original-price-overlay-modern {
        right: -6px;
    }

    .price-value-overlay,
    .price-value-full {
        font-size: 1.1rem;
    }

    .additional-badges-row {
        gap: 0.25rem;
        margin-top: 0.25rem;
    }

    .feature-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .current-price-with-overlay,
    .current-price-full-width {
        padding: 0.4rem 0.6rem;
    }

    .price-value-overlay,
    .price-value-full {
        font-size: 1rem;
    }

    .price-label-overlay,
    .price-label-full {
        font-size: 0.65rem;
    }

    .savings-badge-improved {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }

    .discount-tag-overlay-modern,
    .original-price-overlay-modern {
        font-size: 0.6rem;
        padding: 0.2rem 0.35rem;
        top: -5px;
    }

    .discount-tag-overlay-modern {
        left: -5px;
    }

    .discount-tag-overlay-modern i {
        font-size: 0.55rem;
    }

    .original-price-overlay-modern {
        right: -5px;
    }
}

/* === تحسينات الوضع المضغوط === */
.price-compact .feature-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.price-compact .savings-badge-improved {
    font-size: 0.75rem;
    padding: 0.3rem 0.5rem;
}

.price-compact .price-display-container {
    padding: 8px; /* مساحة أقل في الوضع المضغوط */
}

/* === أنماط الميزات المتقدمة === */

/* شارة انتهاء الخصم */
.expires-badge {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: 1px solid rgba(239, 68, 68, 0.3);
    animation: pulse 2s infinite;
}

/* ملاحظات السعر المفصلة */
.price-notes-detailed {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    border-right: 4px solid #3b82f6;
}

.price-notes-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.85rem;
    color: #3b82f6;
    margin-bottom: 0.5rem;
}

.price-notes-content {
    font-size: 0.85rem;
    color: #4b5563;
    line-height: 1.5;
    text-align: right;
}

/* معلومات العملة المفصلة */
.currency-info-detailed {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.1);
    border-radius: 6px;
}

.currency-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #92400e;
    font-weight: 500;
}

/* تحسينات للعرض المفصل */
.price-detailed .price-display-container {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.price-detailed .additional-badges-row {
    margin-top: 0.75rem;
    justify-content: flex-start;
}

/* ضمان ظهور العناصر في جميع الحاويات */
.card-body,
.ad-card,
.favorite-card-wrapper {
    overflow: visible !important;
    position: relative;
}
</style>
@endpush
@endonce
