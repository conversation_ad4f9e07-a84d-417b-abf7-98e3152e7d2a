<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration لإنشاء جدول سجلات الوصول لمعلومات التواصل
 * يتتبع جميع محاولات الوصول لمعلومات التواصل للأمان والتحليلات
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_access_logs', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id();

            // معرف المستخدم صاحب معلومات التواصل
            $table->unsignedBigInteger('contact_owner_id');

            // معرف معلومة التواصل المحددة
            $table->unsignedBigInteger('contact_id')->nullable();

            // معرف المستخدم الذي وصل للمعلومات (null للزوار)
            $table->unsignedBigInteger('accessor_user_id')->nullable();

            // نوع الوصول
            $table->enum('access_type', [
                'view',           // مشاهدة معلومات التواصل
                'click',          // النقر على رابط التواصل
                'copy',           // نسخ معلومات التواصل
                'export',         // تصدير معلومات التواصل
                'share',          // مشاركة معلومات التواصل
                'reveal',         // كشف معلومات مخفية
                'download_qr'     // تحميل QR code
            ]);

            // معلومات الجلسة والشبكة
            $table->string('ip_address', 45); // دعم IPv6
            $table->text('user_agent')->nullable();
            $table->string('session_id')->nullable();
            $table->string('referrer_url')->nullable();

            // معلومات الموقع الجغرافي (اختياري)
            $table->string('country_code', 2)->nullable();
            $table->string('city')->nullable();

            // معلومات إضافية
            $table->json('metadata')->nullable(); // بيانات إضافية مرنة
            $table->boolean('is_suspicious')->default(false); // هل النشاط مشبوه
            $table->text('suspicious_reason')->nullable(); // سبب الشك

            // تواريخ الإنشاء والتحديث
            $table->timestamps();

            // المفاتيح الخارجية
            $table->foreign('contact_owner_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');

            $table->foreign('contact_id')
                  ->references('id')
                  ->on('user_contacts')
                  ->onDelete('set null');

            $table->foreign('accessor_user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('set null');

            // الفهارس لتحسين الأداء
            $table->index('contact_owner_id', 'idx_contact_logs_owner');
            $table->index('contact_id', 'idx_contact_logs_contact');
            $table->index('accessor_user_id', 'idx_contact_logs_accessor');
            $table->index('access_type', 'idx_contact_logs_type');
            $table->index('ip_address', 'idx_contact_logs_ip');
            $table->index('is_suspicious', 'idx_contact_logs_suspicious');
            $table->index('created_at', 'idx_contact_logs_created');

            // فهرس مركب للاستعلامات الشائعة
            $table->index(['contact_owner_id', 'created_at'], 'idx_contact_logs_owner_date');
            $table->index(['ip_address', 'created_at'], 'idx_contact_logs_ip_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_access_logs');
    }
};
