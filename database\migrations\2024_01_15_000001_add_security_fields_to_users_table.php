<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // حقول أمنية إضافية
            $table->timestamp('password_changed_at')->nullable()->after('password');
            $table->timestamp('last_login_at')->nullable()->after('password_changed_at');
            $table->string('last_login_ip', 45)->nullable()->after('last_login_at');
            $table->string('registration_ip', 45)->nullable()->after('last_login_ip');
            $table->integer('failed_login_attempts')->default(0)->after('registration_ip');
            $table->timestamp('locked_until')->nullable()->after('failed_login_attempts');
            $table->boolean('force_password_change')->default(false)->after('locked_until');
            $table->json('password_history')->nullable()->after('force_password_change');
            
            // فهارس للأداء
            $table->index('last_login_at');
            $table->index('password_changed_at');
            $table->index('locked_until');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['last_login_at']);
            $table->dropIndex(['password_changed_at']);
            $table->dropIndex(['locked_until']);
            
            $table->dropColumn([
                'password_changed_at',
                'last_login_at',
                'last_login_ip',
                'registration_ip',
                'failed_login_attempts',
                'locked_until',
                'force_password_change',
                'password_history',
            ]);
        });
    }
};
