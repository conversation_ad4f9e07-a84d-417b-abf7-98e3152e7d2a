@extends('layouts.app')

@section('title', __('My Ads'))

@push('styles')
<style>
.arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card-title {
    line-height: 1.4;
}

@if(app()->getLocale() == 'ar')
.btn-group .btn {
    border-radius: 0;
}
.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.badge {
    font-size: 0.75em;
}
@endif
</style>
@endpush

@section('content')
<div class="container py-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <!-- زر العودة -->
            <div class="mb-3">
                <a href="{{ route('dashboard.index') }}" class="btn btn-outline-secondary arabic-text">
                    <i class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'right' : 'left' }} me-2"></i>
                    {{ __('Back to Dashboard') }}
                </a>
            </div>

            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1 arabic-text" data-animation="fadeInLeft">
                        <i class="fas fa-bullhorn me-2"></i>
                        {{ __('My Ads') }}
                    </h2>
                    <p class="text-muted mb-0 arabic-text">{{ __('Manage all your ads from here') }}</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.ads.create') }}" class="btn btn-primary arabic-text">
                        <i class="fas fa-plus me-2"></i>
                        {{ __('Add New Ad') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-bullhorn fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ $stats['total'] ?? 0 }}</h4>
                    <small class="text-muted arabic-text">{{ __('Total Ads') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ $stats['active'] ?? 0 }}</h4>
                    <small class="text-muted arabic-text">{{ __('Active') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ $stats['pending'] ?? 0 }}</h4>
                    <small class="text-muted arabic-text">{{ __('Pending') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-eye fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ number_format($stats['total_views'] ?? 0) }}</h4>
                    <small class="text-muted arabic-text">{{ __('Total Views') }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label arabic-text">{{ __('Status') }}</label>
                            <select name="status" id="status" class="form-select">
                                <option value="all" {{ $status == 'all' ? 'selected' : '' }}>{{ __('All') }}</option>
                                <option value="active" {{ $status == 'active' ? 'selected' : '' }}>{{ __('active') }}</option>
                                <option value="pending" {{ $status == 'pending' ? 'selected' : '' }}>{{ __('pending') }}</option>
                                <option value="rejected" {{ $status == 'rejected' ? 'selected' : '' }}>{{ __('rejected') }}</option>
                                <option value="expired" {{ $status == 'expired' ? 'selected' : '' }}>{{ __('expired') }}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="per_page" class="form-label arabic-text">{{ __('Per Page') }}</label>
                            <select name="per_page" id="per_page" class="form-select">
                                <option value="12" {{ request('per_page', 12) == 12 ? 'selected' : '' }}>12</option>
                                <option value="24" {{ request('per_page') == 24 ? 'selected' : '' }}>24</option>
                                <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2 arabic-text">
                                <i class="fas fa-filter me-1"></i>
                                {{ __('Filter') }}
                            </button>
                            <a href="{{ route('dashboard.ads.index') }}" class="btn btn-outline-secondary arabic-text">
                                <i class="fas fa-times me-1"></i>
                                {{ __('Clear') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الإعلانات -->
    <div class="row">
        @forelse($ads as $ad)
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="row g-0 h-100">
                        @if($ad->image)
                            <div class="col-md-4">
                                <img src="{{ asset('storage/' . $ad->image) }}"
                                     class="img-fluid rounded-start h-100 object-fit-cover"
                                     alt="{{ $ad->title }}"
                                     style="min-height: 200px;">
                            </div>
                        @endif
                        <div class="{{ $ad->image ? 'col-md-8' : 'col-12' }}">
                            <div class="card-body d-flex flex-column h-100">
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="card-title mb-1 arabic-text">{{ $ad->title }}</h5>
                                        <span class="badge bg-{{ $ad->status === 'active' ? 'success' : ($ad->status === 'pending' ? 'warning' : 'danger') }} arabic-text">
                                            {{ __($ad->status) }}
                                        </span>
                                    </div>
                                    <p class="card-text text-muted small mb-2 arabic-text">
                                        <i class="fas fa-tag me-1"></i>
                                        {{ $ad->category->name ?? __('General') }}
                                    </p>
                                    <p class="card-text arabic-text">{{ Str::limit($ad->description, 100) }}</p>
                                    <div class="d-flex justify-content-between text-muted small">
                                        <span class="arabic-text">
                                            <i class="fas fa-eye me-1"></i>
                                            {{ number_format($ad->views_count) }} {{ __('views') }}
                                        </span>
                                        <span class="arabic-text">{{ $ad->created_at->diffForHumans() }}</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="btn-group w-100" role="group">
                                        <a href="{{ route('dashboard.ads.edit', $ad) }}"
                                           class="btn btn-outline-primary btn-sm arabic-text">
                                            <i class="fas fa-edit me-1"></i>
                                            {{ __('Edit') }}
                                        </a>
                                        <a href="{{ route('ads.show', [$ad->category->slug ?? 'general', $ad->slug]) }}"
                                           class="btn btn-outline-info btn-sm arabic-text"
                                           target="_blank">
                                            <i class="fas fa-external-link-alt me-1"></i>
                                            {{ __('View') }}
                                        </a>
                                        <button class="btn btn-outline-danger btn-sm arabic-text"
                                                onclick="deleteAd({{ $ad->id }})">
                                            <i class="fas fa-trash me-1"></i>
                                            {{ __('Delete') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted arabic-text">{{ __('No ads found') }}</h5>
                    <p class="text-muted arabic-text">{{ __('Start by creating your first ad') }}</p>
                    <a href="{{ route('dashboard.ads.create') }}" class="btn btn-primary arabic-text">
                        <i class="fas fa-plus me-2"></i>
                        {{ __('Create New Ad') }}
                    </a>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($ads->hasPages())
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    {{ $ads->appends(request()->query())->links('pagination.custom') }}
                </div>
            </div>
        </div>
    @endif
</div>

<script>
function deleteAd(adId) {
    if (confirm('{{ __("Are you sure you want to delete this ad?") }}')) {
        fetch(`{{ url('dashboard/ads') }}/${adId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{{ __("An error occurred") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ __("An error occurred") }}');
        });
    }
}
</script>
@endsection
