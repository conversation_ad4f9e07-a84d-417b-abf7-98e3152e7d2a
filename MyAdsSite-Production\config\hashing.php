<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Hash Driver - موقع إعلاناتي
    |--------------------------------------------------------------------------
    |
    | تحديد خوارزمية التشفير الافتراضية لكلمات المرور
    | نستخدم argon2id كونها الأقوى والأكثر أماناً
    |
    | المدعومة: "bcrypt", "argon", "argon2id"
    |
    */

    'driver' => env('HASH_DRIVER', 'argon2id'),

    /*
    |--------------------------------------------------------------------------
    | Bcrypt Options
    |--------------------------------------------------------------------------
    |
    | إعدادات خوارزمية Bcrypt (للتوافق مع كلمات المرور القديمة)
    |
    */

    'bcrypt' => [
        'rounds' => env('BCRYPT_ROUNDS', 12),
        'verify' => env('HASH_VERIFY', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Argon Options
    |--------------------------------------------------------------------------
    |
    | إعدادات خوارزمية Argon2 (الأقوى والأكثر أماناً)
    | تم تحسين الإعدادات للحصول على أفضل أمان وأداء
    |
    */

    'argon' => [
        'memory' => env('ARGON_MEMORY', 65536), // 64 MB
        'threads' => env('ARGON_THREADS', 1),
        'time' => env('ARGON_TIME', 4),
        'verify' => env('HASH_VERIFY', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rehash On Login
    |--------------------------------------------------------------------------
    |
    | إعادة تشفير كلمات المرور تلقائياً عند تسجيل الدخول
    | يسمح بالترقية التدريجية من bcrypt إلى argon2id
    |
    */

    'rehash_on_login' => true,

];
