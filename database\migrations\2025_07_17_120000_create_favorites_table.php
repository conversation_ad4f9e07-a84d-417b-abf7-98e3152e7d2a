<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration لإنشاء جدول المفضلة (Favorites/Wishlist)
 * يحتوي على الإعلانات المحفوظة في المفضلة لكل مستخدم
 */
return new class extends Migration
{
    /**
     * تشغيل الـ migration
     */
    public function up(): void
    {
        Schema::create('favorites', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id();
            
            // معرف المستخدم - مفتاح خارجي
            $table->unsignedBigInteger('user_id');
            
            // معرف الإعلان - مفتاح خارجي
            $table->unsignedBigInteger('ad_id');
            
            // تواريخ الإنشاء والتحديث
            $table->timestamps();
            
            // المفاتيح الخارجية
            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade'); // حذف المفضلة عند حذف المستخدم
                  
            $table->foreign('ad_id')
                  ->references('id')
                  ->on('ads')
                  ->onDelete('cascade'); // حذف المفضلة عند حذف الإعلان
            
            // فهرس مركب لمنع التكرار - مستخدم واحد لا يمكنه إضافة نفس الإعلان مرتين
            $table->unique(['user_id', 'ad_id'], 'favorites_user_ad_unique');
            
            // فهارس لتحسين الأداء
            $table->index('user_id', 'favorites_user_id_index');
            $table->index('ad_id', 'favorites_ad_id_index');
            $table->index('created_at', 'favorites_created_at_index');
            
            // فهرس مركب للاستعلامات الشائعة
            $table->index(['user_id', 'created_at'], 'favorites_user_created_index');
        });
    }

    /**
     * التراجع عن الـ migration
     */
    public function down(): void
    {
        Schema::dropIfExists('favorites');
    }
};
