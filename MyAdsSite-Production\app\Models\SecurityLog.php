<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج سجل الأمان
 * يحفظ جميع الأحداث الأمنية في التطبيق
 */
class SecurityLog extends Model
{
    /**
     * الحقول القابلة للتعبئة الجماعية
     */
    protected $fillable = [
        'user_id',          // معرف المستخدم (اختياري)
        'ip_address',       // عنوان IP
        'user_agent',       // معلومات المتصفح
        'action',           // نوع الإجراء
        'description',      // وصف الحدث
        'severity',         // مستوى الخطورة
        'details',          // تفاصيل إضافية (JSON)
        'status',           // حالة الحدث
        'resolved_at',      // تاريخ الحل
        'resolved_by',      // من قام بالحل
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'details' => 'array',
        'resolved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * مستويات الخطورة
     */
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';

    /**
     * حالات الأحداث
     */
    const STATUS_OPEN = 'open';
    const STATUS_INVESTIGATING = 'investigating';
    const STATUS_RESOLVED = 'resolved';
    const STATUS_FALSE_POSITIVE = 'false_positive';

    /**
     * أنواع الإجراءات الأمنية
     */
    const ACTION_LOGIN_ATTEMPT = 'login_attempt';
    const ACTION_LOGIN_SUCCESS = 'login_success';
    const ACTION_LOGIN_FAILED = 'login_failed';
    const ACTION_LOGOUT = 'logout';
    const ACTION_RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded';
    const ACTION_SUSPICIOUS_ACTIVITY = 'suspicious_activity';
    const ACTION_UNAUTHORIZED_ACCESS = 'unauthorized_access';
    const ACTION_DATA_BREACH_ATTEMPT = 'data_breach_attempt';
    const ACTION_SQL_INJECTION_ATTEMPT = 'sql_injection_attempt';
    const ACTION_XSS_ATTEMPT = 'xss_attempt';
    const ACTION_CSRF_ATTEMPT = 'csrf_attempt';
    const ACTION_FILE_UPLOAD_SUSPICIOUS = 'file_upload_suspicious';
    const ACTION_ADMIN_ACCESS = 'admin_access';
    const ACTION_PASSWORD_CHANGE = 'password_change';
    const ACTION_EMAIL_CHANGE = 'email_change';
    const ACTION_PROFILE_UPDATE = 'profile_update';

    /**
     * علاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * علاقة مع المستخدم الذي حل المشكلة
     */
    public function resolver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * البحث في السجلات حسب مستوى الخطورة
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * البحث في السجلات حسب الحالة
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * البحث في السجلات حسب نوع الإجراء
     */
    public function scopeByAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * البحث في السجلات حسب عنوان IP
     */
    public function scopeByIp($query, string $ip)
    {
        return $query->where('ip_address', $ip);
    }

    /**
     * البحث في السجلات غير المحلولة
     */
    public function scopeUnresolved($query)
    {
        return $query->whereIn('status', [self::STATUS_OPEN, self::STATUS_INVESTIGATING]);
    }

    /**
     * البحث في السجلات الحرجة
     */
    public function scopeCritical($query)
    {
        return $query->where('severity', self::SEVERITY_CRITICAL);
    }

    /**
     * البحث في السجلات الحديثة
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * تسجيل حدث أمني جديد
     */
    public static function logEvent(
        string $action,
        string $description,
        string $severity = self::SEVERITY_LOW,
        array $details = [],
        ?int $userId = null
    ): self {
        return self::create([
            'user_id' => $userId ?? auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'action' => $action,
            'description' => $description,
            'severity' => $severity,
            'details' => $details,
            'status' => self::STATUS_OPEN,
        ]);
    }

    /**
     * تسجيل محاولة تسجيل دخول
     */
    public static function logLoginAttempt(string $email, bool $success, array $details = []): self
    {
        $action = $success ? self::ACTION_LOGIN_SUCCESS : self::ACTION_LOGIN_FAILED;
        $severity = $success ? self::SEVERITY_LOW : self::SEVERITY_MEDIUM;
        $description = $success 
            ? "تسجيل دخول ناجح للمستخدم: {$email}"
            : "محاولة تسجيل دخول فاشلة للمستخدم: {$email}";

        return self::logEvent($action, $description, $severity, array_merge([
            'email' => $email,
            'success' => $success,
        ], $details));
    }

    /**
     * تسجيل تجاوز حد الطلبات
     */
    public static function logRateLimitExceeded(string $type, array $details = []): self
    {
        return self::logEvent(
            self::ACTION_RATE_LIMIT_EXCEEDED,
            "تم تجاوز حد الطلبات المسموح من نوع: {$type}",
            self::SEVERITY_MEDIUM,
            array_merge(['type' => $type], $details)
        );
    }

    /**
     * تسجيل نشاط مشبوه
     */
    public static function logSuspiciousActivity(string $description, array $details = []): self
    {
        return self::logEvent(
            self::ACTION_SUSPICIOUS_ACTIVITY,
            $description,
            self::SEVERITY_HIGH,
            $details
        );
    }

    /**
     * حل المشكلة الأمنية
     */
    public function resolve(?int $resolvedBy = null): bool
    {
        return $this->update([
            'status' => self::STATUS_RESOLVED,
            'resolved_at' => now(),
            'resolved_by' => $resolvedBy ?? auth()->id(),
        ]);
    }

    /**
     * تحديد كإنذار كاذب
     */
    public function markAsFalsePositive(?int $resolvedBy = null): bool
    {
        return $this->update([
            'status' => self::STATUS_FALSE_POSITIVE,
            'resolved_at' => now(),
            'resolved_by' => $resolvedBy ?? auth()->id(),
        ]);
    }
}
