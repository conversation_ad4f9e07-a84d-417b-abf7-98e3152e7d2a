<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DatabaseSecurityService;

class DatabaseSecurityAudit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:security-audit 
                            {--fix : تطبيق الإصلاحات التلقائية}
                            {--format=table : تنسيق الإخراج (table|json)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'فحص أمان قاعدة البيانات وتقديم توصيات للتحسين';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 بدء فحص أمان قاعدة البيانات...');
        $this->newLine();

        // تشغيل فحص الأمان
        $results = DatabaseSecurityService::performSecurityAudit();

        // عرض النتائج
        $this->displayResults($results);

        // تطبيق الإصلاحات إذا طُلب ذلك
        if ($this->option('fix')) {
            $this->newLine();
            $this->info('🔧 تطبيق الإصلاحات التلقائية...');
            $fixes = DatabaseSecurityService::applySecurityFixes();
            
            if (!empty($fixes)) {
                $this->info('✅ تم تطبيق الإصلاحات التالية:');
                foreach ($fixes as $fix) {
                    $this->line("  • {$fix}");
                }
            } else {
                $this->warn('⚠️  لا توجد إصلاحات تلقائية متاحة');
            }
        }

        // حساب النتيجة الإجمالية
        $overallScore = $this->calculateOverallScore($results);
        $this->newLine();
        $this->displayOverallScore($overallScore);

        return 0;
    }

    /**
     * عرض النتائج
     */
    private function displayResults(array $results): void
    {
        if ($this->option('format') === 'json') {
            $this->line(json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            return;
        }

        // عرض نتائج أمان الاتصال
        $this->displayConnectionSecurity($results['connection_security']);
        
        // عرض نتائج صلاحيات المستخدم
        $this->displayUserPrivileges($results['user_privileges']);
        
        // عرض نتائج أمان الجداول
        $this->displayTableSecurity($results['table_security']);
        
        // عرض نتائج أمان الاستعلامات
        $this->displayQuerySecurity($results['query_security']);
        
        // عرض نتائج أمان النسخ الاحتياطية
        $this->displayBackupSecurity($results['backup_security']);
        
        // عرض التوصيات
        $this->displayRecommendations($results['recommendations']);
    }

    /**
     * عرض أمان الاتصال
     */
    private function displayConnectionSecurity(array $data): void
    {
        $this->info('🔐 أمان الاتصال');
        $this->line("النتيجة: {$data['score']}/100");
        
        if (!empty($data['issues'])) {
            $this->warn('المشاكل المكتشفة:');
            foreach ($data['issues'] as $issue) {
                $this->line("  ❌ {$issue}");
            }
        } else {
            $this->info('  ✅ لا توجد مشاكل');
        }
        
        $this->newLine();
    }

    /**
     * عرض صلاحيات المستخدم
     */
    private function displayUserPrivileges(array $data): void
    {
        $this->info('👤 صلاحيات المستخدم');
        $this->line("النتيجة: {$data['score']}/100");
        
        if (!empty($data['issues'])) {
            $this->warn('المشاكل المكتشفة:');
            foreach ($data['issues'] as $issue) {
                $this->line("  ❌ {$issue}");
            }
        } else {
            $this->info('  ✅ لا توجد مشاكل');
        }
        
        $this->newLine();
    }

    /**
     * عرض أمان الجداول
     */
    private function displayTableSecurity(array $data): void
    {
        $this->info('🗃️  أمان الجداول');
        $this->line("النتيجة: {$data['score']}/100");
        $this->line("عدد الجداول: {$data['tables_count']}");
        
        if (!empty($data['issues'])) {
            $this->warn('المشاكل المكتشفة:');
            foreach ($data['issues'] as $issue) {
                $this->line("  ❌ {$issue}");
            }
        } else {
            $this->info('  ✅ لا توجد مشاكل');
        }
        
        $this->newLine();
    }

    /**
     * عرض أمان الاستعلامات
     */
    private function displayQuerySecurity(array $data): void
    {
        $this->info('🔍 أمان الاستعلامات');
        $this->line("النتيجة: {$data['score']}/100");
        
        if (!empty($data['issues'])) {
            $this->warn('المشاكل المكتشفة:');
            foreach ($data['issues'] as $issue) {
                $this->line("  ❌ {$issue}");
            }
        } else {
            $this->info('  ✅ لا توجد مشاكل');
        }
        
        $this->newLine();
    }

    /**
     * عرض أمان النسخ الاحتياطية
     */
    private function displayBackupSecurity(array $data): void
    {
        $this->info('💾 أمان النسخ الاحتياطية');
        $this->line("النتيجة: {$data['score']}/100");
        
        if (!empty($data['issues'])) {
            $this->warn('المشاكل المكتشفة:');
            foreach ($data['issues'] as $issue) {
                $this->line("  ❌ {$issue}");
            }
        } else {
            $this->info('  ✅ لا توجد مشاكل');
        }
        
        $this->newLine();
    }

    /**
     * عرض التوصيات
     */
    private function displayRecommendations(array $recommendations): void
    {
        if (empty($recommendations)) {
            $this->info('🎉 ممتاز! لا توجد توصيات إضافية');
            return;
        }

        $this->warn('📋 التوصيات:');
        
        foreach ($recommendations as $recommendation) {
            $priority = $this->getPriorityIcon($recommendation['priority']);
            $this->line("  {$priority} {$recommendation['title']}");
            $this->line("     {$recommendation['description']}");
            $this->newLine();
        }
    }

    /**
     * حساب النتيجة الإجمالية
     */
    private function calculateOverallScore(array $results): int
    {
        $scores = [
            $results['connection_security']['score'],
            $results['user_privileges']['score'],
            $results['table_security']['score'],
            $results['query_security']['score'],
            $results['backup_security']['score'],
        ];

        return (int) round(array_sum($scores) / count($scores));
    }

    /**
     * عرض النتيجة الإجمالية
     */
    private function displayOverallScore(int $score): void
    {
        $this->line('═══════════════════════════════════════');
        $this->info("📊 النتيجة الإجمالية: {$score}/100");
        
        if ($score >= 90) {
            $this->info('🟢 ممتاز - أمان قاعدة البيانات في حالة جيدة جداً');
        } elseif ($score >= 75) {
            $this->warn('🟡 جيد - توجد بعض التحسينات المطلوبة');
        } elseif ($score >= 60) {
            $this->warn('🟠 متوسط - يحتاج تحسينات مهمة');
        } else {
            $this->error('🔴 ضعيف - يحتاج تحسينات عاجلة');
        }
        
        $this->line('═══════════════════════════════════════');
    }

    /**
     * الحصول على أيقونة الأولوية
     */
    private function getPriorityIcon(string $priority): string
    {
        return match ($priority) {
            'critical' => '🚨',
            'high' => '⚠️',
            'medium' => '⚡',
            'low' => 'ℹ️',
            default => '•',
        };
    }
}
