@extends('layouts.app')

@section('title', __('Contact Us') . ' - ' . __('site_name'))
@section('description', __('Get in touch with us for any questions or support'))

@section('content')
<!-- القسم الرئيسي -->
<section class="contact-hero py-5" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); min-height: 40vh;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center text-white">
                <div class="contact-header">
                    <div class="contact-icon mb-4">
                        <i class="fas fa-envelope fa-5x"></i>
                    </div>
                    <h1 class="display-4 fw-bold arabic-text mb-3">
                        {{ __('Contact Us') }}
                    </h1>
                    <p class="lead arabic-text mb-4">
                        {{ __('We are here to help you. Get in touch with us anytime') }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="contact-content py-5">
    <div class="container">
        <div class="row">
            <!-- نموذج التواصل -->
            <div class="col-lg-8 mb-5">
                <div class="contact-form-card">
                    <div class="card-header">
                        <h3 class="arabic-text fw-bold mb-0">
                            <i class="fas fa-paper-plane me-2 text-primary"></i>
                            {{ __('Send us a message') }}
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- رسائل النجاح والأخطاء -->
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <span class="arabic-text">{{ session('success') }}</span>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        @if ($errors->any())
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li class="arabic-text">{{ $error }}</li>
                                    @endforeach
                                </ul>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        <!-- نموذج التواصل -->
                        <form method="POST" action="{{ route('contact.submit') }}" class="contact-form">
                            @csrf

                            <div class="row">
                                <!-- الاسم -->
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label arabic-text fw-bold">
                                        <i class="fas fa-user me-2 text-primary"></i>
                                        {{ __('Full Name') }}
                                    </label>
                                    <input type="text"
                                           class="form-control @error('name') is-invalid @enderror"
                                           id="name"
                                           name="name"
                                           value="{{ old('name') }}"
                                           placeholder="{{ __('Enter your full name') }}"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- البريد الإلكتروني -->
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label arabic-text fw-bold">
                                        <i class="fas fa-envelope me-2 text-primary"></i>
                                        {{ __('Email') }}
                                        <small class="text-muted">({{ __('Optional') }})</small>
                                    </label>
                                    <input type="email"
                                           class="form-control @error('email') is-invalid @enderror"
                                           id="email"
                                           name="email"
                                           value="{{ old('email') }}"
                                           placeholder="{{ __('Enter your email') }}">
                                    @error('email')
                                        <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- الموضوع -->
                                <div class="col-12 mb-3">
                                    <label for="subject" class="form-label arabic-text fw-bold">
                                        <i class="fas fa-tag me-2 text-primary"></i>
                                        {{ __('Subject') }}
                                    </label>
                                    <select class="form-select @error('subject') is-invalid @enderror"
                                            id="subject"
                                            name="subject"
                                            required>
                                        <option value="">{{ __('Select subject') }}</option>
                                        <option value="general" {{ old('subject') == 'general' ? 'selected' : '' }}>{{ __('General Inquiry') }}</option>
                                        <option value="support" {{ old('subject') == 'support' ? 'selected' : '' }}>{{ __('Technical Support') }}</option>
                                        <option value="business" {{ old('subject') == 'business' ? 'selected' : '' }}>{{ __('Business Partnership') }}</option>
                                        <option value="complaint" {{ old('subject') == 'complaint' ? 'selected' : '' }}>{{ __('Complaint') }}</option>
                                        <option value="suggestion" {{ old('subject') == 'suggestion' ? 'selected' : '' }}>{{ __('Suggestion') }}</option>
                                    </select>
                                    @error('subject')
                                        <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- الرسالة -->
                                <div class="col-12 mb-4">
                                    <label for="message" class="form-label arabic-text fw-bold">
                                        <i class="fas fa-comment me-2 text-primary"></i>
                                        {{ __('Message') }}
                                    </label>
                                    <textarea class="form-control @error('message') is-invalid @enderror"
                                              id="message"
                                              name="message"
                                              rows="6"
                                              placeholder="{{ __('Write your message here...') }}"
                                              required>{{ old('message') }}</textarea>
                                    @error('message')
                                        <div class="invalid-feedback arabic-text">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- زر الإرسال -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5 arabic-text fw-bold">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    {{ __('Send Message') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- معلومات التواصل -->
            <div class="col-lg-4">
                <!-- معلومات الاتصال -->
                <div class="contact-info-card mb-4">
                    <div class="card-header">
                        <h4 class="arabic-text fw-bold mb-0">
                            <i class="fas fa-info-circle me-2 text-success"></i>
                            {{ __('Contact Information') }}
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="contact-item mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-envelope fa-2x text-primary"></i>
                            </div>
                            <div class="contact-details">
                                <h6 class="arabic-text fw-bold">{{ __('Email') }}</h6>
                                <p class="text-muted mb-0"><EMAIL></p>
                                <p class="text-muted mb-0"><EMAIL></p>
                            </div>
                        </div>

                        <div class="contact-item mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-phone fa-2x text-success"></i>
                            </div>
                            <div class="contact-details">
                                <h6 class="arabic-text fw-bold">{{ __('Phone') }}</h6>
                                <p class="text-muted mb-0">+967 1 234 567</p>
                                <p class="text-muted mb-0">+967 770 119 544</p>
                            </div>
                        </div>

                        <div class="contact-item mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt fa-2x text-danger"></i>
                            </div>
                            <div class="contact-details">
                                <h6 class="arabic-text fw-bold">{{ __('Address') }}</h6>
                                <p class="text-muted mb-0 arabic-text">{{ __('Sana\'a, Yemen') }}</p>
                                <p class="text-muted mb-0">Sana'a, Yemen</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock fa-2x text-warning"></i>
                            </div>
                            <div class="contact-details">
                                <h6 class="arabic-text fw-bold">{{ __('Working Hours') }}</h6>
                                <p class="text-muted mb-0 arabic-text">{{ __('Saturday - Thursday: 8:00 AM - 6:00 PM') }}</p>
                                <p class="text-muted mb-0">Saturday - Thursday: 8:00 AM - 6:00 PM</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- وسائل التواصل الاجتماعي -->
                <div class="social-media-card">
                    <div class="card-header">
                        <h4 class="arabic-text fw-bold mb-0">
                            <i class="fas fa-share-alt me-2 text-info"></i>
                            {{ __('Follow Us') }}
                        </h4>
                    </div>
                    <div class="card-body">
                        <p class="arabic-text text-muted mb-3">{{ __('Stay connected with us on social media') }}</p>
                        <div class="social-links">
                            <a href="#" class="social-link facebook">
                                <i class="fab fa-facebook-f"></i>
                                <span>Facebook</span>
                            </a>
                            <a href="#" class="social-link twitter">
                                <i class="fab fa-twitter"></i>
                                <span>Twitter</span>
                            </a>
                            <a href="#" class="social-link instagram">
                                <i class="fab fa-instagram"></i>
                                <span>Instagram</span>
                            </a>
                            <a href="#" class="social-link linkedin">
                                <i class="fab fa-linkedin-in"></i>
                                <span>LinkedIn</span>
                            </a>
                            <a href="#" class="social-link whatsapp">
                                <i class="fab fa-whatsapp"></i>
                                <span>WhatsApp</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- أسئلة شائعة -->
<section class="faq-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="arabic-text fw-bold">{{ __('Frequently Asked Questions') }}</h2>
                <p class="text-muted arabic-text">{{ __('Find answers to common questions') }}</p>
            </div>
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <!-- سؤال 1 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button arabic-text" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                {{ __('How can I create an account?') }}
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body arabic-text">
                                {{ __('You can create an account by clicking on "Register" button and filling out the registration form with your details.') }}
                            </div>
                        </div>
                    </div>

                    <!-- سؤال 2 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed arabic-text" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                {{ __('How can I post an ad?') }}
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body arabic-text">
                                {{ __('After creating an account and logging in, you can post ads through your dashboard. Select the appropriate category and fill in the required information.') }}
                            </div>
                        </div>
                    </div>

                    <!-- سؤال 3 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed arabic-text" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                {{ __('Is the service free?') }}
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body arabic-text">
                                {{ __('Yes, our basic services are free. We offer premium features for businesses that need advanced promotion tools.') }}
                            </div>
                        </div>
                    </div>

                    <!-- سؤال 4 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed arabic-text" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                {{ __('How can I contact support?') }}
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body arabic-text">
                                {{ __('You can contact our support team through this contact form, email, or phone. We respond to all inquiries within 24 hours.') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* تنسيق القسم الرئيسي */
.contact-hero {
    position: relative;
    overflow: hidden;
}

.contact-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.contact-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* بطاقة نموذج التواصل */
.contact-form-card,
.contact-info-card,
.social-media-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.contact-form-card .card-header,
.contact-info-card .card-header,
.social-media-card .card-header {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem;
}

.contact-form-card .card-body,
.contact-info-card .card-body,
.social-media-card .card-body {
    padding: 2rem;
}

/* نموذج التواصل */
.contact-form .form-control,
.contact-form .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus,
.contact-form .form-select:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    transform: translateY(-2px);
}

.contact-form .btn-primary {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    border: none;
    border-radius: 50px;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
}

.contact-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
}

/* عناصر معلومات التواصل */
.contact-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    background: rgba(0, 123, 255, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(0, 123, 255, 0.1);
    transform: translateX(5px);
}

.contact-icon {
    margin-right: 1rem;
    flex-shrink: 0;
}

.contact-details h6 {
    margin-bottom: 0.5rem;
    color: #495057;
}

/* روابط وسائل التواصل الاجتماعي */
.social-links {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.social-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    font-weight: 500;
}

.social-link i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.social-link:hover {
    transform: translateX(5px);
    color: white;
}

.social-link.facebook {
    background: linear-gradient(45deg, #3b5998, #4267B2);
}

.social-link.twitter {
    background: linear-gradient(45deg, #1da1f2, #0d8bd9);
}

.social-link.instagram {
    background: linear-gradient(45deg, #e4405f, #fd1d1d);
}

.social-link.linkedin {
    background: linear-gradient(45deg, #0077b5, #005885);
}

.social-link.whatsapp {
    background: linear-gradient(45deg, #25d366, #128c7e);
}

/* الأسئلة الشائعة */
.faq-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 10px !important;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.accordion-button {
    background: white;
    border: none;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    border-radius: 10px !important;
}

.accordion-button:not(.collapsed) {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-body {
    padding: 1.5rem;
    background: white;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .contact-hero {
        min-height: 30vh;
        padding: 3rem 0;
    }

    .contact-form-card .card-body,
    .contact-info-card .card-body,
    .social-media-card .card-body {
        padding: 1.5rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .social-links {
        gap: 0.5rem;
    }

    .social-link {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .contact-hero h1 {
        font-size: 2rem;
    }

    .contact-form-card .card-body,
    .contact-info-card .card-body,
    .social-media-card .card-body {
        padding: 1rem;
    }

    .contact-form .btn-primary {
        width: 100%;
    }
}
</style>
@endpush
